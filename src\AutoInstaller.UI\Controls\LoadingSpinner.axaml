<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d" d:DesignWidth="32" d:DesignHeight="32"
             x:Class="AutoInstaller.UI.Controls.LoadingSpinner">

  <UserControl.Styles>
    <Style Selector="LoadingSpinner">
      <Setter Property="Width" Value="32" />
      <Setter Property="Height" Value="32" />
    </Style>
    
    <Style Selector="LoadingSpinner Ellipse">
      <Setter Property="Fill" Value="{DynamicResource MaterialPrimaryBrush}" />
      <Setter Property="Opacity" Value="0.6" />
    </Style>
    
    <Style Selector="LoadingSpinner Ellipse.dot1">
      <Style.Animations>
        <Animation Duration="0:0:1.2" IterationCount="Infinite">
          <KeyFrame Cue="0%">
            <Setter Property="Opacity" Value="1" />
          </KeyFrame>
          <KeyFrame Cue="50%">
            <Setter Property="Opacity" Value="0.3" />
          </KeyFrame>
          <KeyFrame Cue="100%">
            <Setter Property="Opacity" Value="1" />
          </KeyFrame>
        </Animation>
      </Style.Animations>
    </Style>
    
    <Style Selector="LoadingSpinner Ellipse.dot2">
      <Style.Animations>
        <Animation Duration="0:0:1.2" IterationCount="Infinite" Delay="0:0:0.1">
          <KeyFrame Cue="0%">
            <Setter Property="Opacity" Value="1" />
          </KeyFrame>
          <KeyFrame Cue="50%">
            <Setter Property="Opacity" Value="0.3" />
          </KeyFrame>
          <KeyFrame Cue="100%">
            <Setter Property="Opacity" Value="1" />
          </KeyFrame>
        </Animation>
      </Style.Animations>
    </Style>
    
    <Style Selector="LoadingSpinner Ellipse.dot3">
      <Style.Animations>
        <Animation Duration="0:0:1.2" IterationCount="Infinite" Delay="0:0:0.2">
          <KeyFrame Cue="0%">
            <Setter Property="Opacity" Value="1" />
          </KeyFrame>
          <KeyFrame Cue="50%">
            <Setter Property="Opacity" Value="0.3" />
          </KeyFrame>
          <KeyFrame Cue="100%">
            <Setter Property="Opacity" Value="1" />
          </KeyFrame>
        </Animation>
      </Style.Animations>
    </Style>
    
    <Style Selector="LoadingSpinner Ellipse.dot4">
      <Style.Animations>
        <Animation Duration="0:0:1.2" IterationCount="Infinite" Delay="0:0:0.3">
          <KeyFrame Cue="0%">
            <Setter Property="Opacity" Value="1" />
          </KeyFrame>
          <KeyFrame Cue="50%">
            <Setter Property="Opacity" Value="0.3" />
          </KeyFrame>
          <KeyFrame Cue="100%">
            <Setter Property="Opacity" Value="1" />
          </KeyFrame>
        </Animation>
      </Style.Animations>
    </Style>
    
    <Style Selector="LoadingSpinner Ellipse.dot5">
      <Style.Animations>
        <Animation Duration="0:0:1.2" IterationCount="Infinite" Delay="0:0:0.4">
          <KeyFrame Cue="0%">
            <Setter Property="Opacity" Value="1" />
          </KeyFrame>
          <KeyFrame Cue="50%">
            <Setter Property="Opacity" Value="0.3" />
          </KeyFrame>
          <KeyFrame Cue="100%">
            <Setter Property="Opacity" Value="1" />
          </KeyFrame>
        </Animation>
      </Style.Animations>
    </Style>
    
    <Style Selector="LoadingSpinner Ellipse.dot6">
      <Style.Animations>
        <Animation Duration="0:0:1.2" IterationCount="Infinite" Delay="0:0:0.5">
          <KeyFrame Cue="0%">
            <Setter Property="Opacity" Value="1" />
          </KeyFrame>
          <KeyFrame Cue="50%">
            <Setter Property="Opacity" Value="0.3" />
          </KeyFrame>
          <KeyFrame Cue="100%">
            <Setter Property="Opacity" Value="1" />
          </KeyFrame>
        </Animation>
      </Style.Animations>
    </Style>
    
    <Style Selector="LoadingSpinner Ellipse.dot7">
      <Style.Animations>
        <Animation Duration="0:0:1.2" IterationCount="Infinite" Delay="0:0:0.6">
          <KeyFrame Cue="0%">
            <Setter Property="Opacity" Value="1" />
          </KeyFrame>
          <KeyFrame Cue="50%">
            <Setter Property="Opacity" Value="0.3" />
          </KeyFrame>
          <KeyFrame Cue="100%">
            <Setter Property="Opacity" Value="1" />
          </KeyFrame>
        </Animation>
      </Style.Animations>
    </Style>
    
    <Style Selector="LoadingSpinner Ellipse.dot8">
      <Style.Animations>
        <Animation Duration="0:0:1.2" IterationCount="Infinite" Delay="0:0:0.7">
          <KeyFrame Cue="0%">
            <Setter Property="Opacity" Value="1" />
          </KeyFrame>
          <KeyFrame Cue="50%">
            <Setter Property="Opacity" Value="0.3" />
          </KeyFrame>
          <KeyFrame Cue="100%">
            <Setter Property="Opacity" Value="1" />
          </KeyFrame>
        </Animation>
      </Style.Animations>
    </Style>
  </UserControl.Styles>

  <!-- Spinner circular com 8 pontos -->
  <Canvas Width="32" Height="32">
    
    <!-- Ponto 1 - Topo -->
    <Ellipse Classes="dot1" Width="3" Height="3" 
             Canvas.Left="14.5" Canvas.Top="2" />
    
    <!-- Ponto 2 - Topo direita -->
    <Ellipse Classes="dot2" Width="3" Height="3" 
             Canvas.Left="22" Canvas.Top="5" />
    
    <!-- Ponto 3 - Direita -->
    <Ellipse Classes="dot3" Width="3" Height="3" 
             Canvas.Left="27" Canvas.Top="14.5" />
    
    <!-- Ponto 4 - Baixo direita -->
    <Ellipse Classes="dot4" Width="3" Height="3" 
             Canvas.Left="22" Canvas.Top="24" />
    
    <!-- Ponto 5 - Baixo -->
    <Ellipse Classes="dot5" Width="3" Height="3" 
             Canvas.Left="14.5" Canvas.Top="27" />
    
    <!-- Ponto 6 - Baixo esquerda -->
    <Ellipse Classes="dot6" Width="3" Height="3" 
             Canvas.Left="7" Canvas.Top="24" />
    
    <!-- Ponto 7 - Esquerda -->
    <Ellipse Classes="dot7" Width="3" Height="3" 
             Canvas.Left="2" Canvas.Top="14.5" />
    
    <!-- Ponto 8 - Topo esquerda -->
    <Ellipse Classes="dot8" Width="3" Height="3" 
             Canvas.Left="7" Canvas.Top="5" />
    
  </Canvas>

</UserControl>
