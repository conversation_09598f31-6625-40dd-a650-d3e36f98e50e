{"format": 1, "restore": {"C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\tests\\AutoInstaller.Infrastructure.Tests\\AutoInstaller.Infrastructure.Tests.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\src\\AutoInstaller.Application\\AutoInstaller.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\src\\AutoInstaller.Application\\AutoInstaller.Application.csproj", "projectName": "AutoInstaller.Application", "projectPath": "C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\src\\AutoInstaller.Application\\AutoInstaller.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\src\\AutoInstaller.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\src\\AutoInstaller.Core\\AutoInstaller.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\src\\AutoInstaller.Core\\AutoInstaller.Core.csproj"}, "C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\src\\AutoInstaller.Shared\\AutoInstaller.Shared.csproj": {"projectPath": "C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\src\\AutoInstaller.Shared\\AutoInstaller.Shared.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true, "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.1, )"}, "FluentValidation": {"target": "Package", "version": "[11.10.0, )"}, "FluentValidation.DependencyInjectionExtensions": {"target": "Package", "version": "[11.10.0, )"}, "MediatR": {"target": "Package", "version": "[11.1.0, )"}, "MediatR.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[11.1.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\src\\AutoInstaller.Core\\AutoInstaller.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\src\\AutoInstaller.Core\\AutoInstaller.Core.csproj", "projectName": "AutoInstaller.Core", "projectPath": "C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\src\\AutoInstaller.Core\\AutoInstaller.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\src\\AutoInstaller.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"allWarningsAsErrors": true, "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"FluentValidation": {"target": "Package", "version": "[11.10.0, )"}, "MediatR": {"target": "Package", "version": "[11.1.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\src\\AutoInstaller.Infrastructure\\AutoInstaller.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\src\\AutoInstaller.Infrastructure\\AutoInstaller.Infrastructure.csproj", "projectName": "AutoInstaller.Infrastructure", "projectPath": "C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\src\\AutoInstaller.Infrastructure\\AutoInstaller.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\src\\AutoInstaller.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\src\\AutoInstaller.Application\\AutoInstaller.Application.csproj": {"projectPath": "C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\src\\AutoInstaller.Application\\AutoInstaller.Application.csproj"}, "C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\src\\AutoInstaller.Core\\AutoInstaller.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\src\\AutoInstaller.Core\\AutoInstaller.Core.csproj"}, "C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\src\\AutoInstaller.Shared\\AutoInstaller.Shared.csproj": {"projectPath": "C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\src\\AutoInstaller.Shared\\AutoInstaller.Shared.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true, "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Docker.DotNet": {"target": "Package", "version": "[3.125.15, )"}, "Docker.DotNet.X509": {"target": "Package", "version": "[3.125.15, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Caching.Memory": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Diagnostics.HealthChecks": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Options": {"target": "Package", "version": "[9.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Polly": {"target": "Package", "version": "[8.4.2, )"}, "Polly.Extensions.Http": {"target": "Package", "version": "[3.0.0, )"}, "Serilog": {"target": "Package", "version": "[4.1.0, )"}, "Serilog.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Settings.Configuration": {"target": "Package", "version": "[8.0.4, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[6.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[6.0.0, )"}, "System.Text.Json": {"target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\src\\AutoInstaller.Shared\\AutoInstaller.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\src\\AutoInstaller.Shared\\AutoInstaller.Shared.csproj", "projectName": "AutoInstaller.Shared", "projectPath": "C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\src\\AutoInstaller.Shared\\AutoInstaller.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\src\\AutoInstaller.Shared\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"allWarningsAsErrors": true, "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"FluentValidation": {"target": "Package", "version": "[11.10.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.0, )"}, "System.Text.Json": {"target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\tests\\AutoInstaller.Infrastructure.Tests\\AutoInstaller.Infrastructure.Tests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\tests\\AutoInstaller.Infrastructure.Tests\\AutoInstaller.Infrastructure.Tests.csproj", "projectName": "AutoInstaller.Infrastructure.Tests", "projectPath": "C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\tests\\AutoInstaller.Infrastructure.Tests\\AutoInstaller.Infrastructure.Tests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\tests\\AutoInstaller.Infrastructure.Tests\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\src\\AutoInstaller.Application\\AutoInstaller.Application.csproj": {"projectPath": "C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\src\\AutoInstaller.Application\\AutoInstaller.Application.csproj"}, "C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\src\\AutoInstaller.Core\\AutoInstaller.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\src\\AutoInstaller.Core\\AutoInstaller.Core.csproj"}, "C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\src\\AutoInstaller.Infrastructure\\AutoInstaller.Infrastructure.csproj": {"projectPath": "C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\src\\AutoInstaller.Infrastructure\\AutoInstaller.Infrastructure.csproj"}, "C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\src\\AutoInstaller.Shared\\AutoInstaller.Shared.csproj": {"projectPath": "C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\src\\AutoInstaller.Shared\\AutoInstaller.Shared.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true, "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AutoFixture": {"target": "Package", "version": "[4.18.1, )"}, "AutoFixture.Xunit2": {"target": "Package", "version": "[4.18.1, )"}, "FluentAssertions": {"target": "Package", "version": "[6.12.2, )"}, "Microsoft.EntityFrameworkCore.InMemory": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.12.0, )"}, "Moq": {"target": "Package", "version": "[4.20.72, )"}, "Testcontainers": {"target": "Package", "version": "[4.0.0, )"}, "Testcontainers.PostgreSql": {"target": "Package", "version": "[4.0.0, )"}, "coverlet.collector": {"target": "Package", "version": "[6.0.2, )"}, "coverlet.msbuild": {"target": "Package", "version": "[6.0.2, )"}, "xunit": {"target": "Package", "version": "[2.9.2, )"}, "xunit.runner.visualstudio": {"target": "Package", "version": "[2.8.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}