<?xml version="1.0"?>
<doc>
    <assembly>
        <name>AutoInstaller.Core</name>
    </assembly>
    <members>
        <member name="T:AutoInstaller.Core.Common.BaseEntity">
            <summary>
            Classe base para todas as entidades do domínio
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Common.BaseEntity.Id">
            <summary>
            Identificador único da entidade
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Common.BaseEntity.IsDeleted">
            <summary>
            Indica se a entidade foi excluída (soft delete)
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Common.BaseEntity.CreatedAt">
            <summary>
            Data de criação da entidade
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Common.BaseEntity.UpdatedAt">
            <summary>
            Data da última atualização
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Common.BaseEntity.CreatedBy">
            <summary>
            Usuário que criou a entidade
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Common.BaseEntity.UpdatedBy">
            <summary>
            Usuário que fez a última atualização
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Common.BaseEntity.DomainEvents">
            <summary>
            Eventos de domínio da entidade
            </summary>
        </member>
        <member name="M:AutoInstaller.Core.Common.BaseEntity.#ctor">
            <summary>
            Construtor protegido para EF Core
            </summary>
        </member>
        <member name="M:AutoInstaller.Core.Common.BaseEntity.#ctor(System.String)">
            <summary>
            Construtor com usuário criador
            </summary>
            <param name="createdBy">Usuário que criou a entidade</param>
        </member>
        <member name="M:AutoInstaller.Core.Common.BaseEntity.AddDomainEvent(AutoInstaller.Core.Interfaces.IDomainEvent)">
            <summary>
            Adiciona um evento de domínio
            </summary>
            <param name="domainEvent">Evento a ser adicionado</param>
        </member>
        <member name="M:AutoInstaller.Core.Common.BaseEntity.RemoveDomainEvent(AutoInstaller.Core.Interfaces.IDomainEvent)">
            <summary>
            Remove um evento de domínio
            </summary>
            <param name="domainEvent">Evento a ser removido</param>
        </member>
        <member name="M:AutoInstaller.Core.Common.BaseEntity.ClearDomainEvents">
            <summary>
            Limpa todos os eventos de domínio
            </summary>
        </member>
        <member name="M:AutoInstaller.Core.Common.BaseEntity.MarkAsUpdated(System.String)">
            <summary>
            Marca a entidade como atualizada
            </summary>
            <param name="updatedBy">Usuário que fez a atualização</param>
        </member>
        <member name="M:AutoInstaller.Core.Common.BaseEntity.MarkAsDeleted(System.String)">
            <summary>
            Marca a entidade como excluída (soft delete)
            </summary>
            <param name="deletedBy">Usuário que excluiu a entidade</param>
        </member>
        <member name="M:AutoInstaller.Core.Common.BaseEntity.Restore(System.String)">
            <summary>
            Restaura uma entidade excluída
            </summary>
            <param name="restoredBy">Usuário que restaurou a entidade</param>
        </member>
        <member name="M:AutoInstaller.Core.Common.BaseEntity.Equals(System.Object)">
            <summary>
            Verifica se duas entidades são iguais baseado no Id
            </summary>
        </member>
        <member name="M:AutoInstaller.Core.Common.BaseEntity.GetHashCode">
            <summary>
            Retorna o hash code baseado no Id
            </summary>
        </member>
        <member name="M:AutoInstaller.Core.Common.BaseEntity.op_Equality(AutoInstaller.Core.Common.BaseEntity,AutoInstaller.Core.Common.BaseEntity)">
            <summary>
            Operador de igualdade
            </summary>
        </member>
        <member name="M:AutoInstaller.Core.Common.BaseEntity.op_Inequality(AutoInstaller.Core.Common.BaseEntity,AutoInstaller.Core.Common.BaseEntity)">
            <summary>
            Operador de desigualdade
            </summary>
        </member>
        <member name="T:AutoInstaller.Core.Common.ValueObject">
            <summary>
            Classe base abstrata para Value Objects
            </summary>
        </member>
        <member name="M:AutoInstaller.Core.Common.ValueObject.GetEqualityComponents">
            <summary>
            Retorna os componentes que definem a igualdade do Value Object
            </summary>
            <returns>Componentes para comparação</returns>
        </member>
        <member name="M:AutoInstaller.Core.Common.ValueObject.Equals(System.Object)">
            <summary>
            Verifica se dois Value Objects são iguais
            </summary>
            <param name="obj">Objeto a ser comparado</param>
            <returns>True se forem iguais, false caso contrário</returns>
        </member>
        <member name="M:AutoInstaller.Core.Common.ValueObject.GetHashCode">
            <summary>
            Retorna o hash code do Value Object
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:AutoInstaller.Core.Common.ValueObject.op_Equality(AutoInstaller.Core.Common.ValueObject,AutoInstaller.Core.Common.ValueObject)">
            <summary>
            Operador de igualdade
            </summary>
            <param name="left">Value Object da esquerda</param>
            <param name="right">Value Object da direita</param>
            <returns>True se forem iguais</returns>
        </member>
        <member name="M:AutoInstaller.Core.Common.ValueObject.op_Inequality(AutoInstaller.Core.Common.ValueObject,AutoInstaller.Core.Common.ValueObject)">
            <summary>
            Operador de desigualdade
            </summary>
            <param name="left">Value Object da esquerda</param>
            <param name="right">Value Object da direita</param>
            <returns>True se forem diferentes</returns>
        </member>
        <member name="M:AutoInstaller.Core.Common.ValueObject.Copy``1">
            <summary>
            Cria uma cópia do Value Object
            </summary>
            <returns>Nova instância com os mesmos valores</returns>
        </member>
        <member name="T:AutoInstaller.Core.Entities.ContainerStatus">
            <summary>
            Status do container
            </summary>
        </member>
        <member name="T:AutoInstaller.Core.Entities.ContainerRuntime">
            <summary>
            Tipo de runtime do container
            </summary>
        </member>
        <member name="T:AutoInstaller.Core.Entities.Container">
            <summary>
            Entidade Container - Aggregate Root
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Entities.Container.Name">
            <summary>
            Nome do container
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Entities.Container.ImageName">
            <summary>
            Nome da imagem
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Entities.Container.ImageTag">
            <summary>
            Tag da imagem
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Entities.Container.Status">
            <summary>
            Status atual do container
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Entities.Container.Runtime">
            <summary>
            Runtime utilizado (Docker)
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Entities.Container.RuntimeId">
            <summary>
            ID do container no runtime
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Entities.Container.Command">
            <summary>
            Comando executado no container
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Entities.Container.Arguments">
            <summary>
            Argumentos do comando
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Entities.Container.WorkingDirectory">
            <summary>
            Diretório de trabalho
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Entities.Container.User">
            <summary>
            Usuário que executa o container
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Entities.Container.AutoRestart">
            <summary>
            Indica se o container deve reiniciar automaticamente
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Entities.Container.RestartPolicy">
            <summary>
            Política de reinicialização
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Entities.Container.MemoryLimit">
            <summary>
            Limite de memória em bytes
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Entities.Container.CpuLimit">
            <summary>
            Limite de CPU (número de cores)
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Entities.Container.StartedAt">
            <summary>
            Data de início do container
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Entities.Container.StoppedAt">
            <summary>
            Data de parada do container
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Entities.Container.ExposedPorts">
            <summary>
            Portas expostas pelo container
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Entities.Container.EnvironmentVariables">
            <summary>
            Variáveis de ambiente
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Entities.Container.Labels">
            <summary>
            Labels do container
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Entities.Container.Volumes">
            <summary>
            Volumes montados
            </summary>
        </member>
        <member name="M:AutoInstaller.Core.Entities.Container.#ctor">
            <summary>
            Construtor protegido para EF Core
            </summary>
        </member>
        <member name="M:AutoInstaller.Core.Entities.Container.#ctor(AutoInstaller.Core.ValueObjects.ContainerName,System.String,AutoInstaller.Core.ValueObjects.ImageTag,AutoInstaller.Core.Entities.ContainerRuntime,System.String)">
            <summary>
            Construtor principal
            </summary>
            <param name="name">Nome do container</param>
            <param name="imageName">Nome da imagem</param>
            <param name="imageTag">Tag da imagem</param>
            <param name="runtime">Runtime do container</param>
            <param name="createdBy">Usuário que criou</param>
        </member>
        <member name="M:AutoInstaller.Core.Entities.Container.UpdateStatus(AutoInstaller.Core.Entities.ContainerStatus,System.String)">
            <summary>
            Atualiza o status do container
            </summary>
            <param name="newStatus">Novo status</param>
            <param name="updatedBy">Usuário que atualizou</param>
        </member>
        <member name="M:AutoInstaller.Core.Entities.Container.SetRuntimeId(System.String,System.String)">
            <summary>
            Define o ID do runtime
            </summary>
            <param name="runtimeId">ID do container no runtime</param>
            <param name="updatedBy">Usuário que atualizou</param>
        </member>
        <member name="M:AutoInstaller.Core.Entities.Container.SetCommand(System.String,System.String[],System.String)">
            <summary>
            Configura o comando do container
            </summary>
            <param name="command">Comando principal</param>
            <param name="arguments">Argumentos do comando</param>
            <param name="updatedBy">Usuário que atualizou</param>
        </member>
        <member name="M:AutoInstaller.Core.Entities.Container.SetExecutionSettings(System.String,System.String,System.String)">
            <summary>
            Define configurações de execução
            </summary>
            <param name="workingDirectory">Diretório de trabalho</param>
            <param name="user">Usuário de execução</param>
            <param name="updatedBy">Usuário que atualizou</param>
        </member>
        <member name="M:AutoInstaller.Core.Entities.Container.SetRestartPolicy(System.Boolean,System.String,System.String)">
            <summary>
            Configura política de reinicialização
            </summary>
            <param name="autoRestart">Se deve reiniciar automaticamente</param>
            <param name="restartPolicy">Política de reinicialização</param>
            <param name="updatedBy">Usuário que atualizou</param>
        </member>
        <member name="M:AutoInstaller.Core.Entities.Container.SetResourceLimits(System.Nullable{System.Int64},System.Nullable{System.Double},System.String)">
            <summary>
            Define limites de recursos
            </summary>
            <param name="memoryLimit">Limite de memória em bytes</param>
            <param name="cpuLimit">Limite de CPU</param>
            <param name="updatedBy">Usuário que atualizou</param>
        </member>
        <member name="M:AutoInstaller.Core.Entities.Container.AddExposedPort(AutoInstaller.Core.ValueObjects.Port,System.String)">
            <summary>
            Adiciona uma porta exposta
            </summary>
            <param name="port">Porta a ser exposta</param>
            <param name="updatedBy">Usuário que atualizou</param>
        </member>
        <member name="M:AutoInstaller.Core.Entities.Container.RemoveExposedPort(AutoInstaller.Core.ValueObjects.Port,System.String)">
            <summary>
            Remove uma porta exposta
            </summary>
            <param name="port">Porta a ser removida</param>
            <param name="updatedBy">Usuário que atualizou</param>
        </member>
        <member name="M:AutoInstaller.Core.Entities.Container.AddEnvironmentVariable(System.String,System.String,System.String)">
            <summary>
            Adiciona uma variável de ambiente
            </summary>
            <param name="key">Chave da variável</param>
            <param name="value">Valor da variável</param>
            <param name="updatedBy">Usuário que atualizou</param>
        </member>
        <member name="M:AutoInstaller.Core.Entities.Container.RemoveEnvironmentVariable(System.String,System.String)">
            <summary>
            Remove uma variável de ambiente
            </summary>
            <param name="key">Chave da variável</param>
            <param name="updatedBy">Usuário que atualizou</param>
        </member>
        <member name="M:AutoInstaller.Core.Entities.Container.AddLabel(System.String,System.String,System.String)">
            <summary>
            Adiciona um label
            </summary>
            <param name="key">Chave do label</param>
            <param name="value">Valor do label</param>
            <param name="updatedBy">Usuário que atualizou</param>
        </member>
        <member name="M:AutoInstaller.Core.Entities.Container.RemoveLabel(System.String,System.String)">
            <summary>
            Remove um label
            </summary>
            <param name="key">Chave do label</param>
            <param name="updatedBy">Usuário que atualizou</param>
        </member>
        <member name="M:AutoInstaller.Core.Entities.Container.AddVolume(System.String,System.String)">
            <summary>
            Adiciona um volume
            </summary>
            <param name="volume">Definição do volume</param>
            <param name="updatedBy">Usuário que atualizou</param>
        </member>
        <member name="M:AutoInstaller.Core.Entities.Container.RemoveVolume(System.String,System.String)">
            <summary>
            Remove um volume
            </summary>
            <param name="volume">Definição do volume</param>
            <param name="updatedBy">Usuário que atualizou</param>
        </member>
        <member name="M:AutoInstaller.Core.Entities.Container.CanStart">
            <summary>
            Verifica se o container pode ser iniciado
            </summary>
            <returns>True se pode ser iniciado</returns>
        </member>
        <member name="M:AutoInstaller.Core.Entities.Container.CanStop">
            <summary>
            Verifica se o container pode ser parado
            </summary>
            <returns>True se pode ser parado</returns>
        </member>
        <member name="M:AutoInstaller.Core.Entities.Container.CanPause">
            <summary>
            Verifica se o container pode ser pausado
            </summary>
            <returns>True se pode ser pausado</returns>
        </member>
        <member name="M:AutoInstaller.Core.Entities.Container.CanUnpause">
            <summary>
            Verifica se o container pode ser despausado
            </summary>
            <returns>True se pode ser despausado</returns>
        </member>
        <member name="M:AutoInstaller.Core.Entities.Container.CanRemove">
            <summary>
            Verifica se o container pode ser removido
            </summary>
            <returns>True se pode ser removido</returns>
        </member>
        <member name="T:AutoInstaller.Core.Events.ContainerCreatedEvent">
            <summary>
            Evento disparado quando um container é criado
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Events.ContainerCreatedEvent.ContainerId">
            <summary>
            ID do container criado
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Events.ContainerCreatedEvent.ContainerName">
            <summary>
            Nome do container
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Events.ContainerCreatedEvent.ImageName">
            <summary>
            Nome da imagem
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Events.ContainerCreatedEvent.ImageTag">
            <summary>
            Tag da imagem
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Events.ContainerCreatedEvent.Runtime">
            <summary>
            Runtime utilizado
            </summary>
        </member>
        <member name="M:AutoInstaller.Core.Events.ContainerCreatedEvent.#ctor(System.Guid,AutoInstaller.Core.ValueObjects.ContainerName,System.String,AutoInstaller.Core.ValueObjects.ImageTag,AutoInstaller.Core.Entities.ContainerRuntime)">
            <summary>
            Construtor
            </summary>
            <param name="containerId">ID do container</param>
            <param name="containerName">Nome do container</param>
            <param name="imageName">Nome da imagem</param>
            <param name="imageTag">Tag da imagem</param>
            <param name="runtime">Runtime utilizado</param>
        </member>
        <member name="T:AutoInstaller.Core.Events.ContainerStatusChangedEvent">
            <summary>
            Evento disparado quando o status de um container muda
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Events.ContainerStatusChangedEvent.ContainerId">
            <summary>
            ID do container
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Events.ContainerStatusChangedEvent.PreviousStatus">
            <summary>
            Status anterior
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Events.ContainerStatusChangedEvent.NewStatus">
            <summary>
            Novo status
            </summary>
        </member>
        <member name="M:AutoInstaller.Core.Events.ContainerStatusChangedEvent.#ctor(System.Guid,AutoInstaller.Core.Entities.ContainerStatus,AutoInstaller.Core.Entities.ContainerStatus)">
            <summary>
            Construtor
            </summary>
            <param name="containerId">ID do container</param>
            <param name="previousStatus">Status anterior</param>
            <param name="newStatus">Novo status</param>
        </member>
        <member name="T:AutoInstaller.Core.Events.ContainerStartedEvent">
            <summary>
            Evento disparado quando um container é iniciado
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Events.ContainerStartedEvent.ContainerId">
            <summary>
            ID do container
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Events.ContainerStartedEvent.RuntimeId">
            <summary>
            ID do runtime
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Events.ContainerStartedEvent.StartedAt">
            <summary>
            Data de início
            </summary>
        </member>
        <member name="M:AutoInstaller.Core.Events.ContainerStartedEvent.#ctor(System.Guid,System.String,System.DateTime)">
            <summary>
            Construtor
            </summary>
            <param name="containerId">ID do container</param>
            <param name="runtimeId">ID do runtime</param>
            <param name="startedAt">Data de início</param>
        </member>
        <member name="T:AutoInstaller.Core.Events.ContainerStoppedEvent">
            <summary>
            Evento disparado quando um container é parado
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Events.ContainerStoppedEvent.ContainerId">
            <summary>
            ID do container
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Events.ContainerStoppedEvent.StoppedAt">
            <summary>
            Data de parada
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Events.ContainerStoppedEvent.ExitCode">
            <summary>
            Código de saída
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Events.ContainerStoppedEvent.Reason">
            <summary>
            Motivo da parada
            </summary>
        </member>
        <member name="M:AutoInstaller.Core.Events.ContainerStoppedEvent.#ctor(System.Guid,System.DateTime,System.Nullable{System.Int32},System.String)">
            <summary>
            Construtor
            </summary>
            <param name="containerId">ID do container</param>
            <param name="stoppedAt">Data de parada</param>
            <param name="exitCode">Código de saída</param>
            <param name="reason">Motivo da parada</param>
        </member>
        <member name="T:AutoInstaller.Core.Events.ContainerRemovedEvent">
            <summary>
            Evento disparado quando um container é removido
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Events.ContainerRemovedEvent.ContainerId">
            <summary>
            ID do container
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Events.ContainerRemovedEvent.ContainerName">
            <summary>
            Nome do container
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Events.ContainerRemovedEvent.ForceRemoved">
            <summary>
            Se foi forçada a remoção
            </summary>
        </member>
        <member name="M:AutoInstaller.Core.Events.ContainerRemovedEvent.#ctor(System.Guid,AutoInstaller.Core.ValueObjects.ContainerName,System.Boolean)">
            <summary>
            Construtor
            </summary>
            <param name="containerId">ID do container</param>
            <param name="containerName">Nome do container</param>
            <param name="forceRemoved">Se foi forçada a remoção</param>
        </member>
        <member name="T:AutoInstaller.Core.Events.ContainerResourcesUpdatedEvent">
            <summary>
            Evento disparado quando recursos de um container são atualizados
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Events.ContainerResourcesUpdatedEvent.ContainerId">
            <summary>
            ID do container
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Events.ContainerResourcesUpdatedEvent.PreviousMemoryLimit">
            <summary>
            Limite de memória anterior
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Events.ContainerResourcesUpdatedEvent.NewMemoryLimit">
            <summary>
            Novo limite de memória
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Events.ContainerResourcesUpdatedEvent.PreviousCpuLimit">
            <summary>
            Limite de CPU anterior
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Events.ContainerResourcesUpdatedEvent.NewCpuLimit">
            <summary>
            Novo limite de CPU
            </summary>
        </member>
        <member name="M:AutoInstaller.Core.Events.ContainerResourcesUpdatedEvent.#ctor(System.Guid,System.Nullable{System.Int64},System.Nullable{System.Int64},System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Construtor
            </summary>
            <param name="containerId">ID do container</param>
            <param name="previousMemoryLimit">Limite de memória anterior</param>
            <param name="newMemoryLimit">Novo limite de memória</param>
            <param name="previousCpuLimit">Limite de CPU anterior</param>
            <param name="newCpuLimit">Novo limite de CPU</param>
        </member>
        <member name="T:AutoInstaller.Core.Events.ContainerPortsUpdatedEvent">
            <summary>
            Evento disparado quando portas de um container são modificadas
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Events.ContainerPortsUpdatedEvent.ContainerId">
            <summary>
            ID do container
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Events.ContainerPortsUpdatedEvent.AddedPorts">
            <summary>
            Portas adicionadas
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Events.ContainerPortsUpdatedEvent.RemovedPorts">
            <summary>
            Portas removidas
            </summary>
        </member>
        <member name="M:AutoInstaller.Core.Events.ContainerPortsUpdatedEvent.#ctor(System.Guid,System.Collections.Generic.IReadOnlyList{AutoInstaller.Core.ValueObjects.Port},System.Collections.Generic.IReadOnlyList{AutoInstaller.Core.ValueObjects.Port})">
            <summary>
            Construtor
            </summary>
            <param name="containerId">ID do container</param>
            <param name="addedPorts">Portas adicionadas</param>
            <param name="removedPorts">Portas removidas</param>
        </member>
        <member name="T:AutoInstaller.Core.Exceptions.DomainException">
            <summary>
            Exceção base para violações de regras de domínio
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Exceptions.DomainException.ErrorCode">
            <summary>
            Código do erro
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Exceptions.DomainException.Details">
            <summary>
            Detalhes adicionais do erro
            </summary>
        </member>
        <member name="M:AutoInstaller.Core.Exceptions.DomainException.#ctor(System.String)">
            <summary>
            Construtor básico
            </summary>
            <param name="message">Mensagem do erro</param>
        </member>
        <member name="M:AutoInstaller.Core.Exceptions.DomainException.#ctor(System.String,System.String)">
            <summary>
            Construtor com código de erro
            </summary>
            <param name="message">Mensagem do erro</param>
            <param name="errorCode">Código do erro</param>
        </member>
        <member name="M:AutoInstaller.Core.Exceptions.DomainException.#ctor(System.String,System.Exception)">
            <summary>
            Construtor com exceção interna
            </summary>
            <param name="message">Mensagem do erro</param>
            <param name="innerException">Exceção interna</param>
        </member>
        <member name="M:AutoInstaller.Core.Exceptions.DomainException.#ctor(System.String,System.String,System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Construtor completo
            </summary>
            <param name="message">Mensagem do erro</param>
            <param name="errorCode">Código do erro</param>
            <param name="details">Detalhes adicionais</param>
        </member>
        <member name="M:AutoInstaller.Core.Exceptions.DomainException.#ctor(System.String,System.String,System.Collections.Generic.Dictionary{System.String,System.Object},System.Exception)">
            <summary>
            Construtor completo com exceção interna
            </summary>
            <param name="message">Mensagem do erro</param>
            <param name="errorCode">Código do erro</param>
            <param name="details">Detalhes adicionais</param>
            <param name="innerException">Exceção interna</param>
        </member>
        <member name="M:AutoInstaller.Core.Exceptions.DomainException.AddDetail(System.String,System.Object)">
            <summary>
            Adiciona um detalhe ao erro
            </summary>
            <param name="key">Chave do detalhe</param>
            <param name="value">Valor do detalhe</param>
        </member>
        <member name="M:AutoInstaller.Core.Exceptions.DomainException.ToString">
            <summary>
            Retorna uma representação string da exceção
            </summary>
            <returns>String representando a exceção</returns>
        </member>
        <member name="T:AutoInstaller.Core.Exceptions.BusinessRuleException">
            <summary>
            Exceção para violações de regras de negócio
            </summary>
        </member>
        <member name="T:AutoInstaller.Core.Exceptions.EntityNotFoundException">
            <summary>
            Exceção para entidades não encontradas
            </summary>
        </member>
        <member name="T:AutoInstaller.Core.Exceptions.EntityConflictException">
            <summary>
            Exceção para conflitos de entidades
            </summary>
        </member>
        <member name="T:AutoInstaller.Core.Exceptions.ValueObjectValidationException">
            <summary>
            Exceção para validações de Value Objects
            </summary>
        </member>
        <member name="T:AutoInstaller.Core.Interfaces.IAggregateRoot">
            <summary>
            Interface que marca uma entidade como raiz de agregado
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.IAggregateRoot.Id">
            <summary>
            Identificador único do agregado
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.IAggregateRoot.DomainEvents">
            <summary>
            Eventos de domínio do agregado
            </summary>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IAggregateRoot.ClearDomainEvents">
            <summary>
            Limpa todos os eventos de domínio
            </summary>
        </member>
        <member name="T:AutoInstaller.Core.Interfaces.IContainerRepository">
            <summary>
            Interface do repositório de containers
            </summary>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IContainerRepository.GetByNameAsync(AutoInstaller.Core.ValueObjects.ContainerName,System.Threading.CancellationToken)">
            <summary>
            Obtém um container pelo nome
            </summary>
            <param name="name">Nome do container</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>Container ou null se não encontrado</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IContainerRepository.GetByRuntimeIdAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Obtém um container pelo ID do runtime
            </summary>
            <param name="runtimeId">ID do container no runtime</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>Container ou null se não encontrado</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IContainerRepository.GetByStatusAsync(AutoInstaller.Core.Entities.ContainerStatus,System.Threading.CancellationToken)">
            <summary>
            Obtém containers por status
            </summary>
            <param name="status">Status dos containers</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>Lista de containers com o status especificado</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IContainerRepository.GetByRuntimeAsync(AutoInstaller.Core.Entities.ContainerRuntime,System.Threading.CancellationToken)">
            <summary>
            Obtém containers por runtime
            </summary>
            <param name="runtime">Runtime dos containers</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>Lista de containers do runtime especificado</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IContainerRepository.GetByImageAsync(System.String,AutoInstaller.Core.ValueObjects.ImageTag,System.Threading.CancellationToken)">
            <summary>
            Obtém containers por imagem
            </summary>
            <param name="imageName">Nome da imagem</param>
            <param name="imageTag">Tag da imagem (opcional)</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>Lista de containers da imagem especificada</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IContainerRepository.GetByExposedPortAsync(AutoInstaller.Core.ValueObjects.Port,System.Threading.CancellationToken)">
            <summary>
            Obtém containers que expõem uma porta específica
            </summary>
            <param name="port">Porta a ser verificada</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>Lista de containers que expõem a porta</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IContainerRepository.GetByEnvironmentVariableAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Obtém containers com uma variável de ambiente específica
            </summary>
            <param name="key">Chave da variável de ambiente</param>
            <param name="value">Valor da variável (opcional)</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>Lista de containers com a variável de ambiente</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IContainerRepository.GetByLabelAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Obtém containers com um label específico
            </summary>
            <param name="key">Chave do label</param>
            <param name="value">Valor do label (opcional)</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>Lista de containers com o label</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IContainerRepository.GetRunningContainersAsync(System.Threading.CancellationToken)">
            <summary>
            Obtém containers em execução
            </summary>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>Lista de containers em execução</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IContainerRepository.GetStoppedContainersAsync(System.Threading.CancellationToken)">
            <summary>
            Obtém containers parados
            </summary>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>Lista de containers parados</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IContainerRepository.GetByCreatedByAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Obtém containers criados por um usuário específico
            </summary>
            <param name="createdBy">Usuário que criou os containers</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>Lista de containers criados pelo usuário</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IContainerRepository.GetByCreatedDateRangeAsync(System.DateTime,System.DateTime,System.Threading.CancellationToken)">
            <summary>
            Obtém containers criados em um período específico
            </summary>
            <param name="startDate">Data de início</param>
            <param name="endDate">Data de fim</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>Lista de containers criados no período</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IContainerRepository.ExistsByNameAsync(AutoInstaller.Core.ValueObjects.ContainerName,System.Nullable{System.Guid},System.Threading.CancellationToken)">
            <summary>
            Verifica se existe um container com o nome especificado
            </summary>
            <param name="name">Nome do container</param>
            <param name="excludeId">ID do container a ser excluído da verificação (opcional)</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>True se existe um container com o nome</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IContainerRepository.ExistsByRuntimeIdAsync(System.String,System.Nullable{System.Guid},System.Threading.CancellationToken)">
            <summary>
            Verifica se existe um container com o ID do runtime especificado
            </summary>
            <param name="runtimeId">ID do runtime</param>
            <param name="excludeId">ID do container a ser excluído da verificação (opcional)</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>True se existe um container com o ID do runtime</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IContainerRepository.CountByStatusAsync(AutoInstaller.Core.Entities.ContainerStatus,System.Threading.CancellationToken)">
            <summary>
            Conta containers por status
            </summary>
            <param name="status">Status dos containers</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>Número de containers com o status</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IContainerRepository.CountByRuntimeAsync(AutoInstaller.Core.Entities.ContainerRuntime,System.Threading.CancellationToken)">
            <summary>
            Conta containers por runtime
            </summary>
            <param name="runtime">Runtime dos containers</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>Número de containers do runtime</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IContainerRepository.GetStatisticsAsync(System.Threading.CancellationToken)">
            <summary>
            Obtém estatísticas dos containers
            </summary>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>Estatísticas dos containers</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IContainerRepository.SearchAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Busca containers por texto (nome, imagem, labels, etc.)
            </summary>
            <param name="searchText">Texto de busca</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>Lista de containers que correspondem à busca</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IContainerRepository.SearchPagedAsync(System.String,System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Busca containers paginada por texto
            </summary>
            <param name="searchText">Texto de busca</param>
            <param name="pageNumber">Número da página</param>
            <param name="pageSize">Tamanho da página</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>Resultado paginado da busca</returns>
        </member>
        <member name="T:AutoInstaller.Core.Interfaces.ContainerStatistics">
            <summary>
            Estatísticas dos containers
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.ContainerStatistics.TotalContainers">
            <summary>
            Total de containers
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.ContainerStatistics.RunningContainers">
            <summary>
            Containers em execução
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.ContainerStatistics.StoppedContainers">
            <summary>
            Containers parados
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.ContainerStatistics.PausedContainers">
            <summary>
            Containers pausados
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.ContainerStatistics.ErrorContainers">
            <summary>
            Containers com erro
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.ContainerStatistics.DockerContainers">
            <summary>
            Containers Docker
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.ContainerStatistics.PodmanContainers">
            <summary>
            Containers Podman
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.ContainerStatistics.TotalMemoryUsage">
            <summary>
            Uso total de memória (em bytes)
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.ContainerStatistics.TotalCpuUsage">
            <summary>
            Uso total de CPU
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.ContainerStatistics.LastUpdated">
            <summary>
            Data da última atualização das estatísticas
            </summary>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.ContainerStatistics.#ctor">
            <summary>
            Construtor
            </summary>
        </member>
        <member name="T:AutoInstaller.Core.Interfaces.IContainerService">
            <summary>
            Interface para serviços de containerização
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.IContainerService.SupportedRuntime">
            <summary>
            Runtime suportado pelo serviço
            </summary>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IContainerService.IsAvailableAsync(System.Threading.CancellationToken)">
            <summary>
            Verifica se o runtime está disponível
            </summary>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>True se disponível</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IContainerService.GetRuntimeInfoAsync(System.Threading.CancellationToken)">
            <summary>
            Obtém informações do runtime
            </summary>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>Informações do runtime</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IContainerService.ListContainersAsync(System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Lista todos os containers
            </summary>
            <param name="includeAll">Incluir containers parados</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>Lista de containers</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IContainerService.GetContainerAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Obtém informações de um container específico
            </summary>
            <param name="containerId">ID do container</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>Informações do container</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IContainerService.CreateContainerAsync(AutoInstaller.Core.Interfaces.CreateContainerRequest,System.Threading.CancellationToken)">
            <summary>
            Cria um novo container
            </summary>
            <param name="request">Requisição de criação</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>ID do container criado</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IContainerService.StartContainerAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Inicia um container
            </summary>
            <param name="containerId">ID do container</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>True se iniciado com sucesso</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IContainerService.StopContainerAsync(System.String,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Para um container
            </summary>
            <param name="containerId">ID do container</param>
            <param name="timeoutSeconds">Timeout em segundos</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>True se parado com sucesso</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IContainerService.RestartContainerAsync(System.String,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Reinicia um container
            </summary>
            <param name="containerId">ID do container</param>
            <param name="timeoutSeconds">Timeout em segundos</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>True se reiniciado com sucesso</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IContainerService.PauseContainerAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Pausa um container
            </summary>
            <param name="containerId">ID do container</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>True se pausado com sucesso</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IContainerService.UnpauseContainerAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Despausa um container
            </summary>
            <param name="containerId">ID do container</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>True se despausado com sucesso</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IContainerService.RemoveContainerAsync(System.String,System.Boolean,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Remove um container
            </summary>
            <param name="containerId">ID do container</param>
            <param name="force">Forçar remoção</param>
            <param name="removeVolumes">Remover volumes associados</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>True se removido com sucesso</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IContainerService.GetContainerLogsAsync(System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime},System.Threading.CancellationToken)">
            <summary>
            Obtém logs de um container
            </summary>
            <param name="containerId">ID do container</param>
            <param name="tail">Número de linhas do final</param>
            <param name="since">Data de início</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>Logs do container</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IContainerService.GetContainerStatsAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Obtém estatísticas de um container
            </summary>
            <param name="containerId">ID do container</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>Estatísticas do container</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IContainerService.ListImagesAsync(System.Threading.CancellationToken)">
            <summary>
            Lista imagens disponíveis
            </summary>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>Lista de imagens</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IContainerService.PullImageAsync(System.String,System.String,System.IProgress{AutoInstaller.Core.Interfaces.PullProgress},System.Threading.CancellationToken)">
            <summary>
            Puxa uma imagem do registry
            </summary>
            <param name="imageName">Nome da imagem</param>
            <param name="tag">Tag da imagem</param>
            <param name="progress">Callback de progresso</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>True se puxada com sucesso</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IContainerService.RemoveImageAsync(System.String,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Remove uma imagem
            </summary>
            <param name="imageId">ID da imagem</param>
            <param name="force">Forçar remoção</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>True se removida com sucesso</returns>
        </member>
        <member name="T:AutoInstaller.Core.Interfaces.RuntimeInfo">
            <summary>
            Informações do runtime
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.RuntimeInfo.Name">
            <summary>
            Nome do runtime
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.RuntimeInfo.Version">
            <summary>
            Versão do runtime
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.RuntimeInfo.ApiVersion">
            <summary>
            Versão da API
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.RuntimeInfo.OperatingSystem">
            <summary>
            Sistema operacional
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.RuntimeInfo.Architecture">
            <summary>
            Arquitetura
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.RuntimeInfo.CpuCount">
            <summary>
            Número de CPUs
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.RuntimeInfo.TotalMemory">
            <summary>
            Memória total em bytes
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.RuntimeInfo.IsRunning">
            <summary>
            Indica se está executando
            </summary>
        </member>
        <member name="T:AutoInstaller.Core.Interfaces.ContainerInfo">
            <summary>
            Informações de um container
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.ContainerInfo.Id">
            <summary>
            ID do container
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.ContainerInfo.Name">
            <summary>
            Nome do container
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.ContainerInfo.Image">
            <summary>
            Imagem do container
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.ContainerInfo.Status">
            <summary>
            Status do container
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.ContainerInfo.State">
            <summary>
            Estado do container
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.ContainerInfo.Created">
            <summary>
            Data de criação
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.ContainerInfo.StartedAt">
            <summary>
            Data de início
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.ContainerInfo.FinishedAt">
            <summary>
            Data de parada
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.ContainerInfo.Ports">
            <summary>
            Portas expostas
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.ContainerInfo.Labels">
            <summary>
            Labels do container
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.ContainerInfo.Mounts">
            <summary>
            Mounts/Volumes
            </summary>
        </member>
        <member name="T:AutoInstaller.Core.Interfaces.CreateContainerRequest">
            <summary>
            Requisição para criação de container
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.CreateContainerRequest.Name">
            <summary>
            Nome do container
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.CreateContainerRequest.Image">
            <summary>
            Imagem do container
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.CreateContainerRequest.Tag">
            <summary>
            Tag da imagem
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.CreateContainerRequest.Command">
            <summary>
            Comando a ser executado
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.CreateContainerRequest.Arguments">
            <summary>
            Argumentos do comando
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.CreateContainerRequest.Environment">
            <summary>
            Variáveis de ambiente
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.CreateContainerRequest.Ports">
            <summary>
            Portas a serem expostas
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.CreateContainerRequest.Volumes">
            <summary>
            Volumes a serem montados
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.CreateContainerRequest.Labels">
            <summary>
            Labels do container
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.CreateContainerRequest.WorkingDirectory">
            <summary>
            Diretório de trabalho
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.CreateContainerRequest.User">
            <summary>
            Usuário de execução
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.CreateContainerRequest.RestartPolicy">
            <summary>
            Política de reinicialização
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.CreateContainerRequest.MemoryLimit">
            <summary>
            Limite de memória em bytes
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.CreateContainerRequest.CpuLimit">
            <summary>
            Limite de CPU
            </summary>
        </member>
        <member name="T:AutoInstaller.Core.Interfaces.ContainerStats">
            <summary>
            Estatísticas de um container
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.ContainerStats.ContainerId">
            <summary>
            ID do container
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.ContainerStats.CpuUsagePercent">
            <summary>
            Uso de CPU em percentual
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.ContainerStats.MemoryUsage">
            <summary>
            Uso de memória em bytes
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.ContainerStats.MemoryLimit">
            <summary>
            Limite de memória em bytes
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.ContainerStats.MemoryUsagePercent">
            <summary>
            Uso de memória em percentual
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.ContainerStats.NetworkRxBytes">
            <summary>
            Bytes recebidos pela rede
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.ContainerStats.NetworkTxBytes">
            <summary>
            Bytes transmitidos pela rede
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.ContainerStats.BlockReadBytes">
            <summary>
            Bytes lidos do disco
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.ContainerStats.BlockWriteBytes">
            <summary>
            Bytes escritos no disco
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.ContainerStats.Timestamp">
            <summary>
            Timestamp da coleta
            </summary>
        </member>
        <member name="T:AutoInstaller.Core.Interfaces.ImageInfo">
            <summary>
            Informações de uma imagem
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.ImageInfo.Id">
            <summary>
            ID da imagem
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.ImageInfo.RepoTags">
            <summary>
            Tags da imagem
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.ImageInfo.Size">
            <summary>
            Tamanho da imagem em bytes
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.ImageInfo.Created">
            <summary>
            Data de criação
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.ImageInfo.Labels">
            <summary>
            Labels da imagem
            </summary>
        </member>
        <member name="T:AutoInstaller.Core.Interfaces.PullProgress">
            <summary>
            Progresso de pull de imagem
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.PullProgress.Status">
            <summary>
            Status atual
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.PullProgress.Id">
            <summary>
            ID da camada
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.PullProgress.Current">
            <summary>
            Progresso atual
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.PullProgress.Total">
            <summary>
            Total de bytes
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.PullProgress.ProgressPercent">
            <summary>
            Percentual de progresso
            </summary>
        </member>
        <member name="T:AutoInstaller.Core.Interfaces.IDomainEvent">
            <summary>
            Interface base para eventos de domínio
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.IDomainEvent.EventId">
            <summary>
            Identificador único do evento
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.IDomainEvent.OccurredOn">
            <summary>
            Data e hora em que o evento ocorreu
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.IDomainEvent.EventType">
            <summary>
            Tipo do evento
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.IDomainEvent.Version">
            <summary>
            Versão do evento (para versionamento de eventos)
            </summary>
        </member>
        <member name="T:AutoInstaller.Core.Interfaces.DomainEvent">
            <summary>
            Classe base abstrata para eventos de domínio
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.DomainEvent.EventId">
            <summary>
            Identificador único do evento
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.DomainEvent.OccurredOn">
            <summary>
            Data e hora em que o evento ocorreu
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.DomainEvent.EventType">
            <summary>
            Tipo do evento
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.DomainEvent.Version">
            <summary>
            Versão do evento
            </summary>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.DomainEvent.#ctor">
            <summary>
            Construtor protegido
            </summary>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.DomainEvent.#ctor(System.Int32)">
            <summary>
            Construtor com versão específica
            </summary>
            <param name="version">Versão do evento</param>
        </member>
        <member name="T:AutoInstaller.Core.Interfaces.IRepository`1">
            <summary>
            Interface base para repositórios
            </summary>
            <typeparam name="TEntity">Tipo da entidade</typeparam>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IRepository`1.GetByIdAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Obtém uma entidade por ID
            </summary>
            <param name="id">ID da entidade</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>Entidade ou null se não encontrada</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IRepository`1.GetAllAsync(System.Threading.CancellationToken)">
            <summary>
            Obtém todas as entidades
            </summary>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>Lista de entidades</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IRepository`1.GetAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Threading.CancellationToken)">
            <summary>
            Obtém entidades com base em um filtro
            </summary>
            <param name="predicate">Filtro</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>Lista de entidades filtradas</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IRepository`1.GetFirstOrDefaultAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Threading.CancellationToken)">
            <summary>
            Obtém a primeira entidade que atende ao filtro
            </summary>
            <param name="predicate">Filtro</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>Primeira entidade ou null</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IRepository`1.ExistsAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Threading.CancellationToken)">
            <summary>
            Verifica se existe uma entidade que atende ao filtro
            </summary>
            <param name="predicate">Filtro</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>True se existe</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IRepository`1.CountAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Threading.CancellationToken)">
            <summary>
            Conta o número de entidades que atendem ao filtro
            </summary>
            <param name="predicate">Filtro</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>Número de entidades</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IRepository`1.AddAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Adiciona uma nova entidade
            </summary>
            <param name="entity">Entidade a ser adicionada</param>
            <param name="cancellationToken">Token de cancelamento</param>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IRepository`1.AddRangeAsync(System.Collections.Generic.IEnumerable{`0},System.Threading.CancellationToken)">
            <summary>
            Adiciona múltiplas entidades
            </summary>
            <param name="entities">Entidades a serem adicionadas</param>
            <param name="cancellationToken">Token de cancelamento</param>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IRepository`1.UpdateAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Atualiza uma entidade
            </summary>
            <param name="entity">Entidade a ser atualizada</param>
            <param name="cancellationToken">Token de cancelamento</param>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IRepository`1.RemoveAsync(`0,System.String,System.Threading.CancellationToken)">
            <summary>
            Remove uma entidade (soft delete)
            </summary>
            <param name="entity">Entidade a ser removida</param>
            <param name="deletedBy">Usuário que removeu</param>
            <param name="cancellationToken">Token de cancelamento</param>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IRepository`1.RemoveAsync(System.Guid,System.String,System.Threading.CancellationToken)">
            <summary>
            Remove uma entidade por ID (soft delete)
            </summary>
            <param name="id">ID da entidade</param>
            <param name="deletedBy">Usuário que removeu</param>
            <param name="cancellationToken">Token de cancelamento</param>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IRepository`1.HardDeleteAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Remove permanentemente uma entidade
            </summary>
            <param name="entity">Entidade a ser removida</param>
            <param name="cancellationToken">Token de cancelamento</param>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IRepository`1.RestoreAsync(System.Guid,System.String,System.Threading.CancellationToken)">
            <summary>
            Restaura uma entidade removida
            </summary>
            <param name="id">ID da entidade</param>
            <param name="restoredBy">Usuário que restaurou</param>
            <param name="cancellationToken">Token de cancelamento</param>
        </member>
        <member name="T:AutoInstaller.Core.Interfaces.IPagedRepository`1">
            <summary>
            Interface para repositórios com paginação
            </summary>
            <typeparam name="TEntity">Tipo da entidade</typeparam>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IPagedRepository`1.GetPagedAsync(System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Obtém entidades paginadas
            </summary>
            <param name="pageNumber">Número da página (1-based)</param>
            <param name="pageSize">Tamanho da página</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>Resultado paginado</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IPagedRepository`1.GetPagedAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Obtém entidades paginadas com filtro
            </summary>
            <param name="predicate">Filtro</param>
            <param name="pageNumber">Número da página (1-based)</param>
            <param name="pageSize">Tamanho da página</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>Resultado paginado</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IPagedRepository`1.GetPagedAsync``1(System.Int32,System.Int32,System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Obtém entidades paginadas com ordenação
            </summary>
            <param name="pageNumber">Número da página (1-based)</param>
            <param name="pageSize">Tamanho da página</param>
            <param name="orderBy">Expressão de ordenação</param>
            <param name="ascending">Se a ordenação é ascendente</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>Resultado paginado</returns>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.IPagedRepository`1.GetPagedAsync``1(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Int32,System.Int32,System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Obtém entidades paginadas com filtro e ordenação
            </summary>
            <param name="predicate">Filtro</param>
            <param name="pageNumber">Número da página (1-based)</param>
            <param name="pageSize">Tamanho da página</param>
            <param name="orderBy">Expressão de ordenação</param>
            <param name="ascending">Se a ordenação é ascendente</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>Resultado paginado</returns>
        </member>
        <member name="T:AutoInstaller.Core.Interfaces.PagedResult`1">
            <summary>
            Resultado paginado
            </summary>
            <typeparam name="T">Tipo dos itens</typeparam>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.PagedResult`1.Items">
            <summary>
            Itens da página atual
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.PagedResult`1.PageNumber">
            <summary>
            Número da página atual (1-based)
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.PagedResult`1.PageSize">
            <summary>
            Tamanho da página
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.PagedResult`1.TotalCount">
            <summary>
            Total de itens
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.PagedResult`1.TotalPages">
            <summary>
            Total de páginas
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.PagedResult`1.HasPreviousPage">
            <summary>
            Indica se há página anterior
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.Interfaces.PagedResult`1.HasNextPage">
            <summary>
            Indica se há próxima página
            </summary>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.PagedResult`1.#ctor(System.Collections.Generic.IReadOnlyList{`0},System.Int32,System.Int32,System.Int32)">
            <summary>
            Construtor
            </summary>
            <param name="items">Itens da página</param>
            <param name="pageNumber">Número da página</param>
            <param name="pageSize">Tamanho da página</param>
            <param name="totalCount">Total de itens</param>
        </member>
        <member name="M:AutoInstaller.Core.Interfaces.PagedResult`1.Empty(System.Int32,System.Int32)">
            <summary>
            Cria um resultado paginado vazio
            </summary>
            <param name="pageNumber">Número da página</param>
            <param name="pageSize">Tamanho da página</param>
            <returns>Resultado vazio</returns>
        </member>
        <member name="T:AutoInstaller.Core.ValueObjects.ContainerName">
            <summary>
            Value Object para nome de container
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.ValueObjects.ContainerName.Value">
            <summary>
            Valor do nome do container
            </summary>
        </member>
        <member name="M:AutoInstaller.Core.ValueObjects.ContainerName.#ctor(System.String)">
            <summary>
            Construtor privado
            </summary>
            <param name="value">Nome do container</param>
        </member>
        <member name="M:AutoInstaller.Core.ValueObjects.ContainerName.Create(System.String)">
            <summary>
            Cria um novo ContainerName
            </summary>
            <param name="value">Nome do container</param>
            <returns>ContainerName válido</returns>
            <exception cref="T:AutoInstaller.Core.Exceptions.ValueObjectValidationException">Quando o nome é inválido</exception>
        </member>
        <member name="M:AutoInstaller.Core.ValueObjects.ContainerName.TryCreate(System.String,AutoInstaller.Core.ValueObjects.ContainerName@)">
            <summary>
            Tenta criar um ContainerName
            </summary>
            <param name="value">Nome do container</param>
            <param name="containerName">ContainerName criado</param>
            <returns>True se válido, false caso contrário</returns>
        </member>
        <member name="M:AutoInstaller.Core.ValueObjects.ContainerName.IsValid(System.String)">
            <summary>
            Verifica se um nome é válido
            </summary>
            <param name="value">Nome a ser validado</param>
            <returns>True se válido</returns>
        </member>
        <member name="M:AutoInstaller.Core.ValueObjects.ContainerName.GenerateUnique(System.String)">
            <summary>
            Gera um nome único baseado em um prefixo
            </summary>
            <param name="prefix">Prefixo do nome</param>
            <returns>ContainerName único</returns>
        </member>
        <member name="M:AutoInstaller.Core.ValueObjects.ContainerName.GetEqualityComponents">
            <summary>
            Retorna os componentes para comparação
            </summary>
        </member>
        <member name="M:AutoInstaller.Core.ValueObjects.ContainerName.op_Implicit(AutoInstaller.Core.ValueObjects.ContainerName)~System.String">
            <summary>
            Conversão implícita para string
            </summary>
        </member>
        <member name="M:AutoInstaller.Core.ValueObjects.ContainerName.op_Explicit(System.String)~AutoInstaller.Core.ValueObjects.ContainerName">
            <summary>
            Conversão explícita de string
            </summary>
        </member>
        <member name="M:AutoInstaller.Core.ValueObjects.ContainerName.ToString">
            <summary>
            Representação string
            </summary>
        </member>
        <member name="T:AutoInstaller.Core.ValueObjects.ImageTag">
            <summary>
            Value Object para tag de imagem Docker/Podman
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.ValueObjects.ImageTag.Value">
            <summary>
            Valor da tag
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.ValueObjects.ImageTag.IsLatest">
            <summary>
            Indica se é a tag padrão (latest)
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.ValueObjects.ImageTag.IsSemanticVersion">
            <summary>
            Indica se é uma tag de versão semântica
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.ValueObjects.ImageTag.IsDevelopmentTag">
            <summary>
            Indica se é uma tag de desenvolvimento
            </summary>
        </member>
        <member name="M:AutoInstaller.Core.ValueObjects.ImageTag.#ctor(System.String)">
            <summary>
            Construtor privado
            </summary>
            <param name="value">Valor da tag</param>
        </member>
        <member name="M:AutoInstaller.Core.ValueObjects.ImageTag.Create(System.String)">
            <summary>
            Cria uma nova ImageTag
            </summary>
            <param name="value">Valor da tag</param>
            <returns>ImageTag válida</returns>
            <exception cref="T:AutoInstaller.Core.Exceptions.ValueObjectValidationException">Quando a tag é inválida</exception>
        </member>
        <member name="M:AutoInstaller.Core.ValueObjects.ImageTag.CreateLatest">
            <summary>
            Cria a tag padrão (latest)
            </summary>
            <returns>ImageTag latest</returns>
        </member>
        <member name="M:AutoInstaller.Core.ValueObjects.ImageTag.CreateSemanticVersion(System.Int32,System.Int32,System.Int32,System.String,System.String)">
            <summary>
            Cria uma tag de versão semântica
            </summary>
            <param name="major">Versão major</param>
            <param name="minor">Versão minor</param>
            <param name="patch">Versão patch</param>
            <param name="preRelease">Pré-release (opcional)</param>
            <param name="build">Build metadata (opcional)</param>
            <returns>ImageTag com versão semântica</returns>
        </member>
        <member name="M:AutoInstaller.Core.ValueObjects.ImageTag.TryCreate(System.String,AutoInstaller.Core.ValueObjects.ImageTag@)">
            <summary>
            Tenta criar uma ImageTag
            </summary>
            <param name="value">Valor da tag</param>
            <param name="imageTag">ImageTag criada</param>
            <returns>True se válida</returns>
        </member>
        <member name="M:AutoInstaller.Core.ValueObjects.ImageTag.IsValid(System.String)">
            <summary>
            Verifica se uma tag é válida
            </summary>
            <param name="value">Valor da tag</param>
            <returns>True se válida</returns>
        </member>
        <member name="M:AutoInstaller.Core.ValueObjects.ImageTag.GetSemanticVersion">
            <summary>
            Extrai informações de versão semântica
            </summary>
            <returns>Tupla com major, minor, patch e preRelease ou null se não for semântica</returns>
        </member>
        <member name="M:AutoInstaller.Core.ValueObjects.ImageTag.CompareSemanticVersion(AutoInstaller.Core.ValueObjects.ImageTag)">
            <summary>
            Compara versões semânticas
            </summary>
            <param name="other">Outra ImageTag</param>
            <returns>-1 se menor, 0 se igual, 1 se maior, null se não forem versões semânticas</returns>
        </member>
        <member name="T:AutoInstaller.Core.ValueObjects.ImageTag.Common">
            <summary>
            Tags comuns pré-definidas
            </summary>
        </member>
        <member name="M:AutoInstaller.Core.ValueObjects.ImageTag.GetEqualityComponents">
            <summary>
            Retorna os componentes para comparação
            </summary>
        </member>
        <member name="M:AutoInstaller.Core.ValueObjects.ImageTag.op_Implicit(AutoInstaller.Core.ValueObjects.ImageTag)~System.String">
            <summary>
            Conversão implícita para string
            </summary>
        </member>
        <member name="M:AutoInstaller.Core.ValueObjects.ImageTag.op_Explicit(System.String)~AutoInstaller.Core.ValueObjects.ImageTag">
            <summary>
            Conversão explícita de string
            </summary>
        </member>
        <member name="M:AutoInstaller.Core.ValueObjects.ImageTag.ToString">
            <summary>
            Representação string
            </summary>
        </member>
        <member name="T:AutoInstaller.Core.ValueObjects.PortProtocol">
            <summary>
            Protocolo da porta
            </summary>
        </member>
        <member name="T:AutoInstaller.Core.ValueObjects.Port">
            <summary>
            Value Object para porta de rede
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.ValueObjects.Port.Number">
            <summary>
            Número da porta
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.ValueObjects.Port.Protocol">
            <summary>
            Protocolo da porta
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.ValueObjects.Port.IsPrivileged">
            <summary>
            Indica se é uma porta privilegiada (1-1023)
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.ValueObjects.Port.IsEphemeral">
            <summary>
            Indica se é uma porta efêmera (32768-65535)
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.ValueObjects.Port.IsRegistered">
            <summary>
            Indica se é uma porta registrada (1024-49151)
            </summary>
        </member>
        <member name="P:AutoInstaller.Core.ValueObjects.Port.IsDynamic">
            <summary>
            Indica se é uma porta dinâmica/privada (49152-65535)
            </summary>
        </member>
        <member name="M:AutoInstaller.Core.ValueObjects.Port.#ctor(System.Int32,AutoInstaller.Core.ValueObjects.PortProtocol)">
            <summary>
            Construtor privado
            </summary>
            <param name="number">Número da porta</param>
            <param name="protocol">Protocolo da porta</param>
        </member>
        <member name="M:AutoInstaller.Core.ValueObjects.Port.CreateTcp(System.Int32)">
            <summary>
            Cria uma nova porta TCP
            </summary>
            <param name="number">Número da porta</param>
            <returns>Port TCP válida</returns>
        </member>
        <member name="M:AutoInstaller.Core.ValueObjects.Port.CreateUdp(System.Int32)">
            <summary>
            Cria uma nova porta UDP
            </summary>
            <param name="number">Número da porta</param>
            <returns>Port UDP válida</returns>
        </member>
        <member name="M:AutoInstaller.Core.ValueObjects.Port.Create(System.Int32,AutoInstaller.Core.ValueObjects.PortProtocol)">
            <summary>
            Cria uma nova porta
            </summary>
            <param name="number">Número da porta</param>
            <param name="protocol">Protocolo da porta</param>
            <returns>Port válida</returns>
            <exception cref="T:AutoInstaller.Core.Exceptions.ValueObjectValidationException">Quando a porta é inválida</exception>
        </member>
        <member name="M:AutoInstaller.Core.ValueObjects.Port.Parse(System.String)">
            <summary>
            Cria uma porta a partir de string no formato "número/protocolo"
            </summary>
            <param name="portString">String no formato "80/tcp" ou "53/udp"</param>
            <returns>Port válida</returns>
            <exception cref="T:AutoInstaller.Core.Exceptions.ValueObjectValidationException">Quando o formato é inválido</exception>
        </member>
        <member name="M:AutoInstaller.Core.ValueObjects.Port.TryCreate(System.Int32,AutoInstaller.Core.ValueObjects.PortProtocol,AutoInstaller.Core.ValueObjects.Port@)">
            <summary>
            Tenta criar uma porta
            </summary>
            <param name="number">Número da porta</param>
            <param name="protocol">Protocolo da porta</param>
            <param name="port">Port criada</param>
            <returns>True se válida</returns>
        </member>
        <member name="M:AutoInstaller.Core.ValueObjects.Port.TryParse(System.String,AutoInstaller.Core.ValueObjects.Port@)">
            <summary>
            Tenta fazer parse de uma string
            </summary>
            <param name="portString">String da porta</param>
            <param name="port">Port criada</param>
            <returns>True se válida</returns>
        </member>
        <member name="M:AutoInstaller.Core.ValueObjects.Port.IsValid(System.Int32)">
            <summary>
            Verifica se uma porta é válida
            </summary>
            <param name="number">Número da porta</param>
            <returns>True se válida</returns>
        </member>
        <member name="T:AutoInstaller.Core.ValueObjects.Port.Common">
            <summary>
            Portas comuns pré-definidas
            </summary>
        </member>
        <member name="M:AutoInstaller.Core.ValueObjects.Port.GetEqualityComponents">
            <summary>
            Retorna os componentes para comparação
            </summary>
        </member>
        <member name="M:AutoInstaller.Core.ValueObjects.Port.ToString">
            <summary>
            Representação string no formato "número/protocolo"
            </summary>
        </member>
        <member name="M:AutoInstaller.Core.ValueObjects.Port.ToNumberString">
            <summary>
            Representação string apenas com o número
            </summary>
        </member>
    </members>
</doc>
