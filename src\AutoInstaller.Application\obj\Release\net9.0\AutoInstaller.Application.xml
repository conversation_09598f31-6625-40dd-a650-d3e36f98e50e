<?xml version="1.0"?>
<doc>
    <assembly>
        <name>AutoInstaller.Application</name>
    </assembly>
    <members>
        <member name="T:AutoInstaller.Application.Common.ICommand">
            <summary>
            Interface base para comandos sem retorno
            </summary>
        </member>
        <member name="T:AutoInstaller.Application.Common.ICommand`1">
            <summary>
            Interface base para comandos com retorno
            </summary>
            <typeparam name="TResponse">Tipo da resposta</typeparam>
        </member>
        <member name="T:AutoInstaller.Application.Common.ICommandHandler`1">
            <summary>
            Interface base para handlers de comandos sem retorno
            </summary>
            <typeparam name="TCommand">Tipo do comando</typeparam>
        </member>
        <member name="T:AutoInstaller.Application.Common.ICommandHandler`2">
            <summary>
            Interface base para handlers de comandos com retorno
            </summary>
            <typeparam name="TCommand">Tipo do comando</typeparam>
            <typeparam name="TResponse">Tipo da resposta</typeparam>
        </member>
        <member name="T:AutoInstaller.Application.Common.BaseCommand">
            <summary>
            Classe base abstrata para comandos
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Common.BaseCommand.ExecutedBy">
            <summary>
            Usuário que está executando o comando
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Common.BaseCommand.ExecutedAt">
            <summary>
            Timestamp da execução
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Common.BaseCommand.CorrelationId">
            <summary>
            ID de correlação para rastreamento
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Common.BaseCommand.Metadata">
            <summary>
            Metadados adicionais
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Common.BaseCommand.#ctor">
            <summary>
            Construtor protegido
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Common.BaseCommand.#ctor(System.String)">
            <summary>
            Construtor com usuário executor
            </summary>
            <param name="executedBy">Usuário que executa o comando</param>
        </member>
        <member name="M:AutoInstaller.Application.Common.BaseCommand.AddMetadata(System.String,System.Object)">
            <summary>
            Adiciona metadados ao comando
            </summary>
            <param name="key">Chave do metadado</param>
            <param name="value">Valor do metadado</param>
        </member>
        <member name="M:AutoInstaller.Application.Common.BaseCommand.GetMetadata``1(System.String)">
            <summary>
            Obtém um metadado
            </summary>
            <typeparam name="T">Tipo do metadado</typeparam>
            <param name="key">Chave do metadado</param>
            <returns>Valor do metadado ou default(T)</returns>
        </member>
        <member name="T:AutoInstaller.Application.Common.BaseCommand`1">
            <summary>
            Classe base abstrata para comandos com retorno
            </summary>
            <typeparam name="TResponse">Tipo da resposta</typeparam>
        </member>
        <member name="P:AutoInstaller.Application.Common.BaseCommand`1.ExecutedBy">
            <summary>
            Usuário que está executando o comando
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Common.BaseCommand`1.ExecutedAt">
            <summary>
            Timestamp da execução
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Common.BaseCommand`1.CorrelationId">
            <summary>
            ID de correlação para rastreamento
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Common.BaseCommand`1.Metadata">
            <summary>
            Metadados adicionais
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Common.BaseCommand`1.#ctor">
            <summary>
            Construtor protegido
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Common.BaseCommand`1.#ctor(System.String)">
            <summary>
            Construtor com usuário executor
            </summary>
            <param name="executedBy">Usuário que executa o comando</param>
        </member>
        <member name="M:AutoInstaller.Application.Common.BaseCommand`1.AddMetadata(System.String,System.Object)">
            <summary>
            Adiciona metadados ao comando
            </summary>
            <param name="key">Chave do metadado</param>
            <param name="value">Valor do metadado</param>
        </member>
        <member name="M:AutoInstaller.Application.Common.BaseCommand`1.GetMetadata``1(System.String)">
            <summary>
            Obtém um metadado
            </summary>
            <typeparam name="T">Tipo do metadado</typeparam>
            <param name="key">Chave do metadado</param>
            <returns>Valor do metadado ou default(T)</returns>
        </member>
        <member name="T:AutoInstaller.Application.Common.IQuery`1">
            <summary>
            Interface base para queries
            </summary>
            <typeparam name="TResponse">Tipo da resposta</typeparam>
        </member>
        <member name="T:AutoInstaller.Application.Common.IQueryHandler`2">
            <summary>
            Interface base para handlers de queries
            </summary>
            <typeparam name="TQuery">Tipo da query</typeparam>
            <typeparam name="TResponse">Tipo da resposta</typeparam>
        </member>
        <member name="T:AutoInstaller.Application.Common.BaseQuery`1">
            <summary>
            Classe base abstrata para queries
            </summary>
            <typeparam name="TResponse">Tipo da resposta</typeparam>
        </member>
        <member name="P:AutoInstaller.Application.Common.BaseQuery`1.ExecutedBy">
            <summary>
            Usuário que está executando a query
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Common.BaseQuery`1.ExecutedAt">
            <summary>
            Timestamp da execução
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Common.BaseQuery`1.CorrelationId">
            <summary>
            ID de correlação para rastreamento
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Common.BaseQuery`1.Metadata">
            <summary>
            Metadados adicionais
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Common.BaseQuery`1.#ctor">
            <summary>
            Construtor protegido
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Common.BaseQuery`1.#ctor(System.String)">
            <summary>
            Construtor com usuário executor
            </summary>
            <param name="executedBy">Usuário que executa a query</param>
        </member>
        <member name="M:AutoInstaller.Application.Common.BaseQuery`1.AddMetadata(System.String,System.Object)">
            <summary>
            Adiciona metadados à query
            </summary>
            <param name="key">Chave do metadado</param>
            <param name="value">Valor do metadado</param>
        </member>
        <member name="M:AutoInstaller.Application.Common.BaseQuery`1.GetMetadata``1(System.String)">
            <summary>
            Obtém um metadado
            </summary>
            <typeparam name="T">Tipo do metadado</typeparam>
            <param name="key">Chave do metadado</param>
            <returns>Valor do metadado ou default(T)</returns>
        </member>
        <member name="T:AutoInstaller.Application.Common.BasePagedQuery`1">
            <summary>
            Query base para consultas paginadas
            </summary>
            <typeparam name="TResponse">Tipo da resposta</typeparam>
        </member>
        <member name="P:AutoInstaller.Application.Common.BasePagedQuery`1.PageNumber">
            <summary>
            Número da página (1-based)
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Common.BasePagedQuery`1.PageSize">
            <summary>
            Tamanho da página
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Common.BasePagedQuery`1.Skip">
            <summary>
            Número de itens a pular
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Common.BasePagedQuery`1.#ctor">
            <summary>
            Construtor protegido
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Common.BasePagedQuery`1.#ctor(System.String)">
            <summary>
            Construtor com usuário executor
            </summary>
            <param name="executedBy">Usuário que executa a query</param>
        </member>
        <member name="M:AutoInstaller.Application.Common.BasePagedQuery`1.#ctor(System.Int32,System.Int32)">
            <summary>
            Construtor com paginação
            </summary>
            <param name="pageNumber">Número da página</param>
            <param name="pageSize">Tamanho da página</param>
        </member>
        <member name="M:AutoInstaller.Application.Common.BasePagedQuery`1.#ctor(System.String,System.Int32,System.Int32)">
            <summary>
            Construtor completo
            </summary>
            <param name="executedBy">Usuário que executa a query</param>
            <param name="pageNumber">Número da página</param>
            <param name="pageSize">Tamanho da página</param>
        </member>
        <member name="T:AutoInstaller.Application.Common.BaseSearchQuery`1">
            <summary>
            Query base para consultas com filtros de texto
            </summary>
            <typeparam name="TResponse">Tipo da resposta</typeparam>
        </member>
        <member name="P:AutoInstaller.Application.Common.BaseSearchQuery`1.SearchText">
            <summary>
            Texto de busca
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Common.BaseSearchQuery`1.HasSearchFilter">
            <summary>
            Indica se há filtro de busca
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Common.BaseSearchQuery`1.#ctor">
            <summary>
            Construtor protegido
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Common.BaseSearchQuery`1.#ctor(System.String)">
            <summary>
            Construtor com usuário executor
            </summary>
            <param name="executedBy">Usuário que executa a query</param>
        </member>
        <member name="M:AutoInstaller.Application.Common.BaseSearchQuery`1.#ctor(System.String,System.Int32,System.Int32)">
            <summary>
            Construtor com busca e paginação
            </summary>
            <param name="searchText">Texto de busca</param>
            <param name="pageNumber">Número da página</param>
            <param name="pageSize">Tamanho da página</param>
        </member>
        <member name="M:AutoInstaller.Application.Common.BaseSearchQuery`1.#ctor(System.String,System.String,System.Int32,System.Int32)">
            <summary>
            Construtor completo
            </summary>
            <param name="executedBy">Usuário que executa a query</param>
            <param name="searchText">Texto de busca</param>
            <param name="pageNumber">Número da página</param>
            <param name="pageSize">Tamanho da página</param>
        </member>
        <member name="T:AutoInstaller.Application.Common.BaseSortedQuery`1">
            <summary>
            Query base para consultas com ordenação
            </summary>
            <typeparam name="TResponse">Tipo da resposta</typeparam>
        </member>
        <member name="P:AutoInstaller.Application.Common.BaseSortedQuery`1.SortBy">
            <summary>
            Campo de ordenação
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Common.BaseSortedQuery`1.SortAscending">
            <summary>
            Direção da ordenação (true = ascendente, false = descendente)
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Common.BaseSortedQuery`1.HasCustomSort">
            <summary>
            Indica se há ordenação personalizada
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Common.BaseSortedQuery`1.#ctor">
            <summary>
            Construtor protegido
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Common.BaseSortedQuery`1.#ctor(System.String)">
            <summary>
            Construtor com usuário executor
            </summary>
            <param name="executedBy">Usuário que executa a query</param>
        </member>
        <member name="M:AutoInstaller.Application.Common.BaseSortedQuery`1.#ctor(System.String,System.Boolean)">
            <summary>
            Construtor com ordenação
            </summary>
            <param name="sortBy">Campo de ordenação</param>
            <param name="sortAscending">Direção da ordenação</param>
        </member>
        <member name="M:AutoInstaller.Application.Common.BaseSortedQuery`1.#ctor(System.String,System.Int32,System.Int32,System.String,System.Boolean)">
            <summary>
            Construtor completo
            </summary>
            <param name="executedBy">Usuário que executa a query</param>
            <param name="pageNumber">Número da página</param>
            <param name="pageSize">Tamanho da página</param>
            <param name="sortBy">Campo de ordenação</param>
            <param name="sortAscending">Direção da ordenação</param>
        </member>
        <member name="T:AutoInstaller.Application.DTOs.ContainerDto">
            <summary>
            DTO para Container
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerDto.Id">
            <summary>
            ID do container
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerDto.Name">
            <summary>
            Nome do container
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerDto.ImageName">
            <summary>
            Nome da imagem
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerDto.ImageTag">
            <summary>
            Tag da imagem
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerDto.Status">
            <summary>
            Status do container
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerDto.Runtime">
            <summary>
            Runtime utilizado
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerDto.RuntimeId">
            <summary>
            ID do container no runtime
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerDto.Command">
            <summary>
            Comando executado
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerDto.Arguments">
            <summary>
            Argumentos do comando
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerDto.WorkingDirectory">
            <summary>
            Diretório de trabalho
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerDto.User">
            <summary>
            Usuário de execução
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerDto.AutoRestart">
            <summary>
            Auto restart habilitado
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerDto.RestartPolicy">
            <summary>
            Política de reinicialização
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerDto.MemoryLimit">
            <summary>
            Limite de memória em bytes
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerDto.CpuLimit">
            <summary>
            Limite de CPU
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerDto.StartedAt">
            <summary>
            Data de início
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerDto.StoppedAt">
            <summary>
            Data de parada
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerDto.ExposedPorts">
            <summary>
            Portas expostas
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerDto.EnvironmentVariables">
            <summary>
            Variáveis de ambiente
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerDto.Labels">
            <summary>
            Labels
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerDto.Volumes">
            <summary>
            Volumes
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerDto.CreatedAt">
            <summary>
            Data de criação
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerDto.UpdatedAt">
            <summary>
            Data de atualização
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerDto.CreatedBy">
            <summary>
            Usuário que criou
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerDto.UpdatedBy">
            <summary>
            Usuário que atualizou
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerDto.IsDeleted">
            <summary>
            Indica se foi excluído
            </summary>
        </member>
        <member name="T:AutoInstaller.Application.DTOs.PortDto">
            <summary>
            DTO para Porta
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.PortDto.Number">
            <summary>
            Número da porta
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.PortDto.Protocol">
            <summary>
            Protocolo (TCP/UDP)
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.PortDto.IsPrivileged">
            <summary>
            Indica se é porta privilegiada
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.PortDto.IsEphemeral">
            <summary>
            Indica se é porta efêmera
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.PortDto.DisplayName">
            <summary>
            Representação string da porta
            </summary>
        </member>
        <member name="T:AutoInstaller.Application.DTOs.ContainerSummaryDto">
            <summary>
            DTO resumido para Container (para listas)
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerSummaryDto.Id">
            <summary>
            ID do container
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerSummaryDto.Name">
            <summary>
            Nome do container
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerSummaryDto.ImageName">
            <summary>
            Nome da imagem
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerSummaryDto.ImageTag">
            <summary>
            Tag da imagem
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerSummaryDto.Status">
            <summary>
            Status do container
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerSummaryDto.Runtime">
            <summary>
            Runtime utilizado
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerSummaryDto.ExposedPortsCount">
            <summary>
            Número de portas expostas
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerSummaryDto.CreatedAt">
            <summary>
            Data de criação
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerSummaryDto.StartedAt">
            <summary>
            Data de início (se em execução)
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerSummaryDto.CreatedBy">
            <summary>
            Usuário que criou
            </summary>
        </member>
        <member name="T:AutoInstaller.Application.DTOs.ContainerStatisticsDto">
            <summary>
            DTO para estatísticas de containers
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerStatisticsDto.TotalContainers">
            <summary>
            Total de containers
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerStatisticsDto.RunningContainers">
            <summary>
            Containers em execução
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerStatisticsDto.StoppedContainers">
            <summary>
            Containers parados
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerStatisticsDto.PausedContainers">
            <summary>
            Containers pausados
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerStatisticsDto.ErrorContainers">
            <summary>
            Containers com erro
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerStatisticsDto.DockerContainers">
            <summary>
            Containers Docker
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerStatisticsDto.PodmanContainers">
            <summary>
            Containers Podman
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerStatisticsDto.TotalMemoryUsage">
            <summary>
            Uso total de memória
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerStatisticsDto.TotalCpuUsage">
            <summary>
            Uso total de CPU
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerStatisticsDto.LastUpdated">
            <summary>
            Data da última atualização
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerStatisticsDto.StatusDistribution">
            <summary>
            Distribuição por status
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.ContainerStatisticsDto.RuntimeDistribution">
            <summary>
            Distribuição por runtime
            </summary>
        </member>
        <member name="T:AutoInstaller.Application.DTOs.PagedContainerResultDto">
            <summary>
            DTO para resultado paginado de containers
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.PagedContainerResultDto.Items">
            <summary>
            Containers da página atual
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.PagedContainerResultDto.PageNumber">
            <summary>
            Número da página atual
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.PagedContainerResultDto.PageSize">
            <summary>
            Tamanho da página
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.PagedContainerResultDto.TotalCount">
            <summary>
            Total de itens
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.PagedContainerResultDto.TotalPages">
            <summary>
            Total de páginas
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.PagedContainerResultDto.HasPreviousPage">
            <summary>
            Indica se há página anterior
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.PagedContainerResultDto.HasNextPage">
            <summary>
            Indica se há próxima página
            </summary>
        </member>
        <member name="T:AutoInstaller.Application.DTOs.CreateContainerDto">
            <summary>
            DTO para criação de container
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.CreateContainerDto.Name">
            <summary>
            Nome do container
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.CreateContainerDto.ImageName">
            <summary>
            Nome da imagem
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.CreateContainerDto.ImageTag">
            <summary>
            Tag da imagem
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.CreateContainerDto.Runtime">
            <summary>
            Runtime a ser utilizado
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.CreateContainerDto.Command">
            <summary>
            Comando a ser executado
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.CreateContainerDto.Arguments">
            <summary>
            Argumentos do comando
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.CreateContainerDto.WorkingDirectory">
            <summary>
            Diretório de trabalho
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.CreateContainerDto.User">
            <summary>
            Usuário de execução
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.CreateContainerDto.AutoRestart">
            <summary>
            Auto restart
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.CreateContainerDto.RestartPolicy">
            <summary>
            Política de reinicialização
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.CreateContainerDto.MemoryLimit">
            <summary>
            Limite de memória
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.CreateContainerDto.CpuLimit">
            <summary>
            Limite de CPU
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.CreateContainerDto.ExposedPorts">
            <summary>
            Portas a serem expostas
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.CreateContainerDto.EnvironmentVariables">
            <summary>
            Variáveis de ambiente
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.CreateContainerDto.Labels">
            <summary>
            Labels
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.CreateContainerDto.Volumes">
            <summary>
            Volumes
            </summary>
        </member>
        <member name="T:AutoInstaller.Application.DTOs.CreatePortDto">
            <summary>
            DTO para criação de porta
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.CreatePortDto.Number">
            <summary>
            Número da porta
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.DTOs.CreatePortDto.Protocol">
            <summary>
            Protocolo
            </summary>
        </member>
        <member name="T:AutoInstaller.Application.Features.Containers.Commands.CreateContainerCommand">
            <summary>
            Command para criar um novo container
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Commands.CreateContainerCommand.Name">
            <summary>
            Nome do container
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Commands.CreateContainerCommand.ImageName">
            <summary>
            Nome da imagem
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Commands.CreateContainerCommand.ImageTag">
            <summary>
            Tag da imagem
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Commands.CreateContainerCommand.Runtime">
            <summary>
            Runtime a ser utilizado
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Commands.CreateContainerCommand.Command">
            <summary>
            Comando a ser executado
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Commands.CreateContainerCommand.Arguments">
            <summary>
            Argumentos do comando
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Commands.CreateContainerCommand.WorkingDirectory">
            <summary>
            Diretório de trabalho
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Commands.CreateContainerCommand.User">
            <summary>
            Usuário de execução
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Commands.CreateContainerCommand.AutoRestart">
            <summary>
            Auto restart
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Commands.CreateContainerCommand.RestartPolicy">
            <summary>
            Política de reinicialização
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Commands.CreateContainerCommand.MemoryLimit">
            <summary>
            Limite de memória em bytes
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Commands.CreateContainerCommand.CpuLimit">
            <summary>
            Limite de CPU
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Commands.CreateContainerCommand.ExposedPorts">
            <summary>
            Portas a serem expostas
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Commands.CreateContainerCommand.EnvironmentVariables">
            <summary>
            Variáveis de ambiente
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Commands.CreateContainerCommand.Labels">
            <summary>
            Labels
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Commands.CreateContainerCommand.Volumes">
            <summary>
            Volumes
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Commands.CreateContainerCommand.#ctor">
            <summary>
            Construtor
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Commands.CreateContainerCommand.#ctor(System.String)">
            <summary>
            Construtor com usuário executor
            </summary>
            <param name="executedBy">Usuário que executa o comando</param>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Commands.CreateContainerCommand.FromDto(AutoInstaller.Application.DTOs.CreateContainerDto,System.String)">
            <summary>
            Cria o command a partir de um DTO
            </summary>
            <param name="dto">DTO de criação</param>
            <param name="executedBy">Usuário executor</param>
            <returns>Command configurado</returns>
        </member>
        <member name="T:AutoInstaller.Application.Features.Containers.Commands.UpdateContainerCommand">
            <summary>
            Command para atualizar um container
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Commands.UpdateContainerCommand.Id">
            <summary>
            ID do container
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Commands.UpdateContainerCommand.Command">
            <summary>
            Comando a ser executado
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Commands.UpdateContainerCommand.Arguments">
            <summary>
            Argumentos do comando
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Commands.UpdateContainerCommand.WorkingDirectory">
            <summary>
            Diretório de trabalho
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Commands.UpdateContainerCommand.User">
            <summary>
            Usuário de execução
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Commands.UpdateContainerCommand.AutoRestart">
            <summary>
            Auto restart
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Commands.UpdateContainerCommand.RestartPolicy">
            <summary>
            Política de reinicialização
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Commands.UpdateContainerCommand.MemoryLimit">
            <summary>
            Limite de memória em bytes
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Commands.UpdateContainerCommand.CpuLimit">
            <summary>
            Limite de CPU
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Commands.UpdateContainerCommand.#ctor">
            <summary>
            Construtor
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Commands.UpdateContainerCommand.#ctor(System.Guid,System.String)">
            <summary>
            Construtor com ID e usuário executor
            </summary>
            <param name="id">ID do container</param>
            <param name="executedBy">Usuário que executa o comando</param>
        </member>
        <member name="T:AutoInstaller.Application.Features.Containers.Commands.DeleteContainerCommand">
            <summary>
            Command para deletar um container
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Commands.DeleteContainerCommand.Id">
            <summary>
            ID do container
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Commands.DeleteContainerCommand.Force">
            <summary>
            Forçar remoção mesmo se em execução
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Commands.DeleteContainerCommand.RemoveVolumes">
            <summary>
            Remover volumes associados
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Commands.DeleteContainerCommand.#ctor">
            <summary>
            Construtor
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Commands.DeleteContainerCommand.#ctor(System.Guid,System.String)">
            <summary>
            Construtor com ID e usuário executor
            </summary>
            <param name="id">ID do container</param>
            <param name="executedBy">Usuário que executa o comando</param>
        </member>
        <member name="T:AutoInstaller.Application.Features.Containers.Commands.StartContainerCommand">
            <summary>
            Command para iniciar um container
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Commands.StartContainerCommand.Id">
            <summary>
            ID do container
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Commands.StartContainerCommand.#ctor">
            <summary>
            Construtor
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Commands.StartContainerCommand.#ctor(System.Guid,System.String)">
            <summary>
            Construtor com ID e usuário executor
            </summary>
            <param name="id">ID do container</param>
            <param name="executedBy">Usuário que executa o comando</param>
        </member>
        <member name="T:AutoInstaller.Application.Features.Containers.Commands.StopContainerCommand">
            <summary>
            Command para parar um container
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Commands.StopContainerCommand.Id">
            <summary>
            ID do container
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Commands.StopContainerCommand.TimeoutSeconds">
            <summary>
            Timeout em segundos para parada graceful
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Commands.StopContainerCommand.Force">
            <summary>
            Forçar parada se timeout for atingido
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Commands.StopContainerCommand.#ctor">
            <summary>
            Construtor
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Commands.StopContainerCommand.#ctor(System.Guid,System.String)">
            <summary>
            Construtor com ID e usuário executor
            </summary>
            <param name="id">ID do container</param>
            <param name="executedBy">Usuário que executa o comando</param>
        </member>
        <member name="T:AutoInstaller.Application.Features.Containers.Commands.PauseContainerCommand">
            <summary>
            Command para pausar um container
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Commands.PauseContainerCommand.Id">
            <summary>
            ID do container
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Commands.PauseContainerCommand.#ctor">
            <summary>
            Construtor
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Commands.PauseContainerCommand.#ctor(System.Guid,System.String)">
            <summary>
            Construtor com ID e usuário executor
            </summary>
            <param name="id">ID do container</param>
            <param name="executedBy">Usuário que executa o comando</param>
        </member>
        <member name="T:AutoInstaller.Application.Features.Containers.Commands.UnpauseContainerCommand">
            <summary>
            Command para despausar um container
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Commands.UnpauseContainerCommand.Id">
            <summary>
            ID do container
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Commands.UnpauseContainerCommand.#ctor">
            <summary>
            Construtor
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Commands.UnpauseContainerCommand.#ctor(System.Guid,System.String)">
            <summary>
            Construtor com ID e usuário executor
            </summary>
            <param name="id">ID do container</param>
            <param name="executedBy">Usuário que executa o comando</param>
        </member>
        <member name="T:AutoInstaller.Application.Features.Containers.Commands.RestartContainerCommand">
            <summary>
            Command para reiniciar um container
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Commands.RestartContainerCommand.Id">
            <summary>
            ID do container
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Commands.RestartContainerCommand.TimeoutSeconds">
            <summary>
            Timeout em segundos para parada antes do restart
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Commands.RestartContainerCommand.#ctor">
            <summary>
            Construtor
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Commands.RestartContainerCommand.#ctor(System.Guid,System.String)">
            <summary>
            Construtor com ID e usuário executor
            </summary>
            <param name="id">ID do container</param>
            <param name="executedBy">Usuário que executa o comando</param>
        </member>
        <member name="T:AutoInstaller.Application.Features.Containers.Handlers.CreateContainerCommandHandler">
            <summary>
            Handler para CreateContainerCommand
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Handlers.CreateContainerCommandHandler.#ctor(AutoInstaller.Core.Interfaces.IContainerRepository,AutoMapper.IMapper,Microsoft.Extensions.Logging.ILogger{AutoInstaller.Application.Features.Containers.Handlers.CreateContainerCommandHandler})">
            <summary>
            Construtor
            </summary>
            <param name="containerRepository">Repositório de containers</param>
            <param name="mapper">Mapper</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Handlers.CreateContainerCommandHandler.Handle(AutoInstaller.Application.Features.Containers.Commands.CreateContainerCommand,System.Threading.CancellationToken)">
            <summary>
            Executa o comando
            </summary>
            <param name="request">Comando</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>DTO do container criado</returns>
        </member>
        <member name="T:AutoInstaller.Application.Features.Containers.Handlers.UpdateContainerCommandHandler">
            <summary>
            Handler para UpdateContainerCommand
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Handlers.UpdateContainerCommandHandler.#ctor(AutoInstaller.Core.Interfaces.IContainerRepository,AutoMapper.IMapper,Microsoft.Extensions.Logging.ILogger{AutoInstaller.Application.Features.Containers.Handlers.UpdateContainerCommandHandler})">
            <summary>
            Construtor
            </summary>
            <param name="containerRepository">Repositório de containers</param>
            <param name="mapper">Mapper</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Handlers.UpdateContainerCommandHandler.Handle(AutoInstaller.Application.Features.Containers.Commands.UpdateContainerCommand,System.Threading.CancellationToken)">
            <summary>
            Executa o comando
            </summary>
            <param name="request">Comando</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>DTO do container atualizado</returns>
        </member>
        <member name="T:AutoInstaller.Application.Features.Containers.Handlers.DeleteContainerCommandHandler">
            <summary>
            Handler para DeleteContainerCommand
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Handlers.DeleteContainerCommandHandler.#ctor(AutoInstaller.Core.Interfaces.IContainerRepository,Microsoft.Extensions.Logging.ILogger{AutoInstaller.Application.Features.Containers.Handlers.DeleteContainerCommandHandler})">
            <summary>
            Construtor
            </summary>
            <param name="containerRepository">Repositório de containers</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Handlers.DeleteContainerCommandHandler.Handle(AutoInstaller.Application.Features.Containers.Commands.DeleteContainerCommand,System.Threading.CancellationToken)">
            <summary>
            Executa o comando
            </summary>
            <param name="request">Comando</param>
            <param name="cancellationToken">Token de cancelamento</param>
        </member>
        <member name="T:AutoInstaller.Application.Features.Containers.Handlers.StartContainerCommandHandler">
            <summary>
            Handler para StartContainerCommand
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Handlers.StartContainerCommandHandler.#ctor(AutoInstaller.Core.Interfaces.IContainerRepository,Microsoft.Extensions.Logging.ILogger{AutoInstaller.Application.Features.Containers.Handlers.StartContainerCommandHandler})">
            <summary>
            Construtor
            </summary>
            <param name="containerRepository">Repositório de containers</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Handlers.StartContainerCommandHandler.Handle(AutoInstaller.Application.Features.Containers.Commands.StartContainerCommand,System.Threading.CancellationToken)">
            <summary>
            Executa o comando
            </summary>
            <param name="request">Comando</param>
            <param name="cancellationToken">Token de cancelamento</param>
        </member>
        <member name="T:AutoInstaller.Application.Features.Containers.Handlers.StopContainerCommandHandler">
            <summary>
            Handler para StopContainerCommand
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Handlers.StopContainerCommandHandler.#ctor(AutoInstaller.Core.Interfaces.IContainerRepository,Microsoft.Extensions.Logging.ILogger{AutoInstaller.Application.Features.Containers.Handlers.StopContainerCommandHandler})">
            <summary>
            Construtor
            </summary>
            <param name="containerRepository">Repositório de containers</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Handlers.StopContainerCommandHandler.Handle(AutoInstaller.Application.Features.Containers.Commands.StopContainerCommand,System.Threading.CancellationToken)">
            <summary>
            Executa o comando
            </summary>
            <param name="request">Comando</param>
            <param name="cancellationToken">Token de cancelamento</param>
        </member>
        <member name="T:AutoInstaller.Application.Features.Containers.Handlers.GetContainerByIdQueryHandler">
            <summary>
            Handler para GetContainerByIdQuery
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Handlers.GetContainerByIdQueryHandler.#ctor(AutoInstaller.Core.Interfaces.IContainerRepository,AutoMapper.IMapper,Microsoft.Extensions.Logging.ILogger{AutoInstaller.Application.Features.Containers.Handlers.GetContainerByIdQueryHandler})">
            <summary>
            Construtor
            </summary>
            <param name="containerRepository">Repositório de containers</param>
            <param name="mapper">Mapper</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Handlers.GetContainerByIdQueryHandler.Handle(AutoInstaller.Application.Features.Containers.Queries.GetContainerByIdQuery,System.Threading.CancellationToken)">
            <summary>
            Executa a query
            </summary>
            <param name="request">Query</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>DTO do container ou null</returns>
        </member>
        <member name="T:AutoInstaller.Application.Features.Containers.Handlers.GetContainerByNameQueryHandler">
            <summary>
            Handler para GetContainerByNameQuery
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Handlers.GetContainerByNameQueryHandler.#ctor(AutoInstaller.Core.Interfaces.IContainerRepository,AutoMapper.IMapper,Microsoft.Extensions.Logging.ILogger{AutoInstaller.Application.Features.Containers.Handlers.GetContainerByNameQueryHandler})">
            <summary>
            Construtor
            </summary>
            <param name="containerRepository">Repositório de containers</param>
            <param name="mapper">Mapper</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Handlers.GetContainerByNameQueryHandler.Handle(AutoInstaller.Application.Features.Containers.Queries.GetContainerByNameQuery,System.Threading.CancellationToken)">
            <summary>
            Executa a query
            </summary>
            <param name="request">Query</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>DTO do container ou null</returns>
        </member>
        <member name="T:AutoInstaller.Application.Features.Containers.Handlers.GetContainersByStatusQueryHandler">
            <summary>
            Handler para GetContainersByStatusQuery
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Handlers.GetContainersByStatusQueryHandler.#ctor(AutoInstaller.Core.Interfaces.IContainerRepository,AutoMapper.IMapper,Microsoft.Extensions.Logging.ILogger{AutoInstaller.Application.Features.Containers.Handlers.GetContainersByStatusQueryHandler})">
            <summary>
            Construtor
            </summary>
            <param name="containerRepository">Repositório de containers</param>
            <param name="mapper">Mapper</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Handlers.GetContainersByStatusQueryHandler.Handle(AutoInstaller.Application.Features.Containers.Queries.GetContainersByStatusQuery,System.Threading.CancellationToken)">
            <summary>
            Executa a query
            </summary>
            <param name="request">Query</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>Lista de containers</returns>
        </member>
        <member name="T:AutoInstaller.Application.Features.Containers.Handlers.GetContainersByRuntimeQueryHandler">
            <summary>
            Handler para GetContainersByRuntimeQuery
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Handlers.GetContainersByRuntimeQueryHandler.#ctor(AutoInstaller.Core.Interfaces.IContainerRepository,AutoMapper.IMapper,Microsoft.Extensions.Logging.ILogger{AutoInstaller.Application.Features.Containers.Handlers.GetContainersByRuntimeQueryHandler})">
            <summary>
            Construtor
            </summary>
            <param name="containerRepository">Repositório de containers</param>
            <param name="mapper">Mapper</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Handlers.GetContainersByRuntimeQueryHandler.Handle(AutoInstaller.Application.Features.Containers.Queries.GetContainersByRuntimeQuery,System.Threading.CancellationToken)">
            <summary>
            Executa a query
            </summary>
            <param name="request">Query</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>Lista de containers</returns>
        </member>
        <member name="T:AutoInstaller.Application.Features.Containers.Handlers.GetContainersPagedQueryHandler">
            <summary>
            Handler para GetContainersPagedQuery
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Handlers.GetContainersPagedQueryHandler.#ctor(AutoInstaller.Core.Interfaces.IContainerRepository,AutoMapper.IMapper,Microsoft.Extensions.Logging.ILogger{AutoInstaller.Application.Features.Containers.Handlers.GetContainersPagedQueryHandler})">
            <summary>
            Construtor
            </summary>
            <param name="containerRepository">Repositório de containers</param>
            <param name="mapper">Mapper</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Handlers.GetContainersPagedQueryHandler.Handle(AutoInstaller.Application.Features.Containers.Queries.GetContainersPagedQuery,System.Threading.CancellationToken)">
            <summary>
            Executa a query
            </summary>
            <param name="request">Query</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>Resultado paginado</returns>
        </member>
        <member name="T:AutoInstaller.Application.Features.Containers.Handlers.SearchContainersQueryHandler">
            <summary>
            Handler para SearchContainersQuery
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Handlers.SearchContainersQueryHandler.#ctor(AutoInstaller.Core.Interfaces.IContainerRepository,AutoMapper.IMapper,Microsoft.Extensions.Logging.ILogger{AutoInstaller.Application.Features.Containers.Handlers.SearchContainersQueryHandler})">
            <summary>
            Construtor
            </summary>
            <param name="containerRepository">Repositório de containers</param>
            <param name="mapper">Mapper</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Handlers.SearchContainersQueryHandler.Handle(AutoInstaller.Application.Features.Containers.Queries.SearchContainersQuery,System.Threading.CancellationToken)">
            <summary>
            Executa a query
            </summary>
            <param name="request">Query</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>Resultado paginado da busca</returns>
        </member>
        <member name="T:AutoInstaller.Application.Features.Containers.Handlers.GetContainerStatisticsQueryHandler">
            <summary>
            Handler para GetContainerStatisticsQuery
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Handlers.GetContainerStatisticsQueryHandler.#ctor(AutoInstaller.Core.Interfaces.IContainerRepository,AutoMapper.IMapper,Microsoft.Extensions.Logging.ILogger{AutoInstaller.Application.Features.Containers.Handlers.GetContainerStatisticsQueryHandler})">
            <summary>
            Construtor
            </summary>
            <param name="containerRepository">Repositório de containers</param>
            <param name="mapper">Mapper</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Handlers.GetContainerStatisticsQueryHandler.Handle(AutoInstaller.Application.Features.Containers.Queries.GetContainerStatisticsQuery,System.Threading.CancellationToken)">
            <summary>
            Executa a query
            </summary>
            <param name="request">Query</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>Estatísticas dos containers</returns>
        </member>
        <member name="T:AutoInstaller.Application.Features.Containers.Handlers.GetRunningContainersQueryHandler">
            <summary>
            Handler para GetRunningContainersQuery
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Handlers.GetRunningContainersQueryHandler.#ctor(AutoInstaller.Core.Interfaces.IContainerRepository,AutoMapper.IMapper,Microsoft.Extensions.Logging.ILogger{AutoInstaller.Application.Features.Containers.Handlers.GetRunningContainersQueryHandler})">
            <summary>
            Construtor
            </summary>
            <param name="containerRepository">Repositório de containers</param>
            <param name="mapper">Mapper</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Handlers.GetRunningContainersQueryHandler.Handle(AutoInstaller.Application.Features.Containers.Queries.GetRunningContainersQuery,System.Threading.CancellationToken)">
            <summary>
            Executa a query
            </summary>
            <param name="request">Query</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>Lista de containers em execução</returns>
        </member>
        <member name="T:AutoInstaller.Application.Features.Containers.Handlers.GetStoppedContainersQueryHandler">
            <summary>
            Handler para GetStoppedContainersQuery
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Handlers.GetStoppedContainersQueryHandler.#ctor(AutoInstaller.Core.Interfaces.IContainerRepository,AutoMapper.IMapper,Microsoft.Extensions.Logging.ILogger{AutoInstaller.Application.Features.Containers.Handlers.GetStoppedContainersQueryHandler})">
            <summary>
            Construtor
            </summary>
            <param name="containerRepository">Repositório de containers</param>
            <param name="mapper">Mapper</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Handlers.GetStoppedContainersQueryHandler.Handle(AutoInstaller.Application.Features.Containers.Queries.GetStoppedContainersQuery,System.Threading.CancellationToken)">
            <summary>
            Executa a query
            </summary>
            <param name="request">Query</param>
            <param name="cancellationToken">Token de cancelamento</param>
            <returns>Lista de containers parados</returns>
        </member>
        <member name="T:AutoInstaller.Application.Features.Containers.Queries.GetContainerByIdQuery">
            <summary>
            Query para obter um container por ID
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Queries.GetContainerByIdQuery.Id">
            <summary>
            ID do container
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Queries.GetContainerByIdQuery.#ctor">
            <summary>
            Construtor
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Queries.GetContainerByIdQuery.#ctor(System.Guid)">
            <summary>
            Construtor com ID
            </summary>
            <param name="id">ID do container</param>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Queries.GetContainerByIdQuery.#ctor(System.Guid,System.String)">
            <summary>
            Construtor com ID e usuário executor
            </summary>
            <param name="id">ID do container</param>
            <param name="executedBy">Usuário que executa a query</param>
        </member>
        <member name="T:AutoInstaller.Application.Features.Containers.Queries.GetContainerByNameQuery">
            <summary>
            Query para obter um container por nome
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Queries.GetContainerByNameQuery.Name">
            <summary>
            Nome do container
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Queries.GetContainerByNameQuery.#ctor">
            <summary>
            Construtor
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Queries.GetContainerByNameQuery.#ctor(System.String)">
            <summary>
            Construtor com nome
            </summary>
            <param name="name">Nome do container</param>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Queries.GetContainerByNameQuery.#ctor(System.String,System.String)">
            <summary>
            Construtor com nome e usuário executor
            </summary>
            <param name="name">Nome do container</param>
            <param name="executedBy">Usuário que executa a query</param>
        </member>
        <member name="T:AutoInstaller.Application.Features.Containers.Queries.GetContainersByStatusQuery">
            <summary>
            Query para obter containers por status
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Queries.GetContainersByStatusQuery.Status">
            <summary>
            Status dos containers
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Queries.GetContainersByStatusQuery.#ctor">
            <summary>
            Construtor
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Queries.GetContainersByStatusQuery.#ctor(AutoInstaller.Core.Entities.ContainerStatus)">
            <summary>
            Construtor com status
            </summary>
            <param name="status">Status dos containers</param>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Queries.GetContainersByStatusQuery.#ctor(AutoInstaller.Core.Entities.ContainerStatus,System.String)">
            <summary>
            Construtor com status e usuário executor
            </summary>
            <param name="status">Status dos containers</param>
            <param name="executedBy">Usuário que executa a query</param>
        </member>
        <member name="T:AutoInstaller.Application.Features.Containers.Queries.GetContainersByRuntimeQuery">
            <summary>
            Query para obter containers por runtime
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Queries.GetContainersByRuntimeQuery.Runtime">
            <summary>
            Runtime dos containers
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Queries.GetContainersByRuntimeQuery.#ctor">
            <summary>
            Construtor
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Queries.GetContainersByRuntimeQuery.#ctor(AutoInstaller.Core.Entities.ContainerRuntime)">
            <summary>
            Construtor com runtime
            </summary>
            <param name="runtime">Runtime dos containers</param>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Queries.GetContainersByRuntimeQuery.#ctor(AutoInstaller.Core.Entities.ContainerRuntime,System.String)">
            <summary>
            Construtor com runtime e usuário executor
            </summary>
            <param name="runtime">Runtime dos containers</param>
            <param name="executedBy">Usuário que executa a query</param>
        </member>
        <member name="T:AutoInstaller.Application.Features.Containers.Queries.GetContainersByImageQuery">
            <summary>
            Query para obter containers por imagem
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Queries.GetContainersByImageQuery.ImageName">
            <summary>
            Nome da imagem
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Queries.GetContainersByImageQuery.ImageTag">
            <summary>
            Tag da imagem (opcional)
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Queries.GetContainersByImageQuery.#ctor">
            <summary>
            Construtor
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Queries.GetContainersByImageQuery.#ctor(System.String,System.String)">
            <summary>
            Construtor com imagem
            </summary>
            <param name="imageName">Nome da imagem</param>
            <param name="imageTag">Tag da imagem</param>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Queries.GetContainersByImageQuery.#ctor(System.String,System.String,System.String)">
            <summary>
            Construtor completo
            </summary>
            <param name="imageName">Nome da imagem</param>
            <param name="imageTag">Tag da imagem</param>
            <param name="executedBy">Usuário que executa a query</param>
        </member>
        <member name="T:AutoInstaller.Application.Features.Containers.Queries.GetContainersPagedQuery">
            <summary>
            Query para obter todos os containers paginados
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Queries.GetContainersPagedQuery.StatusFilter">
            <summary>
            Filtro por status (opcional)
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Queries.GetContainersPagedQuery.RuntimeFilter">
            <summary>
            Filtro por runtime (opcional)
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Queries.GetContainersPagedQuery.CreatedByFilter">
            <summary>
            Filtro por usuário criador (opcional)
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Queries.GetContainersPagedQuery.IncludeDeleted">
            <summary>
            Incluir containers excluídos
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Queries.GetContainersPagedQuery.#ctor">
            <summary>
            Construtor
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Queries.GetContainersPagedQuery.#ctor(System.Int32,System.Int32)">
            <summary>
            Construtor com paginação
            </summary>
            <param name="pageNumber">Número da página</param>
            <param name="pageSize">Tamanho da página</param>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Queries.GetContainersPagedQuery.#ctor(System.String,System.Int32,System.Int32,System.String,System.Boolean)">
            <summary>
            Construtor completo
            </summary>
            <param name="executedBy">Usuário que executa a query</param>
            <param name="pageNumber">Número da página</param>
            <param name="pageSize">Tamanho da página</param>
            <param name="sortBy">Campo de ordenação</param>
            <param name="sortAscending">Direção da ordenação</param>
        </member>
        <member name="T:AutoInstaller.Application.Features.Containers.Queries.SearchContainersQuery">
            <summary>
            Query para buscar containers
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Queries.SearchContainersQuery.StatusFilter">
            <summary>
            Filtro por status (opcional)
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Queries.SearchContainersQuery.RuntimeFilter">
            <summary>
            Filtro por runtime (opcional)
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Queries.SearchContainersQuery.IncludeDeleted">
            <summary>
            Incluir containers excluídos
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Queries.SearchContainersQuery.#ctor">
            <summary>
            Construtor
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Queries.SearchContainersQuery.#ctor(System.String)">
            <summary>
            Construtor com busca
            </summary>
            <param name="searchText">Texto de busca</param>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Queries.SearchContainersQuery.#ctor(System.String,System.Int32,System.Int32)">
            <summary>
            Construtor com busca e paginação
            </summary>
            <param name="searchText">Texto de busca</param>
            <param name="pageNumber">Número da página</param>
            <param name="pageSize">Tamanho da página</param>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Queries.SearchContainersQuery.#ctor(System.String,System.String,System.Int32,System.Int32)">
            <summary>
            Construtor completo
            </summary>
            <param name="executedBy">Usuário que executa a query</param>
            <param name="searchText">Texto de busca</param>
            <param name="pageNumber">Número da página</param>
            <param name="pageSize">Tamanho da página</param>
        </member>
        <member name="T:AutoInstaller.Application.Features.Containers.Queries.GetContainerStatisticsQuery">
            <summary>
            Query para obter estatísticas de containers
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Queries.GetContainerStatisticsQuery.IncludeDeleted">
            <summary>
            Incluir containers excluídos nas estatísticas
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Queries.GetContainerStatisticsQuery.#ctor">
            <summary>
            Construtor
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Queries.GetContainerStatisticsQuery.#ctor(System.String)">
            <summary>
            Construtor com usuário executor
            </summary>
            <param name="executedBy">Usuário que executa a query</param>
        </member>
        <member name="T:AutoInstaller.Application.Features.Containers.Queries.GetRunningContainersQuery">
            <summary>
            Query para obter containers em execução
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Queries.GetRunningContainersQuery.RuntimeFilter">
            <summary>
            Filtro por runtime (opcional)
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Queries.GetRunningContainersQuery.#ctor">
            <summary>
            Construtor
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Queries.GetRunningContainersQuery.#ctor(System.String)">
            <summary>
            Construtor com usuário executor
            </summary>
            <param name="executedBy">Usuário que executa a query</param>
        </member>
        <member name="T:AutoInstaller.Application.Features.Containers.Queries.GetStoppedContainersQuery">
            <summary>
            Query para obter containers parados
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Features.Containers.Queries.GetStoppedContainersQuery.RuntimeFilter">
            <summary>
            Filtro por runtime (opcional)
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Queries.GetStoppedContainersQuery.#ctor">
            <summary>
            Construtor
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Features.Containers.Queries.GetStoppedContainersQuery.#ctor(System.String)">
            <summary>
            Construtor com usuário executor
            </summary>
            <param name="executedBy">Usuário que executa a query</param>
        </member>
        <member name="T:AutoInstaller.Application.Features.Containers.Validators.CreateContainerCommandValidator">
            <summary>
            Validator para CreateContainerCommand
            </summary>
        </member>
        <member name="T:AutoInstaller.Application.Features.Containers.Validators.UpdateContainerCommandValidator">
            <summary>
            Validator para UpdateContainerCommand
            </summary>
        </member>
        <member name="T:AutoInstaller.Application.Features.Containers.Validators.DeleteContainerCommandValidator">
            <summary>
            Validator para DeleteContainerCommand
            </summary>
        </member>
        <member name="T:AutoInstaller.Application.Features.Containers.Validators.StartContainerCommandValidator">
            <summary>
            Validator para StartContainerCommand
            </summary>
        </member>
        <member name="T:AutoInstaller.Application.Features.Containers.Validators.StopContainerCommandValidator">
            <summary>
            Validator para StopContainerCommand
            </summary>
        </member>
        <member name="T:AutoInstaller.Application.Features.Containers.Validators.RestartContainerCommandValidator">
            <summary>
            Validator para RestartContainerCommand
            </summary>
        </member>
        <member name="T:AutoInstaller.Application.Mappings.ContainerMappingProfile">
            <summary>
            Profile de mapeamento para Container
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Mappings.ContainerMappingProfile.#ctor">
            <summary>
            Construtor
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Mappings.ContainerMappingProfile.CreateStatusDistribution">
            <summary>
            Cria distribuição de status para estatísticas
            </summary>
            <returns>Dicionário com distribuição de status</returns>
        </member>
        <member name="M:AutoInstaller.Application.Mappings.ContainerMappingProfile.CreateRuntimeDistribution(AutoInstaller.Core.Interfaces.ContainerStatistics)">
            <summary>
            Cria distribuição de runtime para estatísticas
            </summary>
            <param name="statistics">Estatísticas originais</param>
            <returns>Dicionário com distribuição de runtime</returns>
        </member>
        <member name="T:AutoInstaller.Application.Mappings.ValueObjectMappingProfile">
            <summary>
            Profile de mapeamento para Value Objects
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Mappings.ValueObjectMappingProfile.#ctor">
            <summary>
            Construtor
            </summary>
        </member>
        <member name="T:AutoInstaller.Application.Mappings.DomainEventMappingProfile">
            <summary>
            Profile de mapeamento para Domain Events
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Mappings.DomainEventMappingProfile.#ctor">
            <summary>
            Construtor
            </summary>
        </member>
        <member name="T:AutoInstaller.Application.Mappings.DomainEventDto">
            <summary>
            DTO para eventos de domínio
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Mappings.DomainEventDto.EventId">
            <summary>
            ID do evento
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Mappings.DomainEventDto.EventType">
            <summary>
            Tipo do evento
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Mappings.DomainEventDto.OccurredOn">
            <summary>
            Data de ocorrência
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Mappings.DomainEventDto.Version">
            <summary>
            Versão do evento
            </summary>
        </member>
        <member name="P:AutoInstaller.Application.Mappings.DomainEventDto.EventData">
            <summary>
            Dados do evento (JSON)
            </summary>
        </member>
        <member name="T:AutoInstaller.Application.Mappings.MappingExtensions">
            <summary>
            Extensões para AutoMapper
            </summary>
        </member>
        <member name="M:AutoInstaller.Application.Mappings.MappingExtensions.MapList``2(AutoMapper.IMapper,System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Mapeia uma lista de entidades para DTOs
            </summary>
            <typeparam name="TSource">Tipo de origem</typeparam>
            <typeparam name="TDestination">Tipo de destino</typeparam>
            <param name="mapper">Mapper</param>
            <param name="source">Lista de origem</param>
            <returns>Lista mapeada</returns>
        </member>
        <member name="M:AutoInstaller.Application.Mappings.MappingExtensions.MapPagedResult``2(AutoMapper.IMapper,AutoInstaller.Core.Interfaces.PagedResult{``0})">
            <summary>
            Mapeia um resultado paginado
            </summary>
            <typeparam name="TSource">Tipo de origem</typeparam>
            <typeparam name="TDestination">Tipo de destino</typeparam>
            <param name="mapper">Mapper</param>
            <param name="source">Resultado paginado de origem</param>
            <returns>Resultado paginado mapeado</returns>
        </member>
        <member name="M:AutoInstaller.Application.Mappings.MappingExtensions.MapContainerToDto(AutoMapper.IMapper,AutoInstaller.Core.Entities.Container,System.Boolean)">
            <summary>
            Mapeia um container para DTO com configurações específicas
            </summary>
            <param name="mapper">Mapper</param>
            <param name="container">Container</param>
            <param name="includeDetails">Se deve incluir detalhes completos</param>
            <returns>DTO do container</returns>
        </member>
    </members>
</doc>
