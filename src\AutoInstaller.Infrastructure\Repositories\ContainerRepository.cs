using AutoInstaller.Core.Entities;
using AutoInstaller.Core.Interfaces;
using AutoInstaller.Core.ValueObjects;
using AutoInstaller.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace AutoInstaller.Infrastructure.Repositories;

/// <summary>
/// Repositório para Container
/// </summary>
public class ContainerRepository : BaseRepository<Container>, IContainerRepository
{
    /// <summary>
    /// Construtor
    /// </summary>
    /// <param name="context">Contexto do banco de dados</param>
    /// <param name="logger">Logger</param>
    public ContainerRepository(AutoInstallerDbContext context, ILogger<ContainerRepository> logger)
        : base(context, logger)
    {
    }

    /// <summary>
    /// Obtém um container pelo nome
    /// </summary>
    /// <param name="name">Nome do container</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Container ou null se não encontrado</returns>
    public async Task<Container?> GetByNameAsync(ContainerName name, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _dbSet.FirstOrDefaultAsync(c => c.Name == name, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar container por nome {ContainerName}", name.Value);
            throw;
        }
    }

    /// <summary>
    /// Obtém um container pelo ID do runtime
    /// </summary>
    /// <param name="runtimeId">ID do container no runtime</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Container ou null se não encontrado</returns>
    public async Task<Container?> GetByRuntimeIdAsync(string runtimeId, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(runtimeId))
                return null;

            return await _dbSet.FirstOrDefaultAsync(c => c.RuntimeId == runtimeId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar container por RuntimeId {RuntimeId}", runtimeId);
            throw;
        }
    }

    /// <summary>
    /// Obtém containers por status
    /// </summary>
    /// <param name="status">Status dos containers</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de containers com o status especificado</returns>
    public async Task<IReadOnlyList<Container>> GetByStatusAsync(ContainerStatus status, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _dbSet
                .Where(c => c.Status == status)
                .OrderByDescending(c => c.CreatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar containers por status {Status}", status);
            throw;
        }
    }

    /// <summary>
    /// Obtém containers por runtime
    /// </summary>
    /// <param name="runtime">Runtime dos containers</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de containers do runtime especificado</returns>
    public async Task<IReadOnlyList<Container>> GetByRuntimeAsync(ContainerRuntime runtime, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _dbSet
                .Where(c => c.Runtime == runtime)
                .OrderByDescending(c => c.CreatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar containers por runtime {Runtime}", runtime);
            throw;
        }
    }

    /// <summary>
    /// Obtém containers por imagem
    /// </summary>
    /// <param name="imageName">Nome da imagem</param>
    /// <param name="imageTag">Tag da imagem (opcional)</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de containers da imagem especificada</returns>
    public async Task<IReadOnlyList<Container>> GetByImageAsync(string imageName, ImageTag? imageTag = null, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(imageName))
                return Array.Empty<Container>();

            var query = _dbSet.Where(c => c.ImageName == imageName);

            if (imageTag != null)
            {
                query = query.Where(c => c.ImageTag == imageTag);
            }

            return await query
                .OrderByDescending(c => c.CreatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar containers por imagem {ImageName}:{ImageTag}", 
                imageName, imageTag?.Value ?? "all");
            throw;
        }
    }

    /// <summary>
    /// Obtém containers que expõem uma porta específica
    /// </summary>
    /// <param name="port">Porta a ser verificada</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de containers que expõem a porta</returns>
    public async Task<IReadOnlyList<Container>> GetByExposedPortAsync(Port port, CancellationToken cancellationToken = default)
    {
        try
        {
            // Como as portas são armazenadas como JSON, precisamos fazer uma busca textual
            var portString = port.ToString();
            return await _dbSet
                .Where(c => EF.Functions.Like(c.ExposedPorts.ToString(), $"%{portString}%"))
                .OrderByDescending(c => c.CreatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar containers por porta exposta {Port}", port);
            throw;
        }
    }

    /// <summary>
    /// Obtém containers com uma variável de ambiente específica
    /// </summary>
    /// <param name="key">Chave da variável de ambiente</param>
    /// <param name="value">Valor da variável (opcional)</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de containers com a variável de ambiente</returns>
    public async Task<IReadOnlyList<Container>> GetByEnvironmentVariableAsync(string key, string? value = null, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(key))
                return Array.Empty<Container>();

            var searchPattern = value != null ? $"\"{key}\":\"{value}\"" : $"\"{key}\":";
            
            return await _dbSet
                .Where(c => EF.Functions.Like(c.EnvironmentVariables.ToString(), $"%{searchPattern}%"))
                .OrderByDescending(c => c.CreatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar containers por variável de ambiente {Key}={Value}", key, value ?? "any");
            throw;
        }
    }

    /// <summary>
    /// Obtém containers com um label específico
    /// </summary>
    /// <param name="key">Chave do label</param>
    /// <param name="value">Valor do label (opcional)</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de containers com o label</returns>
    public async Task<IReadOnlyList<Container>> GetByLabelAsync(string key, string? value = null, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(key))
                return Array.Empty<Container>();

            var searchPattern = value != null ? $"\"{key}\":\"{value}\"" : $"\"{key}\":";
            
            return await _dbSet
                .Where(c => EF.Functions.Like(c.Labels.ToString(), $"%{searchPattern}%"))
                .OrderByDescending(c => c.CreatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar containers por label {Key}={Value}", key, value ?? "any");
            throw;
        }
    }

    /// <summary>
    /// Obtém containers em execução
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de containers em execução</returns>
    public async Task<IReadOnlyList<Container>> GetRunningContainersAsync(CancellationToken cancellationToken = default)
    {
        return await GetByStatusAsync(ContainerStatus.Running, cancellationToken);
    }

    /// <summary>
    /// Obtém containers parados
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de containers parados</returns>
    public async Task<IReadOnlyList<Container>> GetStoppedContainersAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _dbSet
                .Where(c => c.Status == ContainerStatus.Exited || c.Status == ContainerStatus.Dead)
                .OrderByDescending(c => c.StoppedAt ?? c.CreatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar containers parados");
            throw;
        }
    }

    /// <summary>
    /// Obtém containers criados por um usuário específico
    /// </summary>
    /// <param name="createdBy">Usuário que criou os containers</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de containers criados pelo usuário</returns>
    public async Task<IReadOnlyList<Container>> GetByCreatedByAsync(string createdBy, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(createdBy))
                return Array.Empty<Container>();

            return await _dbSet
                .Where(c => c.CreatedBy == createdBy)
                .OrderByDescending(c => c.CreatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar containers criados por {CreatedBy}", createdBy);
            throw;
        }
    }

    /// <summary>
    /// Obtém containers criados em um período específico
    /// </summary>
    /// <param name="startDate">Data de início</param>
    /// <param name="endDate">Data de fim</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de containers criados no período</returns>
    public async Task<IReadOnlyList<Container>> GetByCreatedDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _dbSet
                .Where(c => c.CreatedAt >= startDate && c.CreatedAt <= endDate)
                .OrderByDescending(c => c.CreatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar containers por período {StartDate} - {EndDate}", startDate, endDate);
            throw;
        }
    }

    /// <summary>
    /// Verifica se existe um container com o nome especificado
    /// </summary>
    /// <param name="name">Nome do container</param>
    /// <param name="excludeId">ID do container a ser excluído da verificação (opcional)</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se existe um container com o nome</returns>
    public async Task<bool> ExistsByNameAsync(ContainerName name, Guid? excludeId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var query = _dbSet.Where(c => c.Name == name);
            
            if (excludeId.HasValue)
            {
                query = query.Where(c => c.Id != excludeId.Value);
            }

            return await query.AnyAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao verificar existência de container por nome {ContainerName}", name.Value);
            throw;
        }
    }

    /// <summary>
    /// Verifica se existe um container com o ID do runtime especificado
    /// </summary>
    /// <param name="runtimeId">ID do runtime</param>
    /// <param name="excludeId">ID do container a ser excluído da verificação (opcional)</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se existe um container com o ID do runtime</returns>
    public async Task<bool> ExistsByRuntimeIdAsync(string runtimeId, Guid? excludeId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(runtimeId))
                return false;

            var query = _dbSet.Where(c => c.RuntimeId == runtimeId);
            
            if (excludeId.HasValue)
            {
                query = query.Where(c => c.Id != excludeId.Value);
            }

            return await query.AnyAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao verificar existência de container por RuntimeId {RuntimeId}", runtimeId);
            throw;
        }
    }

    /// <summary>
    /// Conta containers por status
    /// </summary>
    /// <param name="status">Status dos containers</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Número de containers com o status</returns>
    public async Task<int> CountByStatusAsync(ContainerStatus status, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _dbSet.CountAsync(c => c.Status == status, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao contar containers por status {Status}", status);
            throw;
        }
    }

    /// <summary>
    /// Conta containers por runtime
    /// </summary>
    /// <param name="runtime">Runtime dos containers</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Número de containers do runtime</returns>
    public async Task<int> CountByRuntimeAsync(ContainerRuntime runtime, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _dbSet.CountAsync(c => c.Runtime == runtime, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao contar containers por runtime {Runtime}", runtime);
            throw;
        }
    }

    /// <summary>
    /// Obtém estatísticas dos containers
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Estatísticas dos containers</returns>
    public async Task<ContainerStatistics> GetStatisticsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var statistics = new ContainerStatistics
            {
                TotalContainers = await _dbSet.CountAsync(cancellationToken),
                RunningContainers = await CountByStatusAsync(ContainerStatus.Running, cancellationToken),
                StoppedContainers = await _dbSet.CountAsync(c => c.Status == ContainerStatus.Exited || c.Status == ContainerStatus.Dead, cancellationToken),
                PausedContainers = await CountByStatusAsync(ContainerStatus.Paused, cancellationToken),
                ErrorContainers = await CountByStatusAsync(ContainerStatus.Dead, cancellationToken),
                DockerContainers = await CountByRuntimeAsync(ContainerRuntime.Docker, cancellationToken),
                PodmanContainers = 0
            };

            // Calcular uso de recursos (simulado por enquanto)
            var containersWithMemoryLimit = await _dbSet
                .Where(c => c.MemoryLimit.HasValue)
                .SumAsync(c => c.MemoryLimit ?? 0, cancellationToken);
            
            var containersWithCpuLimit = await _dbSet
                .Where(c => c.CpuLimit.HasValue)
                .SumAsync(c => c.CpuLimit ?? 0, cancellationToken);

            statistics.TotalMemoryUsage = containersWithMemoryLimit;
            statistics.TotalCpuUsage = (double)containersWithCpuLimit;

            return statistics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter estatísticas de containers");
            throw;
        }
    }

    /// <summary>
    /// Busca containers por texto (nome, imagem, labels, etc.)
    /// </summary>
    /// <param name="searchText">Texto de busca</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de containers que correspondem à busca</returns>
    public async Task<IReadOnlyList<Container>> SearchAsync(string searchText, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(searchText))
                return await GetAllAsync(cancellationToken);

            var searchTerm = $"%{searchText.Trim()}%";

            return await _dbSet
                .Where(c => 
                    EF.Functions.Like(c.Name.Value, searchTerm) ||
                    EF.Functions.Like(c.ImageName, searchTerm) ||
                    EF.Functions.Like(c.ImageTag.Value, searchTerm) ||
                    EF.Functions.Like(c.RuntimeId ?? "", searchTerm) ||
                    EF.Functions.Like(c.CreatedBy, searchTerm))
                .OrderByDescending(c => c.CreatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar containers por texto {SearchText}", searchText);
            throw;
        }
    }

    /// <summary>
    /// Busca containers paginada por texto
    /// </summary>
    /// <param name="searchText">Texto de busca</param>
    /// <param name="pageNumber">Número da página</param>
    /// <param name="pageSize">Tamanho da página</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Resultado paginado da busca</returns>
    public async Task<PagedResult<Container>> SearchPagedAsync(string searchText, int pageNumber, int pageSize, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(searchText))
                return await GetPagedAsync(pageNumber, pageSize, cancellationToken);

            var searchTerm = $"%{searchText.Trim()}%";

            var query = _dbSet
                .Where(c => 
                    EF.Functions.Like(c.Name.Value, searchTerm) ||
                    EF.Functions.Like(c.ImageName, searchTerm) ||
                    EF.Functions.Like(c.ImageTag.Value, searchTerm) ||
                    EF.Functions.Like(c.RuntimeId ?? "", searchTerm) ||
                    EF.Functions.Like(c.CreatedBy, searchTerm))
                .OrderByDescending(c => c.CreatedAt);

            var totalCount = await query.CountAsync(cancellationToken);
            var items = await query
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync(cancellationToken);

            return new PagedResult<Container>(items, pageNumber, pageSize, totalCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar containers paginados por texto {SearchText}", searchText);
            throw;
        }
    }
}
