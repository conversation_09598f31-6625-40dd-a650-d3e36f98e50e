<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <RootNamespace>AutoInstaller.UI.Tests</RootNamespace>
    <AssemblyName>AutoInstaller.UI.Tests</AssemblyName>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\AutoInstaller.Core\AutoInstaller.Core.csproj" />
    <ProjectReference Include="..\..\src\AutoInstaller.Application\AutoInstaller.Application.csproj" />
    <ProjectReference Include="..\..\src\AutoInstaller.Infrastructure\AutoInstaller.Infrastructure.csproj" />
    <ProjectReference Include="..\..\src\AutoInstaller.UI\AutoInstaller.UI.csproj" />
    <ProjectReference Include="..\..\src\AutoInstaller.Shared\AutoInstaller.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="xunit" Version="2.9.2" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.8.2" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.12.0" />
    <PackageReference Include="FluentAssertions" Version="6.12.2" />
    <PackageReference Include="Moq" Version="4.20.72" />
    <PackageReference Include="AutoFixture" Version="4.18.1" />
    <PackageReference Include="AutoFixture.Xunit2" Version="4.18.1" />
    <PackageReference Include="coverlet.collector" Version="6.0.2" />
    <PackageReference Include="coverlet.msbuild" Version="6.0.2" />

    <!-- Avalonia Testing -->
    <PackageReference Include="Avalonia.Headless" Version="11.3.4" />
    <PackageReference Include="Avalonia.Headless.XUnit" Version="11.3.4" />
  </ItemGroup>

</Project>
