using AutoInstaller.Core.Interfaces;
using AutoInstaller.Core.ValueObjects;
using AutoInstaller.Core.Entities;

namespace AutoInstaller.Core.Events;

/// <summary>
/// Evento disparado quando um container é criado
/// </summary>
public sealed class ContainerCreatedEvent : DomainEvent
{
    /// <summary>
    /// ID do container criado
    /// </summary>
    public Guid ContainerId { get; }

    /// <summary>
    /// Nome do container
    /// </summary>
    public ContainerName ContainerName { get; }

    /// <summary>
    /// Nome da imagem
    /// </summary>
    public string ImageName { get; }

    /// <summary>
    /// Tag da imagem
    /// </summary>
    public ImageTag ImageTag { get; }

    /// <summary>
    /// Runtime utilizado
    /// </summary>
    public ContainerRuntime Runtime { get; }

    /// <summary>
    /// Construtor
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="containerName">Nome do container</param>
    /// <param name="imageName">Nome da imagem</param>
    /// <param name="imageTag">Tag da imagem</param>
    /// <param name="runtime">Runtime utilizado</param>
    public ContainerCreatedEvent(Guid containerId, ContainerName containerName, 
        string imageName, ImageTag imageTag, ContainerRuntime runtime)
    {
        ContainerId = containerId;
        ContainerName = containerName;
        ImageName = imageName;
        ImageTag = imageTag;
        Runtime = runtime;
    }
}

/// <summary>
/// Evento disparado quando o status de um container muda
/// </summary>
public sealed class ContainerStatusChangedEvent : DomainEvent
{
    /// <summary>
    /// ID do container
    /// </summary>
    public Guid ContainerId { get; }

    /// <summary>
    /// Status anterior
    /// </summary>
    public ContainerStatus PreviousStatus { get; }

    /// <summary>
    /// Novo status
    /// </summary>
    public ContainerStatus NewStatus { get; }

    /// <summary>
    /// Construtor
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="previousStatus">Status anterior</param>
    /// <param name="newStatus">Novo status</param>
    public ContainerStatusChangedEvent(Guid containerId, ContainerStatus previousStatus, ContainerStatus newStatus)
    {
        ContainerId = containerId;
        PreviousStatus = previousStatus;
        NewStatus = newStatus;
    }
}

/// <summary>
/// Evento disparado quando um container é iniciado
/// </summary>
public sealed class ContainerStartedEvent : DomainEvent
{
    /// <summary>
    /// ID do container
    /// </summary>
    public Guid ContainerId { get; }

    /// <summary>
    /// ID do runtime
    /// </summary>
    public string RuntimeId { get; }

    /// <summary>
    /// Data de início
    /// </summary>
    public DateTime StartedAt { get; }

    /// <summary>
    /// Construtor
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="runtimeId">ID do runtime</param>
    /// <param name="startedAt">Data de início</param>
    public ContainerStartedEvent(Guid containerId, string runtimeId, DateTime startedAt)
    {
        ContainerId = containerId;
        RuntimeId = runtimeId;
        StartedAt = startedAt;
    }
}

/// <summary>
/// Evento disparado quando um container é parado
/// </summary>
public sealed class ContainerStoppedEvent : DomainEvent
{
    /// <summary>
    /// ID do container
    /// </summary>
    public Guid ContainerId { get; }

    /// <summary>
    /// Data de parada
    /// </summary>
    public DateTime StoppedAt { get; }

    /// <summary>
    /// Código de saída
    /// </summary>
    public int? ExitCode { get; }

    /// <summary>
    /// Motivo da parada
    /// </summary>
    public string? Reason { get; }

    /// <summary>
    /// Construtor
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="stoppedAt">Data de parada</param>
    /// <param name="exitCode">Código de saída</param>
    /// <param name="reason">Motivo da parada</param>
    public ContainerStoppedEvent(Guid containerId, DateTime stoppedAt, int? exitCode = null, string? reason = null)
    {
        ContainerId = containerId;
        StoppedAt = stoppedAt;
        ExitCode = exitCode;
        Reason = reason;
    }
}

/// <summary>
/// Evento disparado quando um container é removido
/// </summary>
public sealed class ContainerRemovedEvent : DomainEvent
{
    /// <summary>
    /// ID do container
    /// </summary>
    public Guid ContainerId { get; }

    /// <summary>
    /// Nome do container
    /// </summary>
    public ContainerName ContainerName { get; }

    /// <summary>
    /// Se foi forçada a remoção
    /// </summary>
    public bool ForceRemoved { get; }

    /// <summary>
    /// Construtor
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="containerName">Nome do container</param>
    /// <param name="forceRemoved">Se foi forçada a remoção</param>
    public ContainerRemovedEvent(Guid containerId, ContainerName containerName, bool forceRemoved = false)
    {
        ContainerId = containerId;
        ContainerName = containerName;
        ForceRemoved = forceRemoved;
    }
}

/// <summary>
/// Evento disparado quando recursos de um container são atualizados
/// </summary>
public sealed class ContainerResourcesUpdatedEvent : DomainEvent
{
    /// <summary>
    /// ID do container
    /// </summary>
    public Guid ContainerId { get; }

    /// <summary>
    /// Limite de memória anterior
    /// </summary>
    public long? PreviousMemoryLimit { get; }

    /// <summary>
    /// Novo limite de memória
    /// </summary>
    public long? NewMemoryLimit { get; }

    /// <summary>
    /// Limite de CPU anterior
    /// </summary>
    public double? PreviousCpuLimit { get; }

    /// <summary>
    /// Novo limite de CPU
    /// </summary>
    public double? NewCpuLimit { get; }

    /// <summary>
    /// Construtor
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="previousMemoryLimit">Limite de memória anterior</param>
    /// <param name="newMemoryLimit">Novo limite de memória</param>
    /// <param name="previousCpuLimit">Limite de CPU anterior</param>
    /// <param name="newCpuLimit">Novo limite de CPU</param>
    public ContainerResourcesUpdatedEvent(Guid containerId, long? previousMemoryLimit, long? newMemoryLimit,
        double? previousCpuLimit, double? newCpuLimit)
    {
        ContainerId = containerId;
        PreviousMemoryLimit = previousMemoryLimit;
        NewMemoryLimit = newMemoryLimit;
        PreviousCpuLimit = previousCpuLimit;
        NewCpuLimit = newCpuLimit;
    }
}

/// <summary>
/// Evento disparado quando portas de um container são modificadas
/// </summary>
public sealed class ContainerPortsUpdatedEvent : DomainEvent
{
    /// <summary>
    /// ID do container
    /// </summary>
    public Guid ContainerId { get; }

    /// <summary>
    /// Portas adicionadas
    /// </summary>
    public IReadOnlyList<Port> AddedPorts { get; }

    /// <summary>
    /// Portas removidas
    /// </summary>
    public IReadOnlyList<Port> RemovedPorts { get; }

    /// <summary>
    /// Construtor
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="addedPorts">Portas adicionadas</param>
    /// <param name="removedPorts">Portas removidas</param>
    public ContainerPortsUpdatedEvent(Guid containerId, IReadOnlyList<Port> addedPorts, IReadOnlyList<Port> removedPorts)
    {
        ContainerId = containerId;
        AddedPorts = addedPorts;
        RemovedPorts = removedPorts;
    }
}
