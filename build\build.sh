#!/bin/bash

# Script de build para Auto-Instalador Desktop (Linux/macOS)
# Uso: ./build.sh [target] [configuration] [runtime]

set -euo pipefail

# Configurações padrão
TARGET="${1:-Build}"
CONFIGURATION="${2:-Debug}"
RUNTIME="${3:-linux-x64}"
SKIP_TESTS="${SKIP_TESTS:-false}"
VERBOSE="${VERBOSE:-false}"

# Diretórios
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROOT_DIR="$(dirname "$SCRIPT_DIR")"
SRC_DIR="$ROOT_DIR/src"
TESTS_DIR="$ROOT_DIR/tests"
ARTIFACTS_DIR="$ROOT_DIR/artifacts"
PUBLISH_DIR="$ARTIFACTS_DIR/publish"
PACKAGES_DIR="$ARTIFACTS_DIR/packages"
COVERAGE_DIR="$ARTIFACTS_DIR/coverage"

MAIN_PROJECT="$SRC_DIR/AutoInstaller.UI/AutoInstaller.UI.csproj"
SOLUTION_FILE="$ROOT_DIR/AutoInstaller.sln"

# Cores para output
COLOR_RESET='\033[0m'
COLOR_GREEN='\033[32m'
COLOR_YELLOW='\033[33m'
COLOR_RED='\033[31m'
COLOR_BLUE='\033[34m'

# Funções de logging
log_info() {
    echo -e "${COLOR_BLUE}[INFO]${COLOR_RESET} $1"
}

log_success() {
    echo -e "${COLOR_GREEN}[SUCCESS]${COLOR_RESET} $1"
}

log_warning() {
    echo -e "${COLOR_YELLOW}[WARNING]${COLOR_RESET} $1"
}

log_error() {
    echo -e "${COLOR_RED}[ERROR]${COLOR_RESET} $1"
}

# Verificar se .NET está instalado
check_dotnet() {
    if ! command -v dotnet &> /dev/null; then
        log_error ".NET SDK não encontrado. Instale o .NET 9 SDK."
        exit 1
    fi
    
    local version
    version=$(dotnet --version)
    log_info ".NET SDK versão: $version"
}

# Função de limpeza
clean() {
    log_info "Limpando artefatos..."
    
    if [ -d "$ARTIFACTS_DIR" ]; then
        rm -rf "$ARTIFACTS_DIR"
    fi
    
    # Limpar bin e obj
    find "$ROOT_DIR" -type d \( -name "bin" -o -name "obj" \) -exec rm -rf {} + 2>/dev/null || true
    
    log_success "Limpeza concluída"
}

# Restaurar dependências
restore() {
    log_info "Restaurando dependências..."
    
    local verbosity="minimal"
    if [ "$VERBOSE" = "true" ]; then
        verbosity="normal"
    fi
    
    dotnet restore "$SOLUTION_FILE" --verbosity "$verbosity"
    
    log_success "Dependências restauradas"
}

# Compilar solução
build() {
    log_info "Compilando solução..."
    
    local verbosity="minimal"
    if [ "$VERBOSE" = "true" ]; then
        verbosity="normal"
    fi
    
    dotnet build "$SOLUTION_FILE" \
        --configuration "$CONFIGURATION" \
        --no-restore \
        --verbosity "$verbosity"
    
    log_success "Compilação concluída"
}

# Executar testes
test() {
    if [ "$SKIP_TESTS" = "true" ]; then
        log_warning "Testes pulados conforme solicitado"
        return
    fi
    
    log_info "Executando testes..."
    
    # Criar diretório de cobertura
    mkdir -p "$COVERAGE_DIR"
    
    local verbosity="minimal"
    if [ "$VERBOSE" = "true" ]; then
        verbosity="normal"
    fi
    
    dotnet test "$SOLUTION_FILE" \
        --configuration "$CONFIGURATION" \
        --no-build \
        --collect:"XPlat Code Coverage" \
        --results-directory "$COVERAGE_DIR" \
        --logger trx \
        --verbosity "$verbosity"
    
    # Gerar relatório de cobertura se disponível
    local coverage_files
    coverage_files=$(find "$COVERAGE_DIR" -name "coverage.cobertura.xml" 2>/dev/null || true)
    
    if [ -n "$coverage_files" ]; then
        log_info "Gerando relatório de cobertura..."
        
        if command -v reportgenerator &> /dev/null || dotnet tool list -g | grep -q reportgenerator; then
            local report_dir="$COVERAGE_DIR/report"
            mkdir -p "$report_dir"
            
            dotnet tool run reportgenerator \
                --reports:"$coverage_files" \
                --targetdir:"$report_dir" \
                --reporttypes:"Html;Cobertura" 2>/dev/null || true
            
            log_success "Relatório de cobertura gerado em: $report_dir"
        else
            log_warning "ReportGenerator não encontrado. Instale com: dotnet tool install -g dotnet-reportgenerator-globaltool"
        fi
    fi
    
    log_success "Testes concluídos"
}

# Empacotar projetos
pack() {
    log_info "Empacotando projetos..."
    
    # Criar diretório de pacotes
    mkdir -p "$PACKAGES_DIR"
    
    local verbosity="minimal"
    if [ "$VERBOSE" = "true" ]; then
        verbosity="normal"
    fi
    
    dotnet pack "$SOLUTION_FILE" \
        --configuration "$CONFIGURATION" \
        --no-build \
        --output "$PACKAGES_DIR" \
        --verbosity "$verbosity"
    
    log_success "Empacotamento concluído"
}

# Publicar aplicação
publish() {
    local target_runtime="$1"
    
    log_info "Publicando aplicação para $target_runtime..."
    
    local output_dir="$PUBLISH_DIR/$target_runtime"
    mkdir -p "$output_dir"
    
    local verbosity="minimal"
    if [ "$VERBOSE" = "true" ]; then
        verbosity="normal"
    fi
    
    dotnet publish "$MAIN_PROJECT" \
        --configuration "$CONFIGURATION" \
        --runtime "$target_runtime" \
        --self-contained true \
        --output "$output_dir" \
        -p:PublishSingleFile=true \
        -p:PublishTrimmed=true \
        -p:TrimMode=link \
        -p:EnableCompressionInSingleFile=true \
        -p:IncludeNativeLibrariesForSelfExtract=true \
        --verbosity "$verbosity"
    
    # Criar pacote de distribuição
    local version
    version=$(date +"%Y.%m.%d")
    local package_name="AutoInstaller-$target_runtime-$version"
    local package_dir="$PUBLISH_DIR/$package_name"
    
    # Copiar arquivos
    mkdir -p "$package_dir"
    cp -r "$output_dir"/* "$package_dir/"
    
    # Tornar executável executável
    if [ -f "$package_dir/AutoInstaller.UI" ]; then
        chmod +x "$package_dir/AutoInstaller.UI"
    fi
    
    # Copiar arquivos adicionais
    if [ -f "$ROOT_DIR/README.md" ]; then
        cp "$ROOT_DIR/README.md" "$package_dir/"
    fi
    
    if [ -f "$ROOT_DIR/LICENSE" ]; then
        cp "$ROOT_DIR/LICENSE" "$package_dir/"
    fi
    
    # Criar arquivo TAR.GZ
    local tar_path="$PUBLISH_DIR/$package_name.tar.gz"
    (cd "$PUBLISH_DIR" && tar -czf "$package_name.tar.gz" "$package_name")
    
    log_success "Publicação concluída: $tar_path"
}

# Publicar para todas as plataformas
publish_all() {
    local runtimes=("win-x64" "linux-x64" "osx-x64")
    
    for runtime in "${runtimes[@]}"; do
        if ! publish "$runtime"; then
            log_error "Falha na publicação para $runtime"
        fi
    done
}

# Mostrar resumo
show_summary() {
    log_info "=== RESUMO DO BUILD ==="
    log_info "Target: $TARGET"
    log_info "Configuration: $CONFIGURATION"
    log_info "Runtime: $RUNTIME"
    log_info "Skip Tests: $SKIP_TESTS"
    
    if [ -d "$ARTIFACTS_DIR" ]; then
        local artifact_count
        artifact_count=$(find "$ARTIFACTS_DIR" -type f | wc -l)
        log_info "Artefatos gerados: $artifact_count arquivos"
        
        if [ "$artifact_count" -gt 0 ]; then
            log_info "Localização dos artefatos: $ARTIFACTS_DIR"
        fi
    fi
    
    log_success "Build concluído com sucesso!"
}

# Mostrar ajuda
show_help() {
    echo "Auto-Instalador Desktop - Script de Build"
    echo ""
    echo "Uso: $0 [target] [configuration] [runtime]"
    echo ""
    echo "Targets disponíveis:"
    echo "  Build     - Compilar solução (padrão)"
    echo "  Test      - Compilar e executar testes"
    echo "  Pack      - Compilar, testar e empacotar"
    echo "  Publish   - Compilar, testar e publicar"
    echo "  Clean     - Limpar artefatos"
    echo "  All       - Executar todos os targets"
    echo ""
    echo "Configurações:"
    echo "  Debug     - Configuração de debug (padrão)"
    echo "  Release   - Configuração de release"
    echo ""
    echo "Runtimes:"
    echo "  linux-x64 - Linux x64 (padrão)"
    echo "  osx-x64   - macOS x64"
    echo "  win-x64   - Windows x64"
    echo "  All       - Todas as plataformas"
    echo ""
    echo "Variáveis de ambiente:"
    echo "  SKIP_TESTS=true   - Pular execução de testes"
    echo "  VERBOSE=true      - Saída verbosa"
    echo ""
    echo "Exemplos:"
    echo "  $0 All Release All"
    echo "  $0 Publish Release linux-x64"
    echo "  SKIP_TESTS=true $0 Build Release"
}

# Função principal
main() {
    # Verificar se é pedido de ajuda
    if [ "${1:-}" = "--help" ] || [ "${1:-}" = "-h" ]; then
        show_help
        exit 0
    fi
    
    log_info "=== AUTO-INSTALADOR DESKTOP - BUILD SCRIPT ==="
    log_info "Iniciando build com target: $TARGET"
    
    # Verificar pré-requisitos
    check_dotnet
    
    # Executar targets
    case "$TARGET" in
        "Clean")
            clean
            ;;
        "Build")
            restore
            build
            ;;
        "Test")
            restore
            build
            test
            ;;
        "Pack")
            restore
            build
            test
            pack
            ;;
        "Publish")
            restore
            build
            test
            
            if [ "$RUNTIME" = "All" ]; then
                publish_all
            else
                publish "$RUNTIME"
            fi
            ;;
        "All")
            clean
            restore
            build
            test
            pack
            
            if [ "$RUNTIME" = "All" ]; then
                publish_all
            else
                publish "$RUNTIME"
            fi
            ;;
        *)
            log_error "Target inválido: $TARGET"
            show_help
            exit 1
            ;;
    esac
    
    show_summary
}

# Executar função principal
main "$@"
