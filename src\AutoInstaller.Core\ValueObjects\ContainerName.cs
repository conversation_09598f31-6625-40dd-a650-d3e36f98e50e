using AutoInstaller.Core.Common;
using AutoInstaller.Core.Exceptions;
using System.Text.RegularExpressions;

namespace AutoInstaller.Core.ValueObjects;

/// <summary>
/// Value Object para nome de container
/// </summary>
public sealed class ContainerName : ValueObject
{
    private static readonly Regex ValidNameRegex = new(@"^[a-zA-Z0-9][a-zA-Z0-9_.-]*$", RegexOptions.Compiled);
    private const int MaxLength = 253;
    private const int MinLength = 1;

    /// <summary>
    /// Valor do nome do container
    /// </summary>
    public string Value { get; private set; }

    /// <summary>
    /// Construtor privado
    /// </summary>
    /// <param name="value">Nome do container</param>
    private ContainerName(string value)
    {
        Value = value;
    }

    /// <summary>
    /// Cria um novo ContainerName
    /// </summary>
    /// <param name="value">Nome do container</param>
    /// <returns>ContainerName válido</returns>
    /// <exception cref="ValueObjectValidationException">Quando o nome é inválido</exception>
    public static ContainerName Create(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            throw new ValueObjectValidationException(nameof(ContainerName), value ?? "null", "Nome não pode ser vazio");

        value = value.Trim();

        if (value.Length < MinLength || value.Length > MaxLength)
            throw new ValueObjectValidationException(nameof(ContainerName), value, 
                $"Nome deve ter entre {MinLength} e {MaxLength} caracteres");

        if (!ValidNameRegex.IsMatch(value))
            throw new ValueObjectValidationException(nameof(ContainerName), value, 
                "Nome deve começar com letra ou número e conter apenas letras, números, underscore, ponto ou hífen");

        if (value.StartsWith('-') || value.EndsWith('-'))
            throw new ValueObjectValidationException(nameof(ContainerName), value, 
                "Nome não pode começar ou terminar com hífen");

        if (value.StartsWith('.') || value.EndsWith('.'))
            throw new ValueObjectValidationException(nameof(ContainerName), value, 
                "Nome não pode começar ou terminar com ponto");

        if (value.Contains(".."))
            throw new ValueObjectValidationException(nameof(ContainerName), value, 
                "Nome não pode conter pontos consecutivos");

        return new ContainerName(value);
    }

    /// <summary>
    /// Tenta criar um ContainerName
    /// </summary>
    /// <param name="value">Nome do container</param>
    /// <param name="containerName">ContainerName criado</param>
    /// <returns>True se válido, false caso contrário</returns>
    public static bool TryCreate(string value, out ContainerName? containerName)
    {
        try
        {
            containerName = Create(value);
            return true;
        }
        catch (ValueObjectValidationException)
        {
            containerName = null;
            return false;
        }
    }

    /// <summary>
    /// Verifica se um nome é válido
    /// </summary>
    /// <param name="value">Nome a ser validado</param>
    /// <returns>True se válido</returns>
    public static bool IsValid(string value)
    {
        return TryCreate(value, out _);
    }

    /// <summary>
    /// Gera um nome único baseado em um prefixo
    /// </summary>
    /// <param name="prefix">Prefixo do nome</param>
    /// <returns>ContainerName único</returns>
    public static ContainerName GenerateUnique(string prefix = "container")
    {
        if (string.IsNullOrWhiteSpace(prefix))
            prefix = "container";

        var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
        var random = Random.Shared.Next(1000, 9999);
        var uniqueName = $"{prefix}-{timestamp}-{random}";

        return Create(uniqueName);
    }

    /// <summary>
    /// Retorna os componentes para comparação
    /// </summary>
    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Value.ToLowerInvariant(); // Case-insensitive comparison
    }

    /// <summary>
    /// Conversão implícita para string
    /// </summary>
    public static implicit operator string(ContainerName containerName)
    {
        return containerName.Value;
    }

    /// <summary>
    /// Conversão explícita de string
    /// </summary>
    public static explicit operator ContainerName(string value)
    {
        return Create(value);
    }

    /// <summary>
    /// Representação string
    /// </summary>
    public override string ToString()
    {
        return Value;
    }
}
