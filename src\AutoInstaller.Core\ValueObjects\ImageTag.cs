using AutoInstaller.Core.Common;
using AutoInstaller.Core.Exceptions;
using System.Text.RegularExpressions;

namespace AutoInstaller.Core.ValueObjects;

/// <summary>
/// Value Object para tag de imagem Docker/Podman
/// </summary>
public sealed class ImageTag : ValueObject
{
    private static readonly Regex ValidTagRegex = new(@"^[a-zA-Z0-9_][a-zA-Z0-9_.-]*$", RegexOptions.Compiled);
    private const int MaxLength = 128;
    private const int MinLength = 1;
    private const string DefaultTag = "latest";

    /// <summary>
    /// Valor da tag
    /// </summary>
    public string Value { get; private set; }

    /// <summary>
    /// Indica se é a tag padrão (latest)
    /// </summary>
    public bool IsLatest => string.Equals(Value, DefaultTag, StringComparison.OrdinalIgnoreCase);

    /// <summary>
    /// Indica se é uma tag de versão semântica
    /// </summary>
    public bool IsSemanticVersion => SemanticVersionRegex.IsMatch(Value);

    /// <summary>
    /// Indica se é uma tag de desenvolvimento
    /// </summary>
    public bool IsDevelopmentTag => DevelopmentTags.Contains(Value.ToLowerInvariant());

    private static readonly Regex SemanticVersionRegex = new(
        @"^v?(\d+)\.(\d+)\.(\d+)(?:-([0-9A-Za-z-]+(?:\.[0-9A-Za-z-]+)*))?(?:\+([0-9A-Za-z-]+(?:\.[0-9A-Za-z-]+)*))?$",
        RegexOptions.Compiled);

    private static readonly HashSet<string> DevelopmentTags = new(StringComparer.OrdinalIgnoreCase)
    {
        "dev", "develop", "development", "alpha", "beta", "rc", "snapshot", "nightly", "edge", "canary"
    };

    /// <summary>
    /// Construtor privado
    /// </summary>
    /// <param name="value">Valor da tag</param>
    private ImageTag(string value)
    {
        Value = value;
    }

    /// <summary>
    /// Cria uma nova ImageTag
    /// </summary>
    /// <param name="value">Valor da tag</param>
    /// <returns>ImageTag válida</returns>
    /// <exception cref="ValueObjectValidationException">Quando a tag é inválida</exception>
    public static ImageTag Create(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            return CreateLatest();

        value = value.Trim();

        if (value.Length < MinLength || value.Length > MaxLength)
            throw new ValueObjectValidationException(nameof(ImageTag), value,
                $"Tag deve ter entre {MinLength} e {MaxLength} caracteres");

        if (!ValidTagRegex.IsMatch(value))
            throw new ValueObjectValidationException(nameof(ImageTag), value,
                "Tag deve começar com letra, número ou underscore e conter apenas letras, números, underscore, ponto ou hífen");

        if (value.StartsWith('.') || value.EndsWith('.'))
            throw new ValueObjectValidationException(nameof(ImageTag), value,
                "Tag não pode começar ou terminar com ponto");

        if (value.StartsWith('-') || value.EndsWith('-'))
            throw new ValueObjectValidationException(nameof(ImageTag), value,
                "Tag não pode começar ou terminar com hífen");

        if (value.Contains("..") || value.Contains("--"))
            throw new ValueObjectValidationException(nameof(ImageTag), value,
                "Tag não pode conter pontos ou hífens consecutivos");

        return new ImageTag(value);
    }

    /// <summary>
    /// Cria a tag padrão (latest)
    /// </summary>
    /// <returns>ImageTag latest</returns>
    public static ImageTag CreateLatest()
    {
        return new ImageTag(DefaultTag);
    }

    /// <summary>
    /// Cria uma tag de versão semântica
    /// </summary>
    /// <param name="major">Versão major</param>
    /// <param name="minor">Versão minor</param>
    /// <param name="patch">Versão patch</param>
    /// <param name="preRelease">Pré-release (opcional)</param>
    /// <param name="build">Build metadata (opcional)</param>
    /// <returns>ImageTag com versão semântica</returns>
    public static ImageTag CreateSemanticVersion(int major, int minor, int patch, 
        string? preRelease = null, string? build = null)
    {
        if (major < 0 || minor < 0 || patch < 0)
            throw new ValueObjectValidationException(nameof(ImageTag), $"{major}.{minor}.{patch}",
                "Números de versão não podem ser negativos");

        var version = $"{major}.{minor}.{patch}";

        if (!string.IsNullOrWhiteSpace(preRelease))
        {
            version += $"-{preRelease.Trim()}";
        }

        if (!string.IsNullOrWhiteSpace(build))
        {
            version += $"+{build.Trim()}";
        }

        return Create(version);
    }

    /// <summary>
    /// Tenta criar uma ImageTag
    /// </summary>
    /// <param name="value">Valor da tag</param>
    /// <param name="imageTag">ImageTag criada</param>
    /// <returns>True se válida</returns>
    public static bool TryCreate(string value, out ImageTag? imageTag)
    {
        try
        {
            imageTag = Create(value);
            return true;
        }
        catch (ValueObjectValidationException)
        {
            imageTag = null;
            return false;
        }
    }

    /// <summary>
    /// Verifica se uma tag é válida
    /// </summary>
    /// <param name="value">Valor da tag</param>
    /// <returns>True se válida</returns>
    public static bool IsValid(string value)
    {
        return TryCreate(value, out _);
    }

    /// <summary>
    /// Extrai informações de versão semântica
    /// </summary>
    /// <returns>Tupla com major, minor, patch e preRelease ou null se não for semântica</returns>
    public (int Major, int Minor, int Patch, string? PreRelease)? GetSemanticVersion()
    {
        if (!IsSemanticVersion)
            return null;

        var match = SemanticVersionRegex.Match(Value);
        if (!match.Success)
            return null;

        var major = int.Parse(match.Groups[1].Value);
        var minor = int.Parse(match.Groups[2].Value);
        var patch = int.Parse(match.Groups[3].Value);
        var preRelease = match.Groups[4].Success ? match.Groups[4].Value : null;

        return (major, minor, patch, preRelease);
    }

    /// <summary>
    /// Compara versões semânticas
    /// </summary>
    /// <param name="other">Outra ImageTag</param>
    /// <returns>-1 se menor, 0 se igual, 1 se maior, null se não forem versões semânticas</returns>
    public int? CompareSemanticVersion(ImageTag other)
    {
        var thisVersion = GetSemanticVersion();
        var otherVersion = other.GetSemanticVersion();

        if (thisVersion == null || otherVersion == null)
            return null;

        // Compara major
        if (thisVersion.Value.Major != otherVersion.Value.Major)
            return thisVersion.Value.Major.CompareTo(otherVersion.Value.Major);

        // Compara minor
        if (thisVersion.Value.Minor != otherVersion.Value.Minor)
            return thisVersion.Value.Minor.CompareTo(otherVersion.Value.Minor);

        // Compara patch
        if (thisVersion.Value.Patch != otherVersion.Value.Patch)
            return thisVersion.Value.Patch.CompareTo(otherVersion.Value.Patch);

        // Compara pre-release
        if (thisVersion.Value.PreRelease == null && otherVersion.Value.PreRelease == null)
            return 0;
        if (thisVersion.Value.PreRelease == null)
            return 1; // Versão sem pre-release é maior
        if (otherVersion.Value.PreRelease == null)
            return -1; // Versão sem pre-release é maior

        return string.Compare(thisVersion.Value.PreRelease, otherVersion.Value.PreRelease, StringComparison.OrdinalIgnoreCase);
    }

    /// <summary>
    /// Tags comuns pré-definidas
    /// </summary>
    public static class Common
    {
        public static readonly ImageTag Latest = CreateLatest();
        public static readonly ImageTag Stable = Create("stable");
        public static readonly ImageTag Dev = Create("dev");
        public static readonly ImageTag Alpha = Create("alpha");
        public static readonly ImageTag Beta = Create("beta");
        public static readonly ImageTag RC = Create("rc");
        public static readonly ImageTag Nightly = Create("nightly");
        public static readonly ImageTag Edge = Create("edge");
    }

    /// <summary>
    /// Retorna os componentes para comparação
    /// </summary>
    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Value.ToLowerInvariant(); // Case-insensitive comparison
    }

    /// <summary>
    /// Conversão implícita para string
    /// </summary>
    public static implicit operator string(ImageTag imageTag)
    {
        return imageTag.Value;
    }

    /// <summary>
    /// Conversão explícita de string
    /// </summary>
    public static explicit operator ImageTag(string value)
    {
        return Create(value);
    }

    /// <summary>
    /// Representação string
    /// </summary>
    public override string ToString()
    {
        return Value;
    }
}
