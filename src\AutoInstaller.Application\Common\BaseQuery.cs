using MediatR;

namespace AutoInstaller.Application.Common;

/// <summary>
/// Interface base para queries
/// </summary>
/// <typeparam name="TResponse">Tipo da resposta</typeparam>
public interface IQuery<out TResponse> : IRequest<TResponse>
{
}

/// <summary>
/// Interface base para handlers de queries
/// </summary>
/// <typeparam name="TQuery">Tipo da query</typeparam>
/// <typeparam name="TResponse">Tipo da resposta</typeparam>
public interface IQueryHandler<in TQuery, TResponse> : IRequestHandler<TQuery, TResponse>
    where TQuery : IQuery<TResponse>
{
}

/// <summary>
/// Classe base abstrata para queries
/// </summary>
/// <typeparam name="TResponse">Tipo da resposta</typeparam>
public abstract class BaseQuery<TResponse> : IQuery<TResponse>
{
    /// <summary>
    /// Usuário que está executando a query
    /// </summary>
    public string ExecutedBy { get; set; } = string.Empty;

    /// <summary>
    /// Timestamp da execução
    /// </summary>
    public DateTime ExecutedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// ID de correlação para rastreamento
    /// </summary>
    public string CorrelationId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// Metadados adicionais
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// Construtor protegido
    /// </summary>
    protected BaseQuery()
    {
    }

    /// <summary>
    /// Construtor com usuário executor
    /// </summary>
    /// <param name="executedBy">Usuário que executa a query</param>
    protected BaseQuery(string executedBy)
    {
        if (string.IsNullOrWhiteSpace(executedBy))
            throw new ArgumentException("Usuário executor não pode ser vazio", nameof(executedBy));

        ExecutedBy = executedBy.Trim();
    }

    /// <summary>
    /// Adiciona metadados à query
    /// </summary>
    /// <param name="key">Chave do metadado</param>
    /// <param name="value">Valor do metadado</param>
    public void AddMetadata(string key, object value)
    {
        if (string.IsNullOrWhiteSpace(key))
            throw new ArgumentException("Chave do metadado não pode ser vazia", nameof(key));

        Metadata[key.Trim()] = value;
    }

    /// <summary>
    /// Obtém um metadado
    /// </summary>
    /// <typeparam name="T">Tipo do metadado</typeparam>
    /// <param name="key">Chave do metadado</param>
    /// <returns>Valor do metadado ou default(T)</returns>
    public T? GetMetadata<T>(string key)
    {
        if (string.IsNullOrWhiteSpace(key) || !Metadata.TryGetValue(key.Trim(), out var value))
            return default;

        if (value is T typedValue)
            return typedValue;

        try
        {
            return (T)Convert.ChangeType(value, typeof(T));
        }
        catch
        {
            return default;
        }
    }
}

/// <summary>
/// Query base para consultas paginadas
/// </summary>
/// <typeparam name="TResponse">Tipo da resposta</typeparam>
public abstract class BasePagedQuery<TResponse> : BaseQuery<TResponse>
{
    private int _pageNumber = 1;
    private int _pageSize = 10;

    /// <summary>
    /// Número da página (1-based)
    /// </summary>
    public int PageNumber
    {
        get => _pageNumber;
        set => _pageNumber = value < 1 ? 1 : value;
    }

    /// <summary>
    /// Tamanho da página
    /// </summary>
    public int PageSize
    {
        get => _pageSize;
        set => _pageSize = value switch
        {
            < 1 => 10,
            > 100 => 100, // Limite máximo para evitar sobrecarga
            _ => value
        };
    }

    /// <summary>
    /// Número de itens a pular
    /// </summary>
    public int Skip => (PageNumber - 1) * PageSize;

    /// <summary>
    /// Construtor protegido
    /// </summary>
    protected BasePagedQuery()
    {
    }

    /// <summary>
    /// Construtor com usuário executor
    /// </summary>
    /// <param name="executedBy">Usuário que executa a query</param>
    protected BasePagedQuery(string executedBy) : base(executedBy)
    {
    }

    /// <summary>
    /// Construtor com paginação
    /// </summary>
    /// <param name="pageNumber">Número da página</param>
    /// <param name="pageSize">Tamanho da página</param>
    protected BasePagedQuery(int pageNumber, int pageSize)
    {
        PageNumber = pageNumber;
        PageSize = pageSize;
    }

    /// <summary>
    /// Construtor completo
    /// </summary>
    /// <param name="executedBy">Usuário que executa a query</param>
    /// <param name="pageNumber">Número da página</param>
    /// <param name="pageSize">Tamanho da página</param>
    protected BasePagedQuery(string executedBy, int pageNumber, int pageSize) : base(executedBy)
    {
        PageNumber = pageNumber;
        PageSize = pageSize;
    }
}

/// <summary>
/// Query base para consultas com filtros de texto
/// </summary>
/// <typeparam name="TResponse">Tipo da resposta</typeparam>
public abstract class BaseSearchQuery<TResponse> : BasePagedQuery<TResponse>
{
    private string _searchText = string.Empty;

    /// <summary>
    /// Texto de busca
    /// </summary>
    public string SearchText
    {
        get => _searchText;
        set => _searchText = value?.Trim() ?? string.Empty;
    }

    /// <summary>
    /// Indica se há filtro de busca
    /// </summary>
    public bool HasSearchFilter => !string.IsNullOrWhiteSpace(SearchText);

    /// <summary>
    /// Construtor protegido
    /// </summary>
    protected BaseSearchQuery()
    {
    }

    /// <summary>
    /// Construtor com usuário executor
    /// </summary>
    /// <param name="executedBy">Usuário que executa a query</param>
    protected BaseSearchQuery(string executedBy) : base(executedBy)
    {
    }



    /// <summary>
    /// Construtor com busca e paginação
    /// </summary>
    /// <param name="searchText">Texto de busca</param>
    /// <param name="pageNumber">Número da página</param>
    /// <param name="pageSize">Tamanho da página</param>
    protected BaseSearchQuery(string searchText, int pageNumber, int pageSize) : base(pageNumber, pageSize)
    {
        SearchText = searchText;
    }

    /// <summary>
    /// Construtor completo
    /// </summary>
    /// <param name="executedBy">Usuário que executa a query</param>
    /// <param name="searchText">Texto de busca</param>
    /// <param name="pageNumber">Número da página</param>
    /// <param name="pageSize">Tamanho da página</param>
    protected BaseSearchQuery(string executedBy, string searchText, int pageNumber, int pageSize) 
        : base(executedBy, pageNumber, pageSize)
    {
        SearchText = searchText;
    }
}

/// <summary>
/// Query base para consultas com ordenação
/// </summary>
/// <typeparam name="TResponse">Tipo da resposta</typeparam>
public abstract class BaseSortedQuery<TResponse> : BasePagedQuery<TResponse>
{
    /// <summary>
    /// Campo de ordenação
    /// </summary>
    public string SortBy { get; set; } = string.Empty;

    /// <summary>
    /// Direção da ordenação (true = ascendente, false = descendente)
    /// </summary>
    public bool SortAscending { get; set; } = true;

    /// <summary>
    /// Indica se há ordenação personalizada
    /// </summary>
    public bool HasCustomSort => !string.IsNullOrWhiteSpace(SortBy);

    /// <summary>
    /// Construtor protegido
    /// </summary>
    protected BaseSortedQuery()
    {
    }

    /// <summary>
    /// Construtor com usuário executor
    /// </summary>
    /// <param name="executedBy">Usuário que executa a query</param>
    protected BaseSortedQuery(string executedBy) : base(executedBy)
    {
    }

    /// <summary>
    /// Construtor com ordenação
    /// </summary>
    /// <param name="sortBy">Campo de ordenação</param>
    /// <param name="sortAscending">Direção da ordenação</param>
    protected BaseSortedQuery(string sortBy, bool sortAscending = true)
    {
        SortBy = sortBy?.Trim() ?? string.Empty;
        SortAscending = sortAscending;
    }

    /// <summary>
    /// Construtor completo
    /// </summary>
    /// <param name="executedBy">Usuário que executa a query</param>
    /// <param name="pageNumber">Número da página</param>
    /// <param name="pageSize">Tamanho da página</param>
    /// <param name="sortBy">Campo de ordenação</param>
    /// <param name="sortAscending">Direção da ordenação</param>
    protected BaseSortedQuery(string executedBy, int pageNumber, int pageSize, string sortBy, bool sortAscending = true) 
        : base(executedBy, pageNumber, pageSize)
    {
        SortBy = sortBy?.Trim() ?? string.Empty;
        SortAscending = sortAscending;
    }
}
