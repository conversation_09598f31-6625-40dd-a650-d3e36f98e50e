using Avalonia.Controls;
using Avalonia.Interactivity;
using AutoInstaller.UI.ViewModels;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace AutoInstaller.UI.Views;

/// <summary>
/// Code-behind para ContainerView
/// </summary>
public partial class ContainerView : UserControl
{
    private readonly ILogger<ContainerView> _logger;

    /// <summary>
    /// Construtor padrão para designer
    /// </summary>
    public ContainerView()
    {
        InitializeComponent();
        _logger = null!; // Será null no designer
    }

    /// <summary>
    /// Construtor com injeção de dependência
    /// </summary>
    /// <param name="logger">Logger</param>
    public ContainerView(ILogger<ContainerView> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        InitializeComponent();
    }

    /// <summary>
    /// Construtor com ViewModel
    /// </summary>
    /// <param name="viewModel">ViewModel</param>
    /// <param name="logger">Logger</param>
    public ContainerView(ContainerViewModel viewModel, ILogger<ContainerView> logger) : this(logger)
    {
        DataContext = viewModel ?? throw new ArgumentNullException(nameof(viewModel));
    }

    /// <summary>
    /// Evento chamado quando o controle é carregado
    /// </summary>
    /// <param name="sender">Sender</param>
    /// <param name="e">Event args</param>
    protected override async void OnLoaded(RoutedEventArgs e)
    {
        base.OnLoaded(e);

        try
        {
            _logger?.LogDebug("ContainerView carregada");

            // Inicializar ViewModel se disponível
            if (DataContext is ContainerViewModel viewModel)
            {
                viewModel.OnActivated();
                await viewModel.InitializeAsync();
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Erro ao carregar ContainerView");
        }
    }

    /// <summary>
    /// Evento chamado quando o controle é descarregado
    /// </summary>
    /// <param name="sender">Sender</param>
    /// <param name="e">Event args</param>
    protected override void OnUnloaded(RoutedEventArgs e)
    {
        try
        {
            _logger?.LogDebug("ContainerView descarregada");

            // Limpar ViewModel se disponível
            if (DataContext is ContainerViewModel viewModel)
            {
                viewModel.OnDeactivated();
                viewModel.Cleanup();
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Erro ao descarregar ContainerView");
        }
        finally
        {
            base.OnUnloaded(e);
        }
    }

    /// <summary>
    /// Configura o ViewModel
    /// </summary>
    /// <param name="viewModel">ViewModel</param>
    public void SetViewModel(ContainerViewModel viewModel)
    {
        if (viewModel == null)
            throw new ArgumentNullException(nameof(viewModel));

        DataContext = viewModel;
        _logger?.LogDebug("ViewModel configurado para ContainerView");
    }

    /// <summary>
    /// Obtém o ViewModel atual
    /// </summary>
    /// <returns>ViewModel ou null</returns>
    public ContainerViewModel? GetViewModel()
    {
        return DataContext as ContainerViewModel;
    }
}
