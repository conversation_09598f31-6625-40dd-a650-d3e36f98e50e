{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"AutoInstaller.Core/1.0.0": {"dependencies": {"FluentValidation": "11.10.0", "MediatR": "11.1.0"}, "runtime": {"AutoInstaller.Core.dll": {}}}, "FluentValidation/11.10.0": {"runtime": {"lib/net8.0/FluentValidation.dll": {"assemblyVersion": "********", "fileVersion": "11.10.0.0"}}}, "MediatR/11.1.0": {"dependencies": {"MediatR.Contracts": "1.0.1"}, "runtime": {"lib/netstandard2.1/MediatR.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MediatR.Contracts/1.0.1": {"runtime": {"lib/netstandard2.0/MediatR.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"AutoInstaller.Core/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "FluentValidation/11.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-qsJGSJDdZ8qiG+lVJ70PZfJHcEdq8UQZ/tZDXoj78/iHKG6lVKtMJsD11zyyv/IPc7rwqGqnFoFLTNzpo3IPYg==", "path": "fluentvalidation/11.10.0", "hashPath": "fluentvalidation.11.10.0.nupkg.sha512"}, "MediatR/11.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-YIbtrLOyeCuIv8vIuHL/mMdls5xmgS36pIOJDxKZuu55nxA2MI2Z+E/Uk0z+F/LE11AGmpjplOM0NZ91m5LQBA==", "path": "mediatr/11.1.0", "hashPath": "mediatr.11.1.0.nupkg.sha512"}, "MediatR.Contracts/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-NsRvOxthhkYml4DcBMyJsmym8C4MchioH7wxKVPEg7plFj9Euh/i4IcmZ3Gvgx6+K8obeuhPeJdoBl56wnuo3A==", "path": "mediatr.contracts/1.0.1", "hashPath": "mediatr.contracts.1.0.1.nupkg.sha512"}}}