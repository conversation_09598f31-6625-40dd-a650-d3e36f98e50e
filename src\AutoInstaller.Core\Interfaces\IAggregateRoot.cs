namespace AutoInstaller.Core.Interfaces;

/// <summary>
/// Interface que marca uma entidade como raiz de agregado
/// </summary>
public interface IAggregateRoot
{
    /// <summary>
    /// Identificador único do agregado
    /// </summary>
    Guid Id { get; }

    /// <summary>
    /// Eventos de domínio do agregado
    /// </summary>
    IReadOnlyCollection<IDomainEvent> DomainEvents { get; }

    /// <summary>
    /// Limpa todos os eventos de domínio
    /// </summary>
    void ClearDomainEvents();
}
