<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:vm="using:AutoInstaller.UI.ViewModels"
             xmlns:material="using:Material.Icons.Avalonia"
             xmlns:controls="using:AutoInstaller.UI.Controls"
             mc:Ignorable="d" d:DesignWidth="1200" d:DesignHeight="800"
             x:Class="AutoInstaller.UI.Views.ContainerView"
             x:DataType="vm:ContainerViewModel">

  <Design.DataContext>
    <vm:ContainerViewModel />
  </Design.DataContext>

  <Grid RowDefinitions="Auto,*,Auto">
    
    <!-- Header com título e estatísticas -->
    <Border Grid.Row="0" Background="{DynamicResource MaterialPrimaryBrush}" 
            Padding="20" Margin="0,0,0,10">
      <Grid ColumnDefinitions="*,Auto">
        
        <!-- Título e estatísticas -->
        <StackPanel Grid.Column="0" Orientation="Vertical">
          <TextBlock Text="{Binding Title}" 
                     FontSize="24" FontWeight="Bold" 
                     Foreground="{DynamicResource MaterialOnPrimaryBrush}" />
          
          <StackPanel Orientation="Horizontal" Margin="0,10,0,0" 
                      IsVisible="{Binding Statistics, Converter={x:Static ObjectConverters.IsNotNull}}">
            
            <!-- Total de containers -->
            <Border Background="{DynamicResource MaterialPrimaryVariantBrush}" 
                    CornerRadius="8" Padding="12,6" Margin="0,0,10,0">
              <StackPanel Orientation="Horizontal">
                <material:MaterialIcon Kind="Docker" Width="16" Height="16" 
                                       Foreground="{DynamicResource MaterialOnPrimaryBrush}" />
                <TextBlock Text="{Binding Statistics.TotalContainers}" 
                           FontWeight="Bold" Margin="8,0,0,0"
                           Foreground="{DynamicResource MaterialOnPrimaryBrush}" />
                <TextBlock Text="Total" Margin="4,0,0,0" 
                           Foreground="{DynamicResource MaterialOnPrimaryBrush}" />
              </StackPanel>
            </Border>
            
            <!-- Containers em execução -->
            <Border Background="{DynamicResource MaterialSuccessBrush}" 
                    CornerRadius="8" Padding="12,6" Margin="0,0,10,0">
              <StackPanel Orientation="Horizontal">
                <material:MaterialIcon Kind="Play" Width="16" Height="16" 
                                       Foreground="White" />
                <TextBlock Text="{Binding Statistics.RunningContainers}" 
                           FontWeight="Bold" Margin="8,0,0,0" Foreground="White" />
                <TextBlock Text="Executando" Margin="4,0,0,0" Foreground="White" />
              </StackPanel>
            </Border>
            
            <!-- Containers parados -->
            <Border Background="{DynamicResource MaterialErrorBrush}" 
                    CornerRadius="8" Padding="12,6" Margin="0,0,10,0">
              <StackPanel Orientation="Horizontal">
                <material:MaterialIcon Kind="Stop" Width="16" Height="16" 
                                       Foreground="White" />
                <TextBlock Text="{Binding Statistics.StoppedContainers}" 
                           FontWeight="Bold" Margin="8,0,0,0" Foreground="White" />
                <TextBlock Text="Parados" Margin="4,0,0,0" Foreground="White" />
              </StackPanel>
            </Border>
            
          </StackPanel>
        </StackPanel>
        
        <!-- Botões de ação -->
        <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
          <Button Command="{Binding CreateContainerCommand}" 
                  Classes="accent" Margin="0,0,10,0">
            <StackPanel Orientation="Horizontal">
              <material:MaterialIcon Kind="Plus" Width="16" Height="16" />
              <TextBlock Text="Novo Container" Margin="8,0,0,0" />
            </StackPanel>
          </Button>
          
          <Button Command="{Binding RefreshCommand}" Classes="outline">
            <StackPanel Orientation="Horizontal">
              <material:MaterialIcon Kind="Refresh" Width="16" Height="16" />
              <TextBlock Text="Atualizar" Margin="8,0,0,0" />
            </StackPanel>
          </Button>
        </StackPanel>
        
      </Grid>
    </Border>

    <!-- Conteúdo principal -->
    <Grid Grid.Row="1" ColumnDefinitions="300,*" Margin="20,0">
      
      <!-- Painel lateral com filtros -->
      <Border Grid.Column="0" Background="{DynamicResource MaterialSurfaceBrush}" 
              CornerRadius="8" Padding="20" Margin="0,0,20,0">
        <StackPanel>
          
          <TextBlock Text="Filtros" FontSize="18" FontWeight="SemiBold" 
                     Margin="0,0,0,15" />
          
          <!-- Busca -->
          <TextBlock Text="Buscar" FontWeight="Medium" Margin="0,0,0,5" />
          <TextBox Text="{Binding SearchText}" 
                   Watermark="Nome, imagem, etc..." 
                   Margin="0,0,0,15">
            <TextBox.InnerLeftContent>
              <material:MaterialIcon Kind="Magnify" Width="16" Height="16" 
                                     Margin="8,0,0,0" />
            </TextBox.InnerLeftContent>
          </TextBox>
          
          <!-- Filtro por Status -->
          <TextBlock Text="Status" FontWeight="Medium" Margin="0,0,0,5" />
          <ComboBox ItemsSource="{Binding StatusOptions}" 
                    SelectedItem="{Binding SelectedStatus}" 
                    Margin="0,0,0,15" />
          
          <!-- Filtro por Runtime -->
          <TextBlock Text="Runtime" FontWeight="Medium" Margin="0,0,0,5" />
          <ComboBox ItemsSource="{Binding RuntimeOptions}" 
                    SelectedItem="{Binding SelectedRuntime}" 
                    Margin="0,0,0,15" />
          
          <!-- Botão para carregar estatísticas -->
          <Button Command="{Binding LoadStatisticsCommand}" 
                  Classes="outline" HorizontalAlignment="Stretch">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
              <material:MaterialIcon Kind="ChartLine" Width="16" Height="16" />
              <TextBlock Text="Atualizar Stats" Margin="8,0,0,0" />
            </StackPanel>
          </Button>
          
        </StackPanel>
      </Border>
      
      <!-- Lista de containers -->
      <Border Grid.Column="1" Background="{DynamicResource MaterialSurfaceBrush}" 
              CornerRadius="8" Padding="0">
        <Grid RowDefinitions="Auto,*,Auto">
          
          <!-- Cabeçalho da lista -->
          <Border Grid.Row="0" Background="{DynamicResource MaterialSurfaceVariantBrush}" 
                  CornerRadius="8,8,0,0" Padding="20,15">
            <Grid ColumnDefinitions="*,120,100,120,Auto">
              <TextBlock Grid.Column="0" Text="Container" FontWeight="SemiBold" />
              <TextBlock Grid.Column="1" Text="Status" FontWeight="SemiBold" />
              <TextBlock Grid.Column="2" Text="Runtime" FontWeight="SemiBold" />
              <TextBlock Grid.Column="3" Text="Criado" FontWeight="SemiBold" />
              <TextBlock Grid.Column="4" Text="Ações" FontWeight="SemiBold" />
            </Grid>
          </Border>
          
          <!-- Lista de containers -->
          <ListBox Grid.Row="1" ItemsSource="{Binding Items}" 
                   SelectedItem="{Binding SelectedItem}"
                   Background="Transparent" BorderThickness="0">
            <ListBox.ItemTemplate>
              <DataTemplate>
                <Border Padding="20,15" BorderBrush="{DynamicResource MaterialOutlineBrush}" 
                        BorderThickness="0,0,0,1">
                  <Grid ColumnDefinitions="*,120,100,120,Auto">
                    
                    <!-- Informações do container -->
                    <StackPanel Grid.Column="0">
                      <TextBlock Text="{Binding Name}" FontWeight="Medium" FontSize="14" />
                      <TextBlock Text="{Binding ImageName}:{Binding ImageTag}" 
                                 FontSize="12" Opacity="0.7" />
                    </StackPanel>
                    
                    <!-- Status -->
                    <Border Grid.Column="1" CornerRadius="12" Padding="8,4" 
                            HorizontalAlignment="Left">
                      <Border.Background>
                        <MultiBinding Converter="{StaticResource StatusToBrushConverter}">
                          <Binding Path="Status" />
                        </MultiBinding>
                      </Border.Background>
                      <TextBlock Text="{Binding Status}" FontSize="11" 
                                 FontWeight="Medium" Foreground="White" />
                    </Border>
                    
                    <!-- Runtime -->
                    <StackPanel Grid.Column="2" Orientation="Horizontal">
                      <material:MaterialIcon Width="16" Height="16">
                        <material:MaterialIcon.Kind>
                          <MultiBinding Converter="{StaticResource RuntimeToIconConverter}">
                            <Binding Path="Runtime" />
                          </MultiBinding>
                        </material:MaterialIcon.Kind>
                      </material:MaterialIcon>
                      <TextBlock Text="{Binding Runtime}" Margin="4,0,0,0" 
                                 FontSize="12" />
                    </StackPanel>
                    
                    <!-- Data de criação -->
                    <TextBlock Grid.Column="3" FontSize="12" Opacity="0.7">
                      <TextBlock.Text>
                        <MultiBinding Converter="{StaticResource DateTimeToStringConverter}">
                          <Binding Path="CreatedAt" />
                        </MultiBinding>
                      </TextBlock.Text>
                    </TextBlock>
                    
                    <!-- Botões de ação -->
                    <StackPanel Grid.Column="4" Orientation="Horizontal">
                      
                      <!-- Botão Start -->
                      <Button Command="{Binding $parent[UserControl].DataContext.StartContainerCommand}"
                              Classes="icon" ToolTip.Tip="Iniciar"
                              IsVisible="{Binding Status, Converter={StaticResource CanStartConverter}}">
                        <material:MaterialIcon Kind="Play" Width="16" Height="16" 
                                               Foreground="{DynamicResource MaterialSuccessBrush}" />
                      </Button>
                      
                      <!-- Botão Stop -->
                      <Button Command="{Binding $parent[UserControl].DataContext.StopContainerCommand}"
                              Classes="icon" ToolTip.Tip="Parar"
                              IsVisible="{Binding Status, Converter={StaticResource CanStopConverter}}">
                        <material:MaterialIcon Kind="Stop" Width="16" Height="16" 
                                               Foreground="{DynamicResource MaterialErrorBrush}" />
                      </Button>
                      
                      <!-- Botão Pause -->
                      <Button Command="{Binding $parent[UserControl].DataContext.PauseContainerCommand}"
                              Classes="icon" ToolTip.Tip="Pausar"
                              IsVisible="{Binding Status, Converter={StaticResource CanPauseConverter}}">
                        <material:MaterialIcon Kind="Pause" Width="16" Height="16" 
                                               Foreground="{DynamicResource MaterialWarningBrush}" />
                      </Button>
                      
                      <!-- Botão Unpause -->
                      <Button Command="{Binding $parent[UserControl].DataContext.UnpauseContainerCommand}"
                              Classes="icon" ToolTip.Tip="Despausar"
                              IsVisible="{Binding Status, Converter={StaticResource CanUnpauseConverter}}">
                        <material:MaterialIcon Kind="Play" Width="16" Height="16" 
                                               Foreground="{DynamicResource MaterialWarningBrush}" />
                      </Button>
                      
                      <!-- Botão Restart -->
                      <Button Command="{Binding $parent[UserControl].DataContext.RestartContainerCommand}"
                              Classes="icon" ToolTip.Tip="Reiniciar"
                              IsVisible="{Binding Status, Converter={StaticResource CanStopConverter}}">
                        <material:MaterialIcon Kind="Restart" Width="16" Height="16" />
                      </Button>
                      
                      <!-- Botão Edit -->
                      <Button Command="{Binding $parent[UserControl].DataContext.EditContainerCommand}"
                              Classes="icon" ToolTip.Tip="Editar">
                        <material:MaterialIcon Kind="Edit" Width="16" Height="16" />
                      </Button>
                      
                      <!-- Botão Delete -->
                      <Button Command="{Binding $parent[UserControl].DataContext.DeleteContainerCommand}"
                              Classes="icon" ToolTip.Tip="Deletar">
                        <material:MaterialIcon Kind="Delete" Width="16" Height="16" 
                                               Foreground="{DynamicResource MaterialErrorBrush}" />
                      </Button>
                      
                    </StackPanel>
                    
                  </Grid>
                </Border>
              </DataTemplate>
            </ListBox.ItemTemplate>
          </ListBox>
          
          <!-- Paginação -->
          <Border Grid.Row="2" Background="{DynamicResource MaterialSurfaceVariantBrush}" 
                  CornerRadius="0,0,8,8" Padding="20,15">
            <Grid ColumnDefinitions="*,Auto,*">
              
              <!-- Informações da página -->
              <TextBlock Grid.Column="0" VerticalAlignment="Center" FontSize="12" Opacity="0.7">
                <TextBlock.Text>
                  <MultiBinding StringFormat="Mostrando {0} de {1} containers">
                    <Binding Path="Items.Count" />
                    <Binding Path="TotalItems" />
                  </MultiBinding>
                </TextBlock.Text>
              </TextBlock>
              
              <!-- Controles de paginação -->
              <StackPanel Grid.Column="1" Orientation="Horizontal">
                <Button Command="{Binding PreviousPageCommand}" 
                        Classes="icon" ToolTip.Tip="Página anterior">
                  <material:MaterialIcon Kind="ChevronLeft" Width="16" Height="16" />
                </Button>
                
                <TextBlock VerticalAlignment="Center" Margin="15,0" FontSize="12">
                  <TextBlock.Text>
                    <MultiBinding StringFormat="Página {0} de {1}">
                      <Binding Path="CurrentPage" />
                      <Binding Path="TotalPages" />
                    </MultiBinding>
                  </TextBlock.Text>
                </TextBlock>
                
                <Button Command="{Binding NextPageCommand}" 
                        Classes="icon" ToolTip.Tip="Próxima página">
                  <material:MaterialIcon Kind="ChevronRight" Width="16" Height="16" />
                </Button>
              </StackPanel>
              
              <!-- Tamanho da página -->
              <StackPanel Grid.Column="2" Orientation="Horizontal" 
                          HorizontalAlignment="Right">
                <TextBlock Text="Itens por página:" VerticalAlignment="Center" 
                           FontSize="12" Margin="0,0,8,0" />
                <ComboBox SelectedItem="{Binding PageSize}" MinWidth="60">
                  <ComboBoxItem Content="10" />
                  <ComboBoxItem Content="20" />
                  <ComboBoxItem Content="50" />
                  <ComboBoxItem Content="100" />
                </ComboBox>
              </StackPanel>
              
            </Grid>
          </Border>
          
        </Grid>
      </Border>
      
    </Grid>

    <!-- Indicador de carregamento e mensagens de erro -->
    <Grid Grid.Row="2" Margin="20,10,20,0">
      
      <!-- Indicador de carregamento -->
      <Border Background="{DynamicResource MaterialSurfaceBrush}" 
              CornerRadius="8" Padding="15" 
              IsVisible="{Binding IsBusy}">
        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
          <controls:LoadingSpinner Width="16" Height="16" />
          <TextBlock Text="Carregando..." Margin="10,0,0,0" />
        </StackPanel>
      </Border>
      
      <!-- Mensagem de erro -->
      <Border Background="{DynamicResource MaterialErrorBrush}" 
              CornerRadius="8" Padding="15" 
              IsVisible="{Binding HasError}">
        <Grid ColumnDefinitions="Auto,*,Auto">
          <material:MaterialIcon Grid.Column="0" Kind="AlertCircle" 
                                 Width="16" Height="16" Foreground="White" />
          <TextBlock Grid.Column="1" Text="{Binding ErrorMessage}" 
                     Foreground="White" Margin="10,0" />
          <Button Grid.Column="2" Command="{Binding ClearErrorCommand}" 
                  Classes="icon" Background="Transparent">
            <material:MaterialIcon Kind="Close" Width="16" Height="16" 
                                   Foreground="White" />
          </Button>
        </Grid>
      </Border>
      
    </Grid>

  </Grid>

</UserControl>
