using MediatR;
using {{RootNamespace}}.Application.Common.Models;
using {{RootNamespace}}.Application.Modules.{{ModuleName}}.DTOs;

namespace {{RootNamespace}}.Application.Modules.{{ModuleName}}.Queries;

/// <summary>
/// Query para obter {{EntityName}} por ID
/// </summary>
public record Get{{EntityName}}Query(Guid Id) : IRequest<Result<{{EntityName}}Dto>>;

/// <summary>
/// Query para obter lista de {{PluralEntityName}}
/// </summary>
public record Get{{PluralEntityName}}Query(
    int PageNumber = 1,
    int PageSize = 10,
    string? SearchTerm = null,
    string? SortBy = null,
    bool SortDescending = false
) : IRequest<Result<PaginatedList<{{EntityName}}Dto>>>;

/// <summary>
/// Query para obter {{PluralEntityName}} por critério específico
/// </summary>
public record Get{{PluralEntityName}}ByCriteriaQuery(
    string? Criteria,
    object? Value,
    int PageNumber = 1,
    int PageSize = 10
) : IRequest<Result<PaginatedList<{{EntityName}}Dto>>>;

/// <summary>
/// Query para verificar se {{EntityName}} existe
/// </summary>
public record Check{{EntityName}}ExistsQuery(Guid Id) : IRequest<Result<bool>>;

/// <summary>
/// Query para obter contagem de {{PluralEntityName}}
/// </summary>
public record Get{{PluralEntityName}}CountQuery(
    string? SearchTerm = null
) : IRequest<Result<int>>;

/// <summary>
/// Query para obter {{PluralEntityName}} ativos
/// </summary>
public record GetActive{{PluralEntityName}}Query(
    int PageNumber = 1,
    int PageSize = 10
) : IRequest<Result<PaginatedList<{{EntityName}}Dto>>>;