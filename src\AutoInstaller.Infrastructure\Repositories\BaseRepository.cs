using AutoInstaller.Core.Common;
using AutoInstaller.Core.Interfaces;
using AutoInstaller.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Linq.Expressions;

namespace AutoInstaller.Infrastructure.Repositories;

/// <summary>
/// Repositório base genérico
/// </summary>
/// <typeparam name="TEntity">Tipo da entidade</typeparam>
public abstract class BaseRepository<TEntity> : IPagedRepository<TEntity> where TEntity : BaseEntity
{
    protected readonly AutoInstallerDbContext _context;
    protected readonly DbSet<TEntity> _dbSet;
    protected readonly ILogger _logger;

    /// <summary>
    /// Construtor
    /// </summary>
    /// <param name="context">Contexto do banco de dados</param>
    /// <param name="logger">Logger</param>
    protected BaseRepository(AutoInstallerDbContext context, ILogger logger)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _dbSet = _context.Set<TEntity>();
    }

    /// <summary>
    /// Obtém uma entidade por ID
    /// </summary>
    /// <param name="id">ID da entidade</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Entidade ou null se não encontrada</returns>
    public virtual async Task<TEntity?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _dbSet.FindAsync(new object[] { id }, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar entidade {EntityType} por ID {Id}", typeof(TEntity).Name, id);
            throw;
        }
    }

    /// <summary>
    /// Obtém todas as entidades
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de entidades</returns>
    public virtual async Task<IReadOnlyList<TEntity>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _dbSet.ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar todas as entidades {EntityType}", typeof(TEntity).Name);
            throw;
        }
    }

    /// <summary>
    /// Obtém entidades com base em um filtro
    /// </summary>
    /// <param name="predicate">Filtro</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de entidades filtradas</returns>
    public virtual async Task<IReadOnlyList<TEntity>> GetAsync(Expression<Func<TEntity, bool>> predicate, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _dbSet.Where(predicate).ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar entidades {EntityType} com filtro", typeof(TEntity).Name);
            throw;
        }
    }

    /// <summary>
    /// Obtém a primeira entidade que atende ao filtro
    /// </summary>
    /// <param name="predicate">Filtro</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Primeira entidade ou null</returns>
    public virtual async Task<TEntity?> GetFirstOrDefaultAsync(Expression<Func<TEntity, bool>> predicate, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _dbSet.FirstOrDefaultAsync(predicate, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar primeira entidade {EntityType} com filtro", typeof(TEntity).Name);
            throw;
        }
    }

    /// <summary>
    /// Verifica se existe uma entidade que atende ao filtro
    /// </summary>
    /// <param name="predicate">Filtro</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se existe</returns>
    public virtual async Task<bool> ExistsAsync(Expression<Func<TEntity, bool>> predicate, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _dbSet.AnyAsync(predicate, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao verificar existência de entidade {EntityType}", typeof(TEntity).Name);
            throw;
        }
    }

    /// <summary>
    /// Conta o número de entidades que atendem ao filtro
    /// </summary>
    /// <param name="predicate">Filtro</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Número de entidades</returns>
    public virtual async Task<int> CountAsync(Expression<Func<TEntity, bool>> predicate, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _dbSet.CountAsync(predicate, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao contar entidades {EntityType}", typeof(TEntity).Name);
            throw;
        }
    }

    /// <summary>
    /// Adiciona uma nova entidade
    /// </summary>
    /// <param name="entity">Entidade a ser adicionada</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    public virtual async Task AddAsync(TEntity entity, CancellationToken cancellationToken = default)
    {
        try
        {
            if (entity == null)
                throw new ArgumentNullException(nameof(entity));

            await _dbSet.AddAsync(entity, cancellationToken);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogDebug("Entidade {EntityType} adicionada com ID {Id}", typeof(TEntity).Name, entity.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao adicionar entidade {EntityType}", typeof(TEntity).Name);
            throw;
        }
    }

    /// <summary>
    /// Adiciona múltiplas entidades
    /// </summary>
    /// <param name="entities">Entidades a serem adicionadas</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    public virtual async Task AddRangeAsync(IEnumerable<TEntity> entities, CancellationToken cancellationToken = default)
    {
        try
        {
            if (entities == null)
                throw new ArgumentNullException(nameof(entities));

            var entityList = entities.ToList();
            if (!entityList.Any())
                return;

            await _dbSet.AddRangeAsync(entityList, cancellationToken);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogDebug("{Count} entidades {EntityType} adicionadas", entityList.Count, typeof(TEntity).Name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao adicionar múltiplas entidades {EntityType}", typeof(TEntity).Name);
            throw;
        }
    }

    /// <summary>
    /// Atualiza uma entidade
    /// </summary>
    /// <param name="entity">Entidade a ser atualizada</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    public virtual async Task UpdateAsync(TEntity entity, CancellationToken cancellationToken = default)
    {
        try
        {
            if (entity == null)
                throw new ArgumentNullException(nameof(entity));

            _dbSet.Update(entity);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogDebug("Entidade {EntityType} atualizada com ID {Id}", typeof(TEntity).Name, entity.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao atualizar entidade {EntityType} com ID {Id}", typeof(TEntity).Name, entity.Id);
            throw;
        }
    }

    /// <summary>
    /// Remove uma entidade (soft delete)
    /// </summary>
    /// <param name="entity">Entidade a ser removida</param>
    /// <param name="deletedBy">Usuário que removeu</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    public virtual async Task RemoveAsync(TEntity entity, string deletedBy, CancellationToken cancellationToken = default)
    {
        try
        {
            if (entity == null)
                throw new ArgumentNullException(nameof(entity));

            if (string.IsNullOrWhiteSpace(deletedBy))
                throw new ArgumentException("Usuário que removeu não pode ser vazio", nameof(deletedBy));

            entity.MarkAsDeleted(deletedBy);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogDebug("Entidade {EntityType} removida (soft delete) com ID {Id} por {DeletedBy}", 
                typeof(TEntity).Name, entity.Id, deletedBy);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao remover entidade {EntityType} com ID {Id}", typeof(TEntity).Name, entity.Id);
            throw;
        }
    }

    /// <summary>
    /// Remove uma entidade por ID (soft delete)
    /// </summary>
    /// <param name="id">ID da entidade</param>
    /// <param name="deletedBy">Usuário que removeu</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    public virtual async Task RemoveAsync(Guid id, string deletedBy, CancellationToken cancellationToken = default)
    {
        try
        {
            var entity = await GetByIdAsync(id, cancellationToken);
            if (entity == null)
                throw new InvalidOperationException($"Entidade {typeof(TEntity).Name} com ID {id} não encontrada");

            await RemoveAsync(entity, deletedBy, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao remover entidade {EntityType} por ID {Id}", typeof(TEntity).Name, id);
            throw;
        }
    }

    /// <summary>
    /// Remove permanentemente uma entidade
    /// </summary>
    /// <param name="entity">Entidade a ser removida</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    public virtual async Task HardDeleteAsync(TEntity entity, CancellationToken cancellationToken = default)
    {
        try
        {
            if (entity == null)
                throw new ArgumentNullException(nameof(entity));

            _dbSet.Remove(entity);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogDebug("Entidade {EntityType} removida permanentemente com ID {Id}", typeof(TEntity).Name, entity.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao remover permanentemente entidade {EntityType} com ID {Id}", 
                typeof(TEntity).Name, entity.Id);
            throw;
        }
    }

    /// <summary>
    /// Restaura uma entidade removida
    /// </summary>
    /// <param name="id">ID da entidade</param>
    /// <param name="restoredBy">Usuário que restaurou</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    public virtual async Task RestoreAsync(Guid id, string restoredBy, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(restoredBy))
                throw new ArgumentException("Usuário que restaurou não pode ser vazio", nameof(restoredBy));

            var success = await _context.RestoreEntityAsync<TEntity>(id, restoredBy, cancellationToken);
            if (!success)
                throw new InvalidOperationException($"Entidade {typeof(TEntity).Name} com ID {id} não encontrada ou não estava excluída");

            _logger.LogDebug("Entidade {EntityType} restaurada com ID {Id} por {RestoredBy}", 
                typeof(TEntity).Name, id, restoredBy);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao restaurar entidade {EntityType} com ID {Id}", typeof(TEntity).Name, id);
            throw;
        }
    }

    /// <summary>
    /// Obtém entidades paginadas
    /// </summary>
    /// <param name="pageNumber">Número da página (1-based)</param>
    /// <param name="pageSize">Tamanho da página</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Resultado paginado</returns>
    public virtual async Task<PagedResult<TEntity>> GetPagedAsync(int pageNumber, int pageSize, CancellationToken cancellationToken = default)
    {
        try
        {
            var totalCount = await _dbSet.CountAsync(cancellationToken);
            var items = await _dbSet
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync(cancellationToken);

            return new PagedResult<TEntity>(items, pageNumber, pageSize, totalCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar entidades {EntityType} paginadas", typeof(TEntity).Name);
            throw;
        }
    }

    /// <summary>
    /// Obtém entidades paginadas com filtro
    /// </summary>
    /// <param name="predicate">Filtro</param>
    /// <param name="pageNumber">Número da página (1-based)</param>
    /// <param name="pageSize">Tamanho da página</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Resultado paginado</returns>
    public virtual async Task<PagedResult<TEntity>> GetPagedAsync(Expression<Func<TEntity, bool>> predicate, int pageNumber, int pageSize, CancellationToken cancellationToken = default)
    {
        try
        {
            var query = _dbSet.Where(predicate);
            var totalCount = await query.CountAsync(cancellationToken);
            var items = await query
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync(cancellationToken);

            return new PagedResult<TEntity>(items, pageNumber, pageSize, totalCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar entidades {EntityType} paginadas com filtro", typeof(TEntity).Name);
            throw;
        }
    }

    /// <summary>
    /// Obtém entidades paginadas com ordenação
    /// </summary>
    /// <param name="pageNumber">Número da página (1-based)</param>
    /// <param name="pageSize">Tamanho da página</param>
    /// <param name="orderBy">Expressão de ordenação</param>
    /// <param name="ascending">Se a ordenação é ascendente</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Resultado paginado</returns>
    public virtual async Task<PagedResult<TEntity>> GetPagedAsync<TKey>(int pageNumber, int pageSize, Expression<Func<TEntity, TKey>> orderBy, bool ascending = true, CancellationToken cancellationToken = default)
    {
        try
        {
            var totalCount = await _dbSet.CountAsync(cancellationToken);
            var query = ascending ? _dbSet.OrderBy(orderBy) : _dbSet.OrderByDescending(orderBy);
            var items = await query
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync(cancellationToken);

            return new PagedResult<TEntity>(items, pageNumber, pageSize, totalCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar entidades {EntityType} paginadas com ordenação", typeof(TEntity).Name);
            throw;
        }
    }

    /// <summary>
    /// Obtém entidades paginadas com filtro e ordenação
    /// </summary>
    /// <param name="predicate">Filtro</param>
    /// <param name="pageNumber">Número da página (1-based)</param>
    /// <param name="pageSize">Tamanho da página</param>
    /// <param name="orderBy">Expressão de ordenação</param>
    /// <param name="ascending">Se a ordenação é ascendente</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Resultado paginado</returns>
    public virtual async Task<PagedResult<TEntity>> GetPagedAsync<TKey>(Expression<Func<TEntity, bool>> predicate, int pageNumber, int pageSize, Expression<Func<TEntity, TKey>> orderBy, bool ascending = true, CancellationToken cancellationToken = default)
    {
        try
        {
            var baseQuery = _dbSet.Where(predicate);
            var totalCount = await baseQuery.CountAsync(cancellationToken);
            var query = ascending ? baseQuery.OrderBy(orderBy) : baseQuery.OrderByDescending(orderBy);
            var items = await query
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync(cancellationToken);

            return new PagedResult<TEntity>(items, pageNumber, pageSize, totalCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar entidades {EntityType} paginadas com filtro e ordenação", typeof(TEntity).Name);
            throw;
        }
    }
}
