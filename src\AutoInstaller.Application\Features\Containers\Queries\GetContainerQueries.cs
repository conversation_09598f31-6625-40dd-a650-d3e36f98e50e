using AutoInstaller.Application.Common;
using AutoInstaller.Application.DTOs;
using AutoInstaller.Core.Entities;

namespace AutoInstaller.Application.Features.Containers.Queries;

/// <summary>
/// Query para obter um container por ID
/// </summary>
public class GetContainerByIdQuery : BaseQuery<ContainerDto?>
{
    /// <summary>
    /// ID do container
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Construtor
    /// </summary>
    public GetContainerByIdQuery()
    {
    }

    /// <summary>
    /// Construtor com ID
    /// </summary>
    /// <param name="id">ID do container</param>
    public GetContainerByIdQuery(Guid id)
    {
        Id = id;
    }

    /// <summary>
    /// Construtor com ID e usuário executor
    /// </summary>
    /// <param name="id">ID do container</param>
    /// <param name="executedBy">Usuário que executa a query</param>
    public GetContainerByIdQuery(Guid id, string executedBy) : base(executedBy)
    {
        Id = id;
    }
}

/// <summary>
/// Query para obter um container por nome
/// </summary>
public class GetContainerByNameQuery : BaseQuery<ContainerDto?>
{
    /// <summary>
    /// Nome do container
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Construtor
    /// </summary>
    public GetContainerByNameQuery()
    {
    }

    /// <summary>
    /// Construtor com nome
    /// </summary>
    /// <param name="name">Nome do container</param>
    public GetContainerByNameQuery(string name)
    {
        Name = name?.Trim() ?? string.Empty;
    }

    /// <summary>
    /// Construtor com nome e usuário executor
    /// </summary>
    /// <param name="name">Nome do container</param>
    /// <param name="executedBy">Usuário que executa a query</param>
    public GetContainerByNameQuery(string name, string executedBy) : base(executedBy)
    {
        Name = name?.Trim() ?? string.Empty;
    }
}

/// <summary>
/// Query para obter containers por status
/// </summary>
public class GetContainersByStatusQuery : BaseQuery<List<ContainerSummaryDto>>
{
    /// <summary>
    /// Status dos containers
    /// </summary>
    public ContainerStatus Status { get; set; }

    /// <summary>
    /// Construtor
    /// </summary>
    public GetContainersByStatusQuery()
    {
    }

    /// <summary>
    /// Construtor com status
    /// </summary>
    /// <param name="status">Status dos containers</param>
    public GetContainersByStatusQuery(ContainerStatus status)
    {
        Status = status;
    }

    /// <summary>
    /// Construtor com status e usuário executor
    /// </summary>
    /// <param name="status">Status dos containers</param>
    /// <param name="executedBy">Usuário que executa a query</param>
    public GetContainersByStatusQuery(ContainerStatus status, string executedBy) : base(executedBy)
    {
        Status = status;
    }
}

/// <summary>
/// Query para obter containers por runtime
/// </summary>
public class GetContainersByRuntimeQuery : BaseQuery<List<ContainerSummaryDto>>
{
    /// <summary>
    /// Runtime dos containers
    /// </summary>
    public ContainerRuntime Runtime { get; set; }

    /// <summary>
    /// Construtor
    /// </summary>
    public GetContainersByRuntimeQuery()
    {
    }

    /// <summary>
    /// Construtor com runtime
    /// </summary>
    /// <param name="runtime">Runtime dos containers</param>
    public GetContainersByRuntimeQuery(ContainerRuntime runtime)
    {
        Runtime = runtime;
    }

    /// <summary>
    /// Construtor com runtime e usuário executor
    /// </summary>
    /// <param name="runtime">Runtime dos containers</param>
    /// <param name="executedBy">Usuário que executa a query</param>
    public GetContainersByRuntimeQuery(ContainerRuntime runtime, string executedBy) : base(executedBy)
    {
        Runtime = runtime;
    }
}

/// <summary>
/// Query para obter containers por imagem
/// </summary>
public class GetContainersByImageQuery : BaseQuery<List<ContainerSummaryDto>>
{
    /// <summary>
    /// Nome da imagem
    /// </summary>
    public string ImageName { get; set; } = string.Empty;

    /// <summary>
    /// Tag da imagem (opcional)
    /// </summary>
    public string? ImageTag { get; set; }

    /// <summary>
    /// Construtor
    /// </summary>
    public GetContainersByImageQuery()
    {
    }

    /// <summary>
    /// Construtor com imagem
    /// </summary>
    /// <param name="imageName">Nome da imagem</param>
    /// <param name="imageTag">Tag da imagem</param>
    public GetContainersByImageQuery(string imageName, string? imageTag = null)
    {
        ImageName = imageName?.Trim() ?? string.Empty;
        ImageTag = imageTag?.Trim();
    }

    /// <summary>
    /// Construtor completo
    /// </summary>
    /// <param name="imageName">Nome da imagem</param>
    /// <param name="imageTag">Tag da imagem</param>
    /// <param name="executedBy">Usuário que executa a query</param>
    public GetContainersByImageQuery(string imageName, string? imageTag, string executedBy) : base(executedBy)
    {
        ImageName = imageName?.Trim() ?? string.Empty;
        ImageTag = imageTag?.Trim();
    }
}

/// <summary>
/// Query para obter todos os containers paginados
/// </summary>
public class GetContainersPagedQuery : BaseSortedQuery<PagedContainerResultDto>
{
    /// <summary>
    /// Filtro por status (opcional)
    /// </summary>
    public ContainerStatus? StatusFilter { get; set; }

    /// <summary>
    /// Filtro por runtime (opcional)
    /// </summary>
    public ContainerRuntime? RuntimeFilter { get; set; }

    /// <summary>
    /// Filtro por usuário criador (opcional)
    /// </summary>
    public string? CreatedByFilter { get; set; }

    /// <summary>
    /// Incluir containers excluídos
    /// </summary>
    public bool IncludeDeleted { get; set; }

    /// <summary>
    /// Construtor
    /// </summary>
    public GetContainersPagedQuery()
    {
        SortBy = "CreatedAt";
        SortAscending = false; // Mais recentes primeiro
    }

    /// <summary>
    /// Construtor com paginação
    /// </summary>
    /// <param name="pageNumber">Número da página</param>
    /// <param name="pageSize">Tamanho da página</param>
    public GetContainersPagedQuery(int pageNumber, int pageSize) : base("CreatedAt", false)
    {
        PageNumber = pageNumber;
        PageSize = pageSize;
    }

    /// <summary>
    /// Construtor completo
    /// </summary>
    /// <param name="executedBy">Usuário que executa a query</param>
    /// <param name="pageNumber">Número da página</param>
    /// <param name="pageSize">Tamanho da página</param>
    /// <param name="sortBy">Campo de ordenação</param>
    /// <param name="sortAscending">Direção da ordenação</param>
    public GetContainersPagedQuery(string executedBy, int pageNumber, int pageSize, string sortBy = "CreatedAt", bool sortAscending = false) 
        : base(executedBy, pageNumber, pageSize, sortBy, sortAscending)
    {
    }
}

/// <summary>
/// Query para buscar containers
/// </summary>
public class SearchContainersQuery : BaseSearchQuery<PagedContainerResultDto>
{
    /// <summary>
    /// Filtro por status (opcional)
    /// </summary>
    public ContainerStatus? StatusFilter { get; set; }

    /// <summary>
    /// Filtro por runtime (opcional)
    /// </summary>
    public ContainerRuntime? RuntimeFilter { get; set; }

    /// <summary>
    /// Incluir containers excluídos
    /// </summary>
    public bool IncludeDeleted { get; set; }

    /// <summary>
    /// Construtor
    /// </summary>
    public SearchContainersQuery()
    {
    }

    /// <summary>
    /// Construtor com busca
    /// </summary>
    /// <param name="searchText">Texto de busca</param>
    public SearchContainersQuery(string searchText) : base(searchText)
    {
    }

    /// <summary>
    /// Construtor com busca e paginação
    /// </summary>
    /// <param name="searchText">Texto de busca</param>
    /// <param name="pageNumber">Número da página</param>
    /// <param name="pageSize">Tamanho da página</param>
    public SearchContainersQuery(string searchText, int pageNumber, int pageSize) : base(searchText, pageNumber, pageSize)
    {
    }

    /// <summary>
    /// Construtor completo
    /// </summary>
    /// <param name="executedBy">Usuário que executa a query</param>
    /// <param name="searchText">Texto de busca</param>
    /// <param name="pageNumber">Número da página</param>
    /// <param name="pageSize">Tamanho da página</param>
    public SearchContainersQuery(string executedBy, string searchText, int pageNumber, int pageSize) 
        : base(executedBy, searchText, pageNumber, pageSize)
    {
    }
}

/// <summary>
/// Query para obter estatísticas de containers
/// </summary>
public class GetContainerStatisticsQuery : BaseQuery<ContainerStatisticsDto>
{
    /// <summary>
    /// Incluir containers excluídos nas estatísticas
    /// </summary>
    public bool IncludeDeleted { get; set; }

    /// <summary>
    /// Construtor
    /// </summary>
    public GetContainerStatisticsQuery()
    {
    }

    /// <summary>
    /// Construtor com usuário executor
    /// </summary>
    /// <param name="executedBy">Usuário que executa a query</param>
    public GetContainerStatisticsQuery(string executedBy) : base(executedBy)
    {
    }
}

/// <summary>
/// Query para obter containers em execução
/// </summary>
public class GetRunningContainersQuery : BaseQuery<List<ContainerSummaryDto>>
{
    /// <summary>
    /// Filtro por runtime (opcional)
    /// </summary>
    public ContainerRuntime? RuntimeFilter { get; set; }

    /// <summary>
    /// Construtor
    /// </summary>
    public GetRunningContainersQuery()
    {
    }

    /// <summary>
    /// Construtor com usuário executor
    /// </summary>
    /// <param name="executedBy">Usuário que executa a query</param>
    public GetRunningContainersQuery(string executedBy) : base(executedBy)
    {
    }
}

/// <summary>
/// Query para obter containers parados
/// </summary>
public class GetStoppedContainersQuery : BaseQuery<List<ContainerSummaryDto>>
{
    /// <summary>
    /// Filtro por runtime (opcional)
    /// </summary>
    public ContainerRuntime? RuntimeFilter { get; set; }

    /// <summary>
    /// Construtor
    /// </summary>
    public GetStoppedContainersQuery()
    {
    }

    /// <summary>
    /// Construtor com usuário executor
    /// </summary>
    /// <param name="executedBy">Usuário que executa a query</param>
    public GetStoppedContainersQuery(string executedBy) : base(executedBy)
    {
    }
}
