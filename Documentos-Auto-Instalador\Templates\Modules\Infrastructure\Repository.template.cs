using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using AutoInstaller.Core.Modules.{{ModuleName}}.Entities;
using AutoInstaller.Core.Modules.{{ModuleName}}.Repositories;
using AutoInstaller.Core.Shared.ValueObjects;
using AutoInstaller.Infrastructure.Data;
using AutoInstaller.Infrastructure.Repositories.Base;
using System.Linq.Expressions;

namespace AutoInstaller.Infrastructure.Modules.{{ModuleName}}.Repositories;

/// <summary>
/// Implementação do repositório para {{EntityName}}
/// </summary>
public class {{EntityName}}Repository : BaseRepository<{{EntityName}}>, I{{EntityName}}Repository
{
    public {{EntityName}}Repository(
        ApplicationDbContext context,
        ILogger<{{EntityName}}Repository> logger) 
        : base(context, logger)
    {
    }

    #region Consultas Específicas

    public async Task<{{EntityName}}?> GetByNameAsync(string name, CancellationToken cancellationToken = default)
    {
        Logger.LogDebug("Buscando {{EntityName}} por nome: {Name}", name);
        
        try
        {
            return await DbSet
                .FirstOrDefaultAsync(x => x.Name == name && !x.IsDeleted, cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao buscar {{EntityName}} por nome: {Name}", name);
            throw;
        }
    }

    public async Task<IEnumerable<{{EntityName}}>> GetByStatusAsync(
        {{EntityName}}Status status, 
        CancellationToken cancellationToken = default)
    {
        Logger.LogDebug("Buscando {{EntityName}}s por status: {Status}", status);
        
        try
        {
            return await DbSet
                .Where(x => x.Status == status && !x.IsDeleted)
                .OrderBy(x => x.Name)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao buscar {{EntityName}}s por status: {Status}", status);
            throw;
        }
    }

    public async Task<IEnumerable<{{EntityName}}>> GetByUserAsync(
        string userId, 
        CancellationToken cancellationToken = default)
    {
        Logger.LogDebug("Buscando {{EntityName}}s por usuário: {UserId}", userId);
        
        try
        {
            return await DbSet
                .Where(x => x.CreatedBy == userId && !x.IsDeleted)
                .OrderByDescending(x => x.CreatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao buscar {{EntityName}}s por usuário: {UserId}", userId);
            throw;
        }
    }

    public async Task<IEnumerable<{{EntityName}}>> GetByPeriodAsync(
        DateTime startDate, 
        DateTime endDate, 
        CancellationToken cancellationToken = default)
    {
        Logger.LogDebug("Buscando {{EntityName}}s por período: {StartDate} - {EndDate}", startDate, endDate);
        
        try
        {
            return await DbSet
                .Where(x => x.CreatedAt >= startDate && 
                           x.CreatedAt <= endDate && 
                           !x.IsDeleted)
                .OrderByDescending(x => x.CreatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao buscar {{EntityName}}s por período: {StartDate} - {EndDate}", startDate, endDate);
            throw;
        }
    }

    #endregion

    #region Consultas Paginadas

    public async Task<PagedResult<{{EntityName}}>> GetPagedAsync(
        int page, 
        int pageSize, 
        string? searchTerm = null,
        {{EntityName}}Status? statusFilter = null,
        string? sortBy = null,
        bool sortDescending = false,
        CancellationToken cancellationToken = default)
    {
        Logger.LogDebug("Buscando {{EntityName}}s paginados: Page={Page}, PageSize={PageSize}, Search={SearchTerm}, Status={StatusFilter}", 
            page, pageSize, searchTerm, statusFilter);
        
        try
        {
            var query = DbSet.Where(x => !x.IsDeleted);

            // Aplicar filtro de busca
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                query = query.Where(x => 
                    x.Name.Contains(searchTerm) || 
                    x.Description.Contains(searchTerm));
            }

            // Aplicar filtro de status
            if (statusFilter.HasValue)
            {
                query = query.Where(x => x.Status == statusFilter.Value);
            }

            // Aplicar ordenação
            query = ApplySorting(query, sortBy, sortDescending);

            // Contar total de registros
            var totalCount = await query.CountAsync(cancellationToken);

            // Aplicar paginação
            var items = await query
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync(cancellationToken);

            return new PagedResult<{{EntityName}}>(
                items,
                totalCount,
                page,
                pageSize);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao buscar {{EntityName}}s paginados");
            throw;
        }
    }

    #endregion

    #region Verificações de Existência

    public async Task<bool> ExistsByNameAsync(
        string name, 
        Guid? excludeId = null, 
        CancellationToken cancellationToken = default)
    {
        Logger.LogDebug("Verificando existência de {{EntityName}} por nome: {Name}, ExcludeId: {ExcludeId}", name, excludeId);
        
        try
        {
            var query = DbSet.Where(x => x.Name == name && !x.IsDeleted);
            
            if (excludeId.HasValue)
            {
                query = query.Where(x => x.Id != excludeId.Value);
            }
            
            return await query.AnyAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao verificar existência de {{EntityName}} por nome: {Name}", name);
            throw;
        }
    }

    public async Task<bool> HasActiveAsync(CancellationToken cancellationToken = default)
    {
        Logger.LogDebug("Verificando se existem {{EntityName}}s ativos");
        
        try
        {
            return await DbSet
                .AnyAsync(x => x.Status == {{EntityName}}Status.Active && !x.IsDeleted, cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao verificar {{EntityName}}s ativos");
            throw;
        }
    }

    #endregion

    #region Contagens e Estatísticas

    public async Task<int> CountByStatusAsync(
        {{EntityName}}Status status, 
        CancellationToken cancellationToken = default)
    {
        Logger.LogDebug("Contando {{EntityName}}s por status: {Status}", status);
        
        try
        {
            return await DbSet
                .CountAsync(x => x.Status == status && !x.IsDeleted, cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao contar {{EntityName}}s por status: {Status}", status);
            throw;
        }
    }

    public async Task<{{EntityName}}Statistics> GetStatisticsAsync(CancellationToken cancellationToken = default)
    {
        Logger.LogDebug("Obtendo estatísticas de {{EntityName}}s");
        
        try
        {
            var total = await DbSet.CountAsync(x => !x.IsDeleted, cancellationToken);
            var active = await DbSet.CountAsync(x => x.Status == {{EntityName}}Status.Active && !x.IsDeleted, cancellationToken);
            var inactive = await DbSet.CountAsync(x => x.Status == {{EntityName}}Status.Inactive && !x.IsDeleted, cancellationToken);
            var pending = await DbSet.CountAsync(x => x.Status == {{EntityName}}Status.Pending && !x.IsDeleted, cancellationToken);
            
            var lastWeek = DateTime.UtcNow.AddDays(-7);
            var createdLastWeek = await DbSet
                .CountAsync(x => x.CreatedAt >= lastWeek && !x.IsDeleted, cancellationToken);
            
            return new {{EntityName}}Statistics
            {
                Total = total,
                Active = active,
                Inactive = inactive,
                Pending = pending,
                CreatedLastWeek = createdLastWeek,
                LastUpdated = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao obter estatísticas de {{EntityName}}s");
            throw;
        }
    }

    public async Task<IEnumerable<{{EntityName}}CountByStatus>> GetCountByStatusAsync(CancellationToken cancellationToken = default)
    {
        Logger.LogDebug("Obtendo contagem de {{EntityName}}s por status");
        
        try
        {
            return await DbSet
                .Where(x => !x.IsDeleted)
                .GroupBy(x => x.Status)
                .Select(g => new {{EntityName}}CountByStatus
                {
                    Status = g.Key,
                    Count = g.Count()
                })
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao obter contagem de {{EntityName}}s por status");
            throw;
        }
    }

    public async Task<IEnumerable<{{EntityName}}CreationTrend>> GetCreationTrendAsync(
        int days = 30, 
        CancellationToken cancellationToken = default)
    {
        Logger.LogDebug("Obtendo tendência de criação de {{EntityName}}s para {Days} dias", days);
        
        try
        {
            var startDate = DateTime.UtcNow.AddDays(-days).Date;
            
            return await DbSet
                .Where(x => x.CreatedAt >= startDate && !x.IsDeleted)
                .GroupBy(x => x.CreatedAt.Date)
                .Select(g => new {{EntityName}}CreationTrend
                {
                    Date = g.Key,
                    Count = g.Count()
                })
                .OrderBy(x => x.Date)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao obter tendência de criação de {{EntityName}}s");
            throw;
        }
    }

    #endregion

    #region Busca Avançada

    public async Task<IEnumerable<{{EntityName}}>> SearchAsync(
        Expression<Func<{{EntityName}}, bool>> predicate,
        int? limit = null,
        CancellationToken cancellationToken = default)
    {
        Logger.LogDebug("Executando busca avançada de {{EntityName}}s com limite: {Limit}", limit);
        
        try
        {
            var query = DbSet
                .Where(x => !x.IsDeleted)
                .Where(predicate)
                .OrderByDescending(x => x.CreatedAt);
            
            if (limit.HasValue)
            {
                query = query.Take(limit.Value);
            }
            
            return await query.ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro na busca avançada de {{EntityName}}s");
            throw;
        }
    }

    #endregion

    #region Operações em Lote

    public async Task<int> BulkUpdateStatusAsync(
        IEnumerable<Guid> ids, 
        {{EntityName}}Status newStatus, 
        string updatedBy,
        CancellationToken cancellationToken = default)
    {
        Logger.LogDebug("Atualizando status em lote para {Count} {{EntityName}}s: {NewStatus}", ids.Count(), newStatus);
        
        try
        {
            var entities = await DbSet
                .Where(x => ids.Contains(x.Id) && !x.IsDeleted)
                .ToListAsync(cancellationToken);
            
            var updatedCount = 0;
            var now = DateTime.UtcNow;
            
            foreach (var entity in entities)
            {
                if (entity.Status != newStatus)
                {
                    entity.Status = newStatus;
                    entity.UpdatedAt = now;
                    entity.UpdatedBy = updatedBy;
                    updatedCount++;
                }
            }
            
            if (updatedCount > 0)
            {
                await Context.SaveChangesAsync(cancellationToken);
            }
            
            Logger.LogInformation("Status atualizado em lote para {UpdatedCount} {{EntityName}}s", updatedCount);
            return updatedCount;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro na atualização em lote de status de {{EntityName}}s");
            throw;
        }
    }

    public async Task<int> BulkDeleteAsync(
        IEnumerable<Guid> ids, 
        string deletedBy,
        bool softDelete = true,
        CancellationToken cancellationToken = default)
    {
        Logger.LogDebug("Excluindo em lote {Count} {{EntityName}}s (SoftDelete: {SoftDelete})", ids.Count(), softDelete);
        
        try
        {
            var entities = await DbSet
                .Where(x => ids.Contains(x.Id) && !x.IsDeleted)
                .ToListAsync(cancellationToken);
            
            if (!entities.Any())
            {
                return 0;
            }
            
            var deletedCount = entities.Count;
            var now = DateTime.UtcNow;
            
            if (softDelete)
            {
                foreach (var entity in entities)
                {
                    entity.IsDeleted = true;
                    entity.DeletedAt = now;
                    entity.DeletedBy = deletedBy;
                }
            }
            else
            {
                DbSet.RemoveRange(entities);
            }
            
            await Context.SaveChangesAsync(cancellationToken);
            
            Logger.LogInformation("Excluídos em lote {DeletedCount} {{EntityName}}s", deletedCount);
            return deletedCount;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro na exclusão em lote de {{EntityName}}s");
            throw;
        }
    }

    #endregion

    #region Métodos Auxiliares

    private IQueryable<{{EntityName}}> ApplySorting(
        IQueryable<{{EntityName}}> query, 
        string? sortBy, 
        bool sortDescending)
    {
        if (string.IsNullOrWhiteSpace(sortBy))
        {
            return query.OrderByDescending(x => x.CreatedAt);
        }
        
        return sortBy.ToLowerInvariant() switch
        {
            "name" => sortDescending 
                ? query.OrderByDescending(x => x.Name)
                : query.OrderBy(x => x.Name),
            "status" => sortDescending 
                ? query.OrderByDescending(x => x.Status)
                : query.OrderBy(x => x.Status),
            "createdat" => sortDescending 
                ? query.OrderByDescending(x => x.CreatedAt)
                : query.OrderBy(x => x.CreatedAt),
            "updatedat" => sortDescending 
                ? query.OrderByDescending(x => x.UpdatedAt)
                : query.OrderBy(x => x.UpdatedAt),
            _ => query.OrderByDescending(x => x.CreatedAt)
        };
    }

    #endregion

    #region Sobrescritas do BaseRepository

    public override async Task<{{EntityName}}> AddAsync({{EntityName}} entity, CancellationToken cancellationToken = default)
    {
        // Validar nome único antes de adicionar
        if (await ExistsByNameAsync(entity.Name, cancellationToken: cancellationToken))
        {
            throw new InvalidOperationException($"Já existe um {{EntityName}} com o nome '{entity.Name}'");
        }
        
        return await base.AddAsync(entity, cancellationToken);
    }

    public override async Task<{{EntityName}}> UpdateAsync({{EntityName}} entity, CancellationToken cancellationToken = default)
    {
        // Validar nome único antes de atualizar
        if (await ExistsByNameAsync(entity.Name, entity.Id, cancellationToken))
        {
            throw new InvalidOperationException($"Já existe um {{EntityName}} com o nome '{entity.Name}'");
        }
        
        return await base.UpdateAsync(entity, cancellationToken);
    }

    #endregion
}