using AutoInstaller.Core.Entities;
using AutoInstaller.Core.Interfaces;
using Docker.DotNet;
using Docker.DotNet.Models;
using Microsoft.Extensions.Logging;
using System.Runtime.InteropServices;

namespace AutoInstaller.Infrastructure.Services;

/// <summary>
/// Serviço para integração com Docker usando Docker.DotNet
/// </summary>
public class DockerService : IContainerService, IDisposable
{
    private readonly IDockerClient _dockerClient;
    private readonly ILogger<DockerService> _logger;
    private bool _disposed;

    /// <summary>
    /// Runtime suportado
    /// </summary>
    public ContainerRuntime SupportedRuntime => ContainerRuntime.Docker;

    /// <summary>
    /// Construtor
    /// </summary>
    /// <param name="logger">Logger</param>
    public DockerService(ILogger<DockerService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        try
        {
            // Configurar cliente Docker baseado no sistema operacional
            var dockerUri = GetDockerUri();
            var config = new DockerClientConfiguration(dockerUri);
            _dockerClient = config.CreateClient();
            
            _logger.LogInformation("DockerService inicializado com URI: {DockerUri}", dockerUri);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao inicializar DockerService");
            throw;
        }
    }

    /// <summary>
    /// Obtém a URI do Docker baseada no sistema operacional
    /// </summary>
    /// <returns>URI do Docker</returns>
    private static Uri GetDockerUri()
    {
        if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
        {
            return new Uri("npipe://./pipe/docker_engine");
        }
        else
        {
            return new Uri("unix:///var/run/docker.sock");
        }
    }

    /// <summary>
    /// Verifica se o Docker está disponível
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se disponível</returns>
    public async Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            await _dockerClient.System.PingAsync(cancellationToken);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Docker não está disponível");
            return false;
        }
    }

    /// <summary>
    /// Obtém informações do Docker
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Informações do runtime</returns>
    public async Task<RuntimeInfo> GetRuntimeInfoAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var version = await _dockerClient.System.GetVersionAsync(cancellationToken);
            var info = await _dockerClient.System.GetSystemInfoAsync(cancellationToken);

            return new RuntimeInfo
            {
                Name = "Docker",
                Version = version.Version,
                ApiVersion = version.APIVersion,
                OperatingSystem = info.OperatingSystem,
                Architecture = info.Architecture,
                CpuCount = info.NCPU,
                TotalMemory = info.MemTotal,
                IsRunning = true
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter informações do Docker");
            throw;
        }
    }

    /// <summary>
    /// Lista todos os containers
    /// </summary>
    /// <param name="includeAll">Incluir containers parados</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de containers</returns>
    public async Task<IReadOnlyList<ContainerInfo>> ListContainersAsync(bool includeAll = true, CancellationToken cancellationToken = default)
    {
        try
        {
            var parameters = new ContainersListParameters
            {
                All = includeAll
            };

            var containers = await _dockerClient.Containers.ListContainersAsync(parameters, cancellationToken);
            
            return containers.Select(MapToContainerInfo).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao listar containers Docker");
            throw;
        }
    }

    /// <summary>
    /// Obtém informações de um container específico
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Informações do container</returns>
    public async Task<ContainerInfo?> GetContainerAsync(string containerId, CancellationToken cancellationToken = default)
    {
        try
        {
            var container = await _dockerClient.Containers.InspectContainerAsync(containerId, cancellationToken);
            return MapToContainerInfo(container);
        }
        catch (DockerContainerNotFoundException)
        {
            _logger.LogWarning("Container {ContainerId} não encontrado", containerId);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter container {ContainerId}", containerId);
            throw;
        }
    }

    /// <summary>
    /// Cria um novo container
    /// </summary>
    /// <param name="request">Requisição de criação</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>ID do container criado</returns>
    public async Task<string> CreateContainerAsync(CreateContainerRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var createParams = new CreateContainerParameters
            {
                Name = request.Name,
                Image = $"{request.Image}:{request.Tag}",
                Env = request.Environment.Select(kv => $"{kv.Key}={kv.Value}").ToList(),
                Labels = request.Labels,
                WorkingDir = request.WorkingDirectory,
                User = request.User,
                HostConfig = new HostConfig
                {
                    RestartPolicy = new RestartPolicy
                    {
                        Name = MapRestartPolicy(request.RestartPolicy)
                    },
                    Memory = request.MemoryLimit,
                    NanoCPUs = request.CpuLimit.HasValue ? (long)(request.CpuLimit.Value * 1_000_000_000) : null,
                    PortBindings = MapPortBindings(request.Ports),
                    Binds = request.Volumes.Select(kv => $"{kv.Key}:{kv.Value}").ToList()
                }
            };

            // Configurar comando se fornecido
            if (!string.IsNullOrWhiteSpace(request.Command))
            {
                var cmd = new List<string> { request.Command };
                if (request.Arguments != null)
                {
                    cmd.AddRange(request.Arguments);
                }
                createParams.Cmd = cmd;
            }

            // Configurar portas expostas
            if (request.Ports.Any())
            {
                createParams.ExposedPorts = request.Ports.Keys.ToDictionary(
                    port => port,
                    _ => new EmptyStruct()
                );
            }

            var response = await _dockerClient.Containers.CreateContainerAsync(createParams, cancellationToken);
            
            _logger.LogInformation("Container Docker criado: {ContainerId}", response.ID);
            return response.ID;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao criar container Docker {ContainerName}", request.Name);
            throw;
        }
    }

    /// <summary>
    /// Inicia um container
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se iniciado com sucesso</returns>
    public async Task<bool> StartContainerAsync(string containerId, CancellationToken cancellationToken = default)
    {
        try
        {
            var parameters = new ContainerStartParameters();
            var result = await _dockerClient.Containers.StartContainerAsync(containerId, parameters, cancellationToken);
            
            _logger.LogInformation("Container Docker iniciado: {ContainerId}", containerId);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao iniciar container Docker {ContainerId}", containerId);
            return false;
        }
    }

    /// <summary>
    /// Para um container
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="timeoutSeconds">Timeout em segundos</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se parado com sucesso</returns>
    public async Task<bool> StopContainerAsync(string containerId, int timeoutSeconds = 10, CancellationToken cancellationToken = default)
    {
        try
        {
            var parameters = new ContainerStopParameters
            {
                WaitBeforeKillSeconds = (uint)timeoutSeconds
            };

            var result = await _dockerClient.Containers.StopContainerAsync(containerId, parameters, cancellationToken);
            
            _logger.LogInformation("Container Docker parado: {ContainerId}", containerId);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao parar container Docker {ContainerId}", containerId);
            return false;
        }
    }

    /// <summary>
    /// Reinicia um container
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="timeoutSeconds">Timeout em segundos</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se reiniciado com sucesso</returns>
    public async Task<bool> RestartContainerAsync(string containerId, int timeoutSeconds = 10, CancellationToken cancellationToken = default)
    {
        try
        {
            var parameters = new ContainerRestartParameters
            {
                WaitBeforeKillSeconds = (uint)timeoutSeconds
            };

            await _dockerClient.Containers.RestartContainerAsync(containerId, parameters, cancellationToken);
            
            _logger.LogInformation("Container Docker reiniciado: {ContainerId}", containerId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao reiniciar container Docker {ContainerId}", containerId);
            return false;
        }
    }

    /// <summary>
    /// Pausa um container
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se pausado com sucesso</returns>
    public async Task<bool> PauseContainerAsync(string containerId, CancellationToken cancellationToken = default)
    {
        try
        {
            await _dockerClient.Containers.PauseContainerAsync(containerId, cancellationToken);
            
            _logger.LogInformation("Container Docker pausado: {ContainerId}", containerId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao pausar container Docker {ContainerId}", containerId);
            return false;
        }
    }

    /// <summary>
    /// Despausa um container
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se despausado com sucesso</returns>
    public async Task<bool> UnpauseContainerAsync(string containerId, CancellationToken cancellationToken = default)
    {
        try
        {
            await _dockerClient.Containers.UnpauseContainerAsync(containerId, cancellationToken);
            
            _logger.LogInformation("Container Docker despausado: {ContainerId}", containerId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao despausar container Docker {ContainerId}", containerId);
            return false;
        }
    }

    /// <summary>
    /// Remove um container
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="force">Forçar remoção</param>
    /// <param name="removeVolumes">Remover volumes associados</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se removido com sucesso</returns>
    public async Task<bool> RemoveContainerAsync(string containerId, bool force = false, bool removeVolumes = false, CancellationToken cancellationToken = default)
    {
        try
        {
            var parameters = new ContainerRemoveParameters
            {
                Force = force,
                RemoveVolumes = removeVolumes
            };

            await _dockerClient.Containers.RemoveContainerAsync(containerId, parameters, cancellationToken);
            
            _logger.LogInformation("Container Docker removido: {ContainerId}", containerId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao remover container Docker {ContainerId}", containerId);
            return false;
        }
    }

    /// <summary>
    /// Obtém logs de um container
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="tail">Número de linhas do final</param>
    /// <param name="since">Data de início</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Logs do container</returns>
    public async Task<string> GetContainerLogsAsync(string containerId, int? tail = null, DateTime? since = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var parameters = new ContainerLogsParameters
            {
                ShowStdout = true,
                ShowStderr = true,
                Timestamps = true,
                Tail = tail?.ToString() ?? "all"
            };

            if (since.HasValue)
            {
                parameters.Since = since.Value.ToString("yyyy-MM-ddTHH:mm:ssZ");
            }

            using var stream = await _dockerClient.Containers.GetContainerLogsAsync(containerId, parameters, cancellationToken);
            using var reader = new StreamReader(stream);
            
            return await reader.ReadToEndAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter logs do container Docker {ContainerId}", containerId);
            throw;
        }
    }

    /// <summary>
    /// Obtém estatísticas de um container
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Estatísticas do container</returns>
    public async Task<ContainerStats?> GetContainerStatsAsync(string containerId, CancellationToken cancellationToken = default)
    {
        try
        {
            var parameters = new ContainerStatsParameters
            {
                Stream = false
            };

            var stats = await _dockerClient.Containers.GetContainerStatsAsync(containerId, parameters, cancellationToken);
            
            return MapToContainerStats(containerId, stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter estatísticas do container Docker {ContainerId}", containerId);
            return null;
        }
    }

    /// <summary>
    /// Lista imagens disponíveis
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de imagens</returns>
    public async Task<IReadOnlyList<ImageInfo>> ListImagesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var images = await _dockerClient.Images.ListImagesAsync(new ImagesListParameters(), cancellationToken);
            
            return images.Select(MapToImageInfo).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao listar imagens Docker");
            throw;
        }
    }

    /// <summary>
    /// Puxa uma imagem do registry
    /// </summary>
    /// <param name="imageName">Nome da imagem</param>
    /// <param name="tag">Tag da imagem</param>
    /// <param name="progress">Callback de progresso</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se puxada com sucesso</returns>
    public async Task<bool> PullImageAsync(string imageName, string tag = "latest", IProgress<PullProgress>? progress = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var parameters = new ImagesPullParameters
            {
                FromImage = imageName,
                Tag = tag
            };

            var progressHandler = progress != null ? new Progress<JSONMessage>(msg =>
            {
                if (msg.Status != null)
                {
                    progress.Report(new PullProgress
                    {
                        Status = msg.Status,
                        Id = msg.ID,
                        Current = msg.Progress?.Current ?? 0,
                        Total = msg.Progress?.Total ?? 0
                    });
                }
            }) : null;

            await _dockerClient.Images.CreateImageAsync(parameters, null, progressHandler, cancellationToken);
            
            _logger.LogInformation("Imagem Docker puxada: {ImageName}:{Tag}", imageName, tag);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao puxar imagem Docker {ImageName}:{Tag}", imageName, tag);
            return false;
        }
    }

    /// <summary>
    /// Remove uma imagem
    /// </summary>
    /// <param name="imageId">ID da imagem</param>
    /// <param name="force">Forçar remoção</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se removida com sucesso</returns>
    public async Task<bool> RemoveImageAsync(string imageId, bool force = false, CancellationToken cancellationToken = default)
    {
        try
        {
            var parameters = new ImageDeleteParameters
            {
                Force = force
            };

            await _dockerClient.Images.DeleteImageAsync(imageId, parameters, cancellationToken);
            
            _logger.LogInformation("Imagem Docker removida: {ImageId}", imageId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao remover imagem Docker {ImageId}", imageId);
            return false;
        }
    }

    /// <summary>
    /// Mapeia ContainerListResponse para ContainerInfo
    /// </summary>
    /// <param name="container">Container do Docker</param>
    /// <returns>ContainerInfo</returns>
    private static ContainerInfo MapToContainerInfo(ContainerListResponse container)
    {
        return new ContainerInfo
        {
            Id = container.ID,
            Name = container.Names.FirstOrDefault()?.TrimStart('/') ?? string.Empty,
            Image = container.Image,
            Status = container.Status,
            State = container.State,
            Created = container.Created,
            Ports = container.Ports?.ToDictionary(
                p => $"{p.PrivatePort}/{p.Type}",
                p => p.PublicPort?.ToString() ?? string.Empty
            ) ?? new Dictionary<string, string>(),
            Labels = container.Labels ?? new Dictionary<string, string>(),
            Mounts = container.Mounts?.Select(m => $"{m.Source}:{m.Destination}").ToList() ?? new List<string>()
        };
    }

    /// <summary>
    /// Mapeia ContainerInspectResponse para ContainerInfo
    /// </summary>
    /// <param name="container">Container inspecionado</param>
    /// <returns>ContainerInfo</returns>
    private static ContainerInfo MapToContainerInfo(ContainerInspectResponse container)
    {
        return new ContainerInfo
        {
            Id = container.ID,
            Name = container.Name?.TrimStart('/') ?? string.Empty,
            Image = container.Config?.Image ?? string.Empty,
            Status = container.State?.Status ?? string.Empty,
            State = container.State?.Status ?? string.Empty,
            Created = container.Created,
            StartedAt = container.State?.StartedAt,
            FinishedAt = container.State?.FinishedAt,
            Ports = container.NetworkSettings?.Ports?.ToDictionary(
                kv => kv.Key,
                kv => kv.Value?.FirstOrDefault()?.HostPort ?? string.Empty
            ) ?? new Dictionary<string, string>(),
            Labels = container.Config?.Labels ?? new Dictionary<string, string>(),
            Mounts = container.Mounts?.Select(m => $"{m.Source}:{m.Destination}").ToList() ?? new List<string>()
        };
    }

    /// <summary>
    /// Mapeia ContainerStatsResponse para ContainerStats
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="stats">Estatísticas do Docker</param>
    /// <returns>ContainerStats</returns>
    private static ContainerStats MapToContainerStats(string containerId, ContainerStatsResponse stats)
    {
        var cpuUsage = CalculateCpuUsagePercent(stats);
        var memoryUsage = stats.MemoryStats?.Usage ?? 0;
        var memoryLimit = stats.MemoryStats?.Limit ?? 0;
        var memoryPercent = memoryLimit > 0 ? (double)memoryUsage / memoryLimit * 100 : 0;

        return new ContainerStats
        {
            ContainerId = containerId,
            CpuUsagePercent = cpuUsage,
            MemoryUsage = (long)memoryUsage,
            MemoryLimit = (long)memoryLimit,
            MemoryUsagePercent = memoryPercent,
            NetworkRxBytes = stats.Networks?.Values.Sum(n => (long)n.RxBytes) ?? 0,
            NetworkTxBytes = stats.Networks?.Values.Sum(n => (long)n.TxBytes) ?? 0,
            BlockReadBytes = stats.BlkioStats?.IoServiceBytesRecursive?.Where(io => io.Op == "Read").Sum(io => (long)io.Value) ?? 0,
            BlockWriteBytes = stats.BlkioStats?.IoServiceBytesRecursive?.Where(io => io.Op == "Write").Sum(io => (long)io.Value) ?? 0,
            Timestamp = DateTime.UtcNow
        };
    }

    /// <summary>
    /// Calcula o percentual de uso de CPU
    /// </summary>
    /// <param name="stats">Estatísticas do container</param>
    /// <returns>Percentual de CPU</returns>
    private static double CalculateCpuUsagePercent(ContainerStatsResponse stats)
    {
        if (stats.CPUStats?.CPUUsage?.TotalUsage == null || stats.PreCPUStats?.CPUUsage?.TotalUsage == null)
            return 0;

        var cpuDelta = stats.CPUStats.CPUUsage.TotalUsage - stats.PreCPUStats.CPUUsage.TotalUsage;
        var systemDelta = stats.CPUStats.SystemUsage - stats.PreCPUStats.SystemUsage;

        if (systemDelta > 0 && cpuDelta > 0)
        {
            var cpuCount = stats.CPUStats.OnlineCPUs ?? Environment.ProcessorCount;
            return (double)cpuDelta / systemDelta * cpuCount * 100.0;
        }

        return 0;
    }

    /// <summary>
    /// Mapeia ImagesListResponse para ImageInfo
    /// </summary>
    /// <param name="image">Imagem do Docker</param>
    /// <returns>ImageInfo</returns>
    private static ImageInfo MapToImageInfo(ImagesListResponse image)
    {
        return new ImageInfo
        {
            Id = image.ID,
            RepoTags = image.RepoTags?.ToList() ?? new List<string>(),
            Size = image.Size,
            Created = DateTimeOffset.FromUnixTimeSeconds(image.Created).DateTime,
            Labels = image.Labels ?? new Dictionary<string, string>()
        };
    }

    /// <summary>
    /// Mapeia política de reinicialização
    /// </summary>
    /// <param name="policy">Política</param>
    /// <returns>Política mapeada</returns>
    private static RestartPolicyKind MapRestartPolicy(string policy)
    {
        return policy.ToLowerInvariant() switch
        {
            "no" => RestartPolicyKind.No,
            "always" => RestartPolicyKind.Always,
            "unless-stopped" => RestartPolicyKind.UnlessStopped,
            "on-failure" => RestartPolicyKind.OnFailure,
            _ => RestartPolicyKind.No
        };
    }

    /// <summary>
    /// Mapeia port bindings
    /// </summary>
    /// <param name="ports">Portas</param>
    /// <returns>Port bindings</returns>
    private static Dictionary<string, IList<PortBinding>> MapPortBindings(Dictionary<string, string> ports)
    {
        var portBindings = new Dictionary<string, IList<PortBinding>>();

        foreach (var port in ports)
        {
            portBindings[port.Key] = new List<PortBinding>
            {
                new PortBinding
                {
                    HostPort = port.Value
                }
            };
        }

        return portBindings;
    }

    /// <summary>
    /// Dispose
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _dockerClient?.Dispose();
            _disposed = true;
        }
    }
}
