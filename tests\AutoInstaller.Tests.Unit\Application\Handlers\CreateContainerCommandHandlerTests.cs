using AutoInstaller.Application.Features.Containers.Commands;
using AutoInstaller.Application.Features.Containers.Handlers;
using AutoInstaller.Core.Entities;
using AutoInstaller.Core.Exceptions;
using AutoInstaller.Core.Interfaces;
using AutoInstaller.Core.ValueObjects;
using AutoMapper;
using Microsoft.Extensions.Logging;

namespace AutoInstaller.Tests.Unit.Application.Handlers;

/// <summary>
/// Testes para CreateContainerCommandHandler
/// </summary>
public class CreateContainerCommandHandlerTests
{
    private readonly Mock<IContainerRepository> _mockRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly Mock<ILogger<CreateContainerCommandHandler>> _mockLogger;
    private readonly CreateContainerCommandHandler _handler;
    private readonly IFixture _fixture;

    public CreateContainerCommandHandlerTests()
    {
        _mockRepository = new Mock<IContainerRepository>();
        _mockMapper = new Mock<IMapper>();
        _mockLogger = new Mock<ILogger<CreateContainerCommandHandler>>();
        _handler = new CreateContainerCommandHandler(_mockRepository.Object, _mockMapper.Object, _mockLogger.Object);
        
        _fixture = new Fixture();
        _fixture.Customize<ContainerName>(c => c.FromFactory(() => ContainerName.Create("test-container")));
        _fixture.Customize<ImageTag>(c => c.FromFactory(() => ImageTag.Create("latest")));
    }

    [Fact]
    public async Task Handle_WithValidCommand_ShouldCreateContainer()
    {
        // Arrange
        var command = new CreateContainerCommand("test-user")
        {
            Name = "test-container",
            ImageName = "nginx",
            ImageTag = "latest",
            Runtime = ContainerRuntime.Docker
        };

        var expectedContainer = new Container(
            ContainerName.Create(command.Name),
            command.ImageName,
            ImageTag.Create(command.ImageTag),
            command.Runtime,
            command.ExecutedBy);

        var expectedDto = _fixture.Create<AutoInstaller.Application.DTOs.ContainerDto>();

        _mockRepository.Setup(r => r.GetByNameAsync(It.IsAny<ContainerName>(), It.IsAny<CancellationToken>()))
                      .ReturnsAsync((Container?)null);

        _mockRepository.Setup(r => r.AddAsync(It.IsAny<Container>(), It.IsAny<CancellationToken>()))
                      .Returns(Task.CompletedTask);

        _mockMapper.Setup(m => m.Map<AutoInstaller.Application.DTOs.ContainerDto>(It.IsAny<Container>()))
                   .Returns(expectedDto);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().Be(expectedDto);

        _mockRepository.Verify(r => r.GetByNameAsync(It.Is<ContainerName>(n => n.Value == command.Name), It.IsAny<CancellationToken>()), Times.Once);
        _mockRepository.Verify(r => r.AddAsync(It.IsAny<Container>(), It.IsAny<CancellationToken>()), Times.Once);
        _mockMapper.Verify(m => m.Map<AutoInstaller.Application.DTOs.ContainerDto>(It.IsAny<Container>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithExistingContainerName_ShouldThrowEntityConflictException()
    {
        // Arrange
        var command = new CreateContainerCommand("test-user")
        {
            Name = "existing-container",
            ImageName = "nginx",
            ImageTag = "latest",
            Runtime = ContainerRuntime.Docker
        };

        var existingContainer = new Container(
            ContainerName.Create(command.Name),
            command.ImageName,
            ImageTag.Create(command.ImageTag),
            command.Runtime,
            command.ExecutedBy);

        _mockRepository.Setup(r => r.GetByNameAsync(It.IsAny<ContainerName>(), It.IsAny<CancellationToken>()))
                      .ReturnsAsync(existingContainer);

        // Act & Assert
        var act = async () => await _handler.Handle(command, CancellationToken.None);
        await act.Should().ThrowAsync<EntityConflictException>()
                 .WithMessage("Já existe um container com o nome 'existing-container'");

        _mockRepository.Verify(r => r.GetByNameAsync(It.IsAny<ContainerName>(), It.IsAny<CancellationToken>()), Times.Once);
        _mockRepository.Verify(r => r.AddAsync(It.IsAny<Container>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task Handle_WithDeletedContainerName_ShouldCreateNewContainer()
    {
        // Arrange
        var command = new CreateContainerCommand("test-user")
        {
            Name = "deleted-container",
            ImageName = "nginx",
            ImageTag = "latest",
            Runtime = ContainerRuntime.Docker
        };

        var deletedContainer = new Container(
            ContainerName.Create(command.Name),
            command.ImageName,
            ImageTag.Create(command.ImageTag),
            command.Runtime,
            command.ExecutedBy);
        deletedContainer.MarkAsDeleted("admin");

        var expectedDto = _fixture.Create<AutoInstaller.Application.DTOs.ContainerDto>();

        _mockRepository.Setup(r => r.GetByNameAsync(It.IsAny<ContainerName>(), It.IsAny<CancellationToken>()))
                      .ReturnsAsync(deletedContainer);

        _mockRepository.Setup(r => r.AddAsync(It.IsAny<Container>(), It.IsAny<CancellationToken>()))
                      .Returns(Task.CompletedTask);

        _mockMapper.Setup(m => m.Map<AutoInstaller.Application.DTOs.ContainerDto>(It.IsAny<Container>()))
                   .Returns(expectedDto);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().Be(expectedDto);

        _mockRepository.Verify(r => r.AddAsync(It.IsAny<Container>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithCommandAndArguments_ShouldSetCommandOnContainer()
    {
        // Arrange
        var command = new CreateContainerCommand("test-user")
        {
            Name = "test-container",
            ImageName = "nginx",
            ImageTag = "latest",
            Runtime = ContainerRuntime.Docker,
            Command = "/bin/bash",
            Arguments = new[] { "-c", "echo hello" }
        };

        var expectedDto = _fixture.Create<AutoInstaller.Application.DTOs.ContainerDto>();

        _mockRepository.Setup(r => r.GetByNameAsync(It.IsAny<ContainerName>(), It.IsAny<CancellationToken>()))
                      .ReturnsAsync((Container?)null);

        _mockRepository.Setup(r => r.AddAsync(It.IsAny<Container>(), It.IsAny<CancellationToken>()))
                      .Returns(Task.CompletedTask);

        _mockMapper.Setup(m => m.Map<AutoInstaller.Application.DTOs.ContainerDto>(It.IsAny<Container>()))
                   .Returns(expectedDto);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        _mockRepository.Verify(r => r.AddAsync(It.Is<Container>(c => 
            c.Command == command.Command && 
            c.Arguments != null && 
            c.Arguments.SequenceEqual(command.Arguments)), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithExecutionSettings_ShouldSetExecutionSettingsOnContainer()
    {
        // Arrange
        var command = new CreateContainerCommand("test-user")
        {
            Name = "test-container",
            ImageName = "nginx",
            ImageTag = "latest",
            Runtime = ContainerRuntime.Docker,
            WorkingDirectory = "/app",
            User = "appuser"
        };

        var expectedDto = _fixture.Create<AutoInstaller.Application.DTOs.ContainerDto>();

        _mockRepository.Setup(r => r.GetByNameAsync(It.IsAny<ContainerName>(), It.IsAny<CancellationToken>()))
                      .ReturnsAsync((Container?)null);

        _mockRepository.Setup(r => r.AddAsync(It.IsAny<Container>(), It.IsAny<CancellationToken>()))
                      .Returns(Task.CompletedTask);

        _mockMapper.Setup(m => m.Map<AutoInstaller.Application.DTOs.ContainerDto>(It.IsAny<Container>()))
                   .Returns(expectedDto);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        _mockRepository.Verify(r => r.AddAsync(It.Is<Container>(c => 
            c.WorkingDirectory == command.WorkingDirectory && 
            c.User == command.User), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithResourceLimits_ShouldSetResourceLimitsOnContainer()
    {
        // Arrange
        var command = new CreateContainerCommand("test-user")
        {
            Name = "test-container",
            ImageName = "nginx",
            ImageTag = "latest",
            Runtime = ContainerRuntime.Docker,
            MemoryLimit = 1024 * 1024 * 1024, // 1GB
            CpuLimit = 2.0
        };

        var expectedDto = _fixture.Create<AutoInstaller.Application.DTOs.ContainerDto>();

        _mockRepository.Setup(r => r.GetByNameAsync(It.IsAny<ContainerName>(), It.IsAny<CancellationToken>()))
                      .ReturnsAsync((Container?)null);

        _mockRepository.Setup(r => r.AddAsync(It.IsAny<Container>(), It.IsAny<CancellationToken>()))
                      .Returns(Task.CompletedTask);

        _mockMapper.Setup(m => m.Map<AutoInstaller.Application.DTOs.ContainerDto>(It.IsAny<Container>()))
                   .Returns(expectedDto);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        _mockRepository.Verify(r => r.AddAsync(It.Is<Container>(c => 
            c.MemoryLimit == command.MemoryLimit && 
            c.CpuLimit == command.CpuLimit), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithExposedPorts_ShouldAddPortsToContainer()
    {
        // Arrange
        var command = new CreateContainerCommand("test-user")
        {
            Name = "test-container",
            ImageName = "nginx",
            ImageTag = "latest",
            Runtime = ContainerRuntime.Docker,
            ExposedPorts = new List<AutoInstaller.Application.DTOs.CreatePortDto>
            {
                new() { Number = 80, Protocol = "TCP" },
                new() { Number = 443, Protocol = "TCP" }
            }
        };

        var expectedDto = _fixture.Create<AutoInstaller.Application.DTOs.ContainerDto>();

        _mockRepository.Setup(r => r.GetByNameAsync(It.IsAny<ContainerName>(), It.IsAny<CancellationToken>()))
                      .ReturnsAsync((Container?)null);

        _mockRepository.Setup(r => r.AddAsync(It.IsAny<Container>(), It.IsAny<CancellationToken>()))
                      .Returns(Task.CompletedTask);

        _mockMapper.Setup(m => m.Map<AutoInstaller.Application.DTOs.ContainerDto>(It.IsAny<Container>()))
                   .Returns(expectedDto);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        _mockRepository.Verify(r => r.AddAsync(It.Is<Container>(c => 
            c.ExposedPorts.Count == 2), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithEnvironmentVariables_ShouldAddEnvironmentVariablesToContainer()
    {
        // Arrange
        var command = new CreateContainerCommand("test-user")
        {
            Name = "test-container",
            ImageName = "nginx",
            ImageTag = "latest",
            Runtime = ContainerRuntime.Docker,
            EnvironmentVariables = new Dictionary<string, string>
            {
                { "NODE_ENV", "production" },
                { "PORT", "3000" }
            }
        };

        var expectedDto = _fixture.Create<AutoInstaller.Application.DTOs.ContainerDto>();

        _mockRepository.Setup(r => r.GetByNameAsync(It.IsAny<ContainerName>(), It.IsAny<CancellationToken>()))
                      .ReturnsAsync((Container?)null);

        _mockRepository.Setup(r => r.AddAsync(It.IsAny<Container>(), It.IsAny<CancellationToken>()))
                      .Returns(Task.CompletedTask);

        _mockMapper.Setup(m => m.Map<AutoInstaller.Application.DTOs.ContainerDto>(It.IsAny<Container>()))
                   .Returns(expectedDto);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        _mockRepository.Verify(r => r.AddAsync(It.Is<Container>(c => 
            c.EnvironmentVariables.Count == 2 &&
            c.EnvironmentVariables.ContainsKey("NODE_ENV") &&
            c.EnvironmentVariables.ContainsKey("PORT")), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithLabels_ShouldAddLabelsToContainer()
    {
        // Arrange
        var command = new CreateContainerCommand("test-user")
        {
            Name = "test-container",
            ImageName = "nginx",
            ImageTag = "latest",
            Runtime = ContainerRuntime.Docker,
            Labels = new Dictionary<string, string>
            {
                { "app.name", "test-app" },
                { "app.version", "1.0.0" }
            }
        };

        var expectedDto = _fixture.Create<AutoInstaller.Application.DTOs.ContainerDto>();

        _mockRepository.Setup(r => r.GetByNameAsync(It.IsAny<ContainerName>(), It.IsAny<CancellationToken>()))
                      .ReturnsAsync((Container?)null);

        _mockRepository.Setup(r => r.AddAsync(It.IsAny<Container>(), It.IsAny<CancellationToken>()))
                      .Returns(Task.CompletedTask);

        _mockMapper.Setup(m => m.Map<AutoInstaller.Application.DTOs.ContainerDto>(It.IsAny<Container>()))
                   .Returns(expectedDto);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        _mockRepository.Verify(r => r.AddAsync(It.Is<Container>(c => 
            c.Labels.Count == 2 &&
            c.Labels.ContainsKey("app.name") &&
            c.Labels.ContainsKey("app.version")), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithVolumes_ShouldAddVolumesToContainer()
    {
        // Arrange
        var command = new CreateContainerCommand("test-user")
        {
            Name = "test-container",
            ImageName = "nginx",
            ImageTag = "latest",
            Runtime = ContainerRuntime.Docker,
            Volumes = new List<string>
            {
                "/host/data:/container/data",
                "/host/logs:/container/logs"
            }
        };

        var expectedDto = _fixture.Create<AutoInstaller.Application.DTOs.ContainerDto>();

        _mockRepository.Setup(r => r.GetByNameAsync(It.IsAny<ContainerName>(), It.IsAny<CancellationToken>()))
                      .ReturnsAsync((Container?)null);

        _mockRepository.Setup(r => r.AddAsync(It.IsAny<Container>(), It.IsAny<CancellationToken>()))
                      .Returns(Task.CompletedTask);

        _mockMapper.Setup(m => m.Map<AutoInstaller.Application.DTOs.ContainerDto>(It.IsAny<Container>()))
                   .Returns(expectedDto);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        _mockRepository.Verify(r => r.AddAsync(It.Is<Container>(c => 
            c.Volumes.Count == 2), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WhenRepositoryThrowsException_ShouldPropagateException()
    {
        // Arrange
        var command = new CreateContainerCommand("test-user")
        {
            Name = "test-container",
            ImageName = "nginx",
            ImageTag = "latest",
            Runtime = ContainerRuntime.Docker
        };

        _mockRepository.Setup(r => r.GetByNameAsync(It.IsAny<ContainerName>(), It.IsAny<CancellationToken>()))
                      .ThrowsAsync(new InvalidOperationException("Database error"));

        // Act & Assert
        var act = async () => await _handler.Handle(command, CancellationToken.None);
        await act.Should().ThrowAsync<InvalidOperationException>()
                 .WithMessage("Database error");
    }

    [Fact]
    public async Task Handle_WhenMapperThrowsException_ShouldPropagateException()
    {
        // Arrange
        var command = new CreateContainerCommand("test-user")
        {
            Name = "test-container",
            ImageName = "nginx",
            ImageTag = "latest",
            Runtime = ContainerRuntime.Docker
        };

        _mockRepository.Setup(r => r.GetByNameAsync(It.IsAny<ContainerName>(), It.IsAny<CancellationToken>()))
                      .ReturnsAsync((Container?)null);

        _mockRepository.Setup(r => r.AddAsync(It.IsAny<Container>(), It.IsAny<CancellationToken>()))
                      .Returns(Task.CompletedTask);

        _mockMapper.Setup(m => m.Map<AutoInstaller.Application.DTOs.ContainerDto>(It.IsAny<Container>()))
                   .Throws(new InvalidOperationException("Mapping error"));

        // Act & Assert
        var act = async () => await _handler.Handle(command, CancellationToken.None);
        await act.Should().ThrowAsync<InvalidOperationException>()
                 .WithMessage("Mapping error");
    }

    [Fact]
    public void Constructor_WithNullRepository_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        var act = () => new CreateContainerCommandHandler(null!, _mockMapper.Object, _mockLogger.Object);
        act.Should().Throw<ArgumentNullException>()
           .WithParameterName("containerRepository");
    }

    [Fact]
    public void Constructor_WithNullMapper_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        var act = () => new CreateContainerCommandHandler(_mockRepository.Object, null!, _mockLogger.Object);
        act.Should().Throw<ArgumentNullException>()
           .WithParameterName("mapper");
    }

    [Fact]
    public void Constructor_WithNullLogger_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        var act = () => new CreateContainerCommandHandler(_mockRepository.Object, _mockMapper.Object, null!);
        act.Should().Throw<ArgumentNullException>()
           .WithParameterName("logger");
    }
}
