<Project>
  
  <!-- Configurações Globais do Projeto -->
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <WarningsNotAsErrors>CS1591</WarningsNotAsErrors>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);CS1591</NoWarn>
    <EnforceCodeStyleInBuild>true</EnforceCodeStyleInBuild>
    <EnableNETAnalyzers>true</EnableNETAnalyzers>
    <AnalysisLevel>latest</AnalysisLevel>
  </PropertyGroup>

  <!-- Informações do Assembly -->
  <PropertyGroup>
    <Product>Auto-Instalador Desktop</Product>
    <Company>Auto-Instalador Team</Company>
    <Copyright>Copyright © 2025 Auto-Instalador Team</Copyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <Version>1.0.0</Version>
    <Description>Sistema de instalação automatizada de aplicativos usando containerização</Description>
    <Authors>Auto-Instalador Team</Authors>
  </PropertyGroup>

  <!-- Configurações de Build -->
  <PropertyGroup Condition="'$(Configuration)' == 'Debug'">
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
    <Optimize>false</Optimize>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)' == 'Release'">
    <DefineConstants>TRACE</DefineConstants>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
    <Optimize>true</Optimize>
  </PropertyGroup>

  <!-- Configurações de Cobertura de Testes -->
  <PropertyGroup Condition="'$(Configuration)' == 'Debug'">
    <CollectCoverage>true</CollectCoverage>
    <CoverletOutputFormat>opencover,lcov</CoverletOutputFormat>
    <CoverletOutput>./coverage/</CoverletOutput>
    <Exclude>[*.Tests]*,[*.TestHelpers]*</Exclude>
    <ExcludeByAttribute>Obsolete,GeneratedCodeAttribute,CompilerGeneratedAttribute</ExcludeByAttribute>
    <Threshold>80</Threshold>
    <ThresholdType>line</ThresholdType>
    <ThresholdStat>total</ThresholdStat>
  </PropertyGroup>

  <!-- Configurações de Gerenciamento Central de Pacotes -->
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
  </PropertyGroup>

  <!-- Versões Padronizadas de Pacotes -->
  <ItemGroup>
    <!-- Framework Base -->
    <PackageVersion Include="Microsoft.Extensions.Hosting" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Configuration" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.Json" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Logging" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Options" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Caching.Memory" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Diagnostics.HealthChecks" Version="9.0.0" />

    <!-- Avalonia UI -->
    <PackageVersion Include="Avalonia" Version="11.3.4" />
    <PackageVersion Include="Avalonia.Desktop" Version="11.3.4" />
    <PackageVersion Include="Avalonia.Themes.Fluent" Version="11.3.4" />
    <PackageVersion Include="Avalonia.Fonts.Inter" Version="11.3.4" />
    <PackageVersion Include="Avalonia.ReactiveUI" Version="11.3.4" />
    <PackageVersion Include="Avalonia.Xaml.Behaviors" Version="11.3.0.6" />
    <PackageVersion Include="Avalonia.Controls.DataGrid" Version="11.3.4" />
    <PackageVersion Include="Avalonia.Svg.Skia" Version="11.3.0" />
    <PackageVersion Include="Avalonia.Diagnostics" Version="11.3.4" />

    <!-- ReactiveUI -->
    <PackageVersion Include="ReactiveUI" Version="20.1.63" />
    <PackageVersion Include="ReactiveUI.Fody" Version="19.5.41" />

    <!-- Docker Integration -->
    <PackageVersion Include="Docker.DotNet" Version="3.125.15" />
    <PackageVersion Include="Docker.DotNet.X509" Version="3.125.15" />

    <!-- Podman Integration -->
    <PackageVersion Include="PodmanClient.DotNet" Version="1.0.4" />

    <!-- CQRS e Mediator -->
    <PackageVersion Include="MediatR" Version="11.1.0" />
    <PackageVersion Include="MediatR.Extensions.Microsoft.DependencyInjection" Version="11.1.0" />

    <!-- Validação -->
    <PackageVersion Include="FluentValidation" Version="11.10.0" />
    <PackageVersion Include="FluentValidation.DependencyInjectionExtensions" Version="11.10.0" />

    <!-- Mapeamento -->
    <PackageVersion Include="AutoMapper" Version="12.0.1" />
    <PackageVersion Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />

    <!-- Entity Framework -->
    <PackageVersion Include="Microsoft.EntityFrameworkCore" Version="9.0.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.0" />

    <!-- Logging -->
    <PackageVersion Include="Serilog" Version="4.1.0" />
    <PackageVersion Include="Serilog.Extensions.Hosting" Version="8.0.0" />
    <PackageVersion Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageVersion Include="Serilog.Sinks.File" Version="6.0.0" />
    <PackageVersion Include="Serilog.Settings.Configuration" Version="8.0.4" />

    <!-- Serialização -->
    <PackageVersion Include="System.Text.Json" Version="9.0.0" />
    <PackageVersion Include="Newtonsoft.Json" Version="13.0.3" />

    <!-- Utilitários -->
    <PackageVersion Include="Polly" Version="8.4.2" />
    <PackageVersion Include="Polly.Extensions.Http" Version="3.0.0" />
    <PackageVersion Include="DotNetEnv" Version="3.1.1" />

    <!-- Testes -->
    <PackageVersion Include="xunit" Version="2.9.2" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="2.8.2" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.12.0" />
    <PackageVersion Include="FluentAssertions" Version="6.12.2" />
    <PackageVersion Include="Moq" Version="4.20.72" />
    <PackageVersion Include="AutoFixture" Version="4.18.1" />
    <PackageVersion Include="AutoFixture.Xunit2" Version="4.18.1" />
    <PackageVersion Include="Testcontainers" Version="4.0.0" />
    <PackageVersion Include="Testcontainers.PostgreSql" Version="4.0.0" />
    <PackageVersion Include="coverlet.collector" Version="6.0.2" />
    <PackageVersion Include="coverlet.msbuild" Version="6.0.2" />

    <!-- Avalonia Testing -->
    <PackageVersion Include="Avalonia.Headless" Version="11.3.4" />
    <PackageVersion Include="Avalonia.Headless.XUnit" Version="11.3.4" />

    <!-- ASP.NET Core Testing -->
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.Testing" Version="9.0.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.0" />

    <!-- Performance Testing -->
    <PackageVersion Include="BenchmarkDotNet" Version="0.14.0" />
    <PackageVersion Include="NBomber" Version="5.11.1" />
  </ItemGroup>

  <!-- Configurações específicas para projetos de teste -->
  <PropertyGroup Condition="$(MSBuildProjectName.EndsWith('.Tests'))">
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <!-- Configurações específicas para projetos UI -->
  <PropertyGroup Condition="$(MSBuildProjectName.EndsWith('.UI'))">
    <OutputType>WinExe</OutputType>
    <BuiltInComInteropSupport>true</BuiltInComInteropSupport>
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <AvaloniaUseCompiledBindingsByDefault>true</AvaloniaUseCompiledBindingsByDefault>
  </PropertyGroup>

</Project>
