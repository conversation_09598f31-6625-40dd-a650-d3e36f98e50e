using MediatR;
using Microsoft.Extensions.Logging;
using AutoInstaller.Application.Common;
using AutoInstaller.Application.DTOs;
using AutoInstaller.Application.Modules.{{ModuleName}}.Commands;
using AutoInstaller.Application.Modules.{{ModuleName}}.Queries;
using AutoInstaller.Application.Modules.{{ModuleName}}.Mappers;
using AutoInstaller.Core.Modules.{{ModuleName}}.Entities;
using AutoInstaller.Core.Modules.{{ModuleName}}.Interfaces;
using AutoInstaller.Core.Interfaces;
using System.Diagnostics;

namespace AutoInstaller.Application.Modules.{{ModuleName}}.Handlers;

/// <summary>
/// Handler para comandos de criação de {{EntityName}}
/// </summary>
public class Create{{EntityName}}Handler : IRequestHandler<Create{{EntityName}}Command, Result<{{EntityName}}Dto>>
{
    private readonly I{{EntityName}}Repository _repository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<Create{{EntityName}}Handler> _logger;
    private readonly I{{EntityName}}Mapper _mapper;
    
    public Create{{EntityName}}Handler(
        I{{EntityName}}Repository repository,
        IUnitOfWork unitOfWork,
        ILogger<Create{{EntityName}}Handler> logger,
        I{{EntityName}}Mapper mapper)
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
        _logger = logger;
        _mapper = mapper;
    }
    
    public async Task<Result<{{EntityName}}Dto>> Handle(Create{{EntityName}}Command request, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            _logger.LogInformation("Iniciando criação de {{EntityName}}: {Name}", request.Name);
            
            // Verificar se já existe um {{EntityName}} com o mesmo nome
            var existingEntity = await _repository.GetByNameAsync(request.Name, cancellationToken);
            if (existingEntity != null)
            {
                _logger.LogWarning("{{EntityName}} com nome '{Name}' já existe", request.Name);
                return Result<{{EntityName}}Dto>.Failure("Já existe um {{EntityName}} com este nome");
            }
            
            // Criar nova entidade
            var entity = new {{EntityName}}(
                request.Name,
                request.Description ?? string.Empty,
                request.CreatedBy);
            
            // Validar entidade
            if (!entity.IsValid())
            {
                var errors = entity.GetValidationErrors();
                _logger.LogWarning("{{EntityName}} inválido: {Errors}", string.Join(", ", errors));
                return Result<{{EntityName}}Dto>.Failure($"{{EntityName}} inválido: {string.Join(", ", errors)}");
            }
            
            // Salvar no repositório
            await _repository.AddAsync(entity, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            // Mapear para DTO
            var dto = _mapper.ToDto(entity);
            
            stopwatch.Stop();
            _logger.LogInformation(
                "{{EntityName}} criado com sucesso: {Id} em {Duration}ms", 
                entity.Id, 
                stopwatch.ElapsedMilliseconds);
            
            return Result<{{EntityName}}Dto>.Success(dto);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, 
                "Erro ao criar {{EntityName}}: {Name} após {Duration}ms", 
                request.Name, 
                stopwatch.ElapsedMilliseconds);
            
            return Result<{{EntityName}}Dto>.Failure($"Erro interno: {ex.Message}");
        }
    }
}

/// <summary>
/// Handler para comandos de atualização de {{EntityName}}
/// </summary>
public class Update{{EntityName}}Handler : IRequestHandler<Update{{EntityName}}Command, Result<{{EntityName}}Dto>>
{
    private readonly I{{EntityName}}Repository _repository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<Update{{EntityName}}Handler> _logger;
    private readonly I{{EntityName}}Mapper _mapper;
    
    public Update{{EntityName}}Handler(
        I{{EntityName}}Repository repository,
        IUnitOfWork unitOfWork,
        ILogger<Update{{EntityName}}Handler> logger,
        I{{EntityName}}Mapper mapper)
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
        _logger = logger;
        _mapper = mapper;
    }
    
    public async Task<Result<{{EntityName}}Dto>> Handle(Update{{EntityName}}Command request, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            _logger.LogInformation("Iniciando atualização de {{EntityName}}: {Id}", request.Id);
            
            // Buscar entidade existente
            var entity = await _repository.GetByIdAsync(request.Id, cancellationToken);
            if (entity == null)
            {
                _logger.LogWarning("{{EntityName}} não encontrado: {Id}", request.Id);
                return Result<{{EntityName}}Dto>.Failure("{{EntityName}} não encontrado");
            }
            
            // Verificar se o novo nome já existe (excluindo a entidade atual)
            if (entity.Name != request.Name)
            {
                var existingWithName = await _repository.ExistsByNameAsync(request.Name, request.Id, cancellationToken);
                if (existingWithName)
                {
                    _logger.LogWarning("{{EntityName}} com nome '{Name}' já existe", request.Name);
                    return Result<{{EntityName}}Dto>.Failure("Já existe um {{EntityName}} com este nome");
                }
            }
            
            // Atualizar propriedades
            entity.UpdateName(request.Name, request.UpdatedBy);
            entity.UpdateDescription(request.Description ?? string.Empty, request.UpdatedBy);
            
            // Validar entidade
            if (!entity.IsValid())
            {
                var errors = entity.GetValidationErrors();
                _logger.LogWarning("{{EntityName}} inválido após atualização: {Errors}", string.Join(", ", errors));
                return Result<{{EntityName}}Dto>.Failure($"{{EntityName}} inválido: {string.Join(", ", errors)}");
            }
            
            // Salvar alterações
            await _repository.UpdateAsync(entity, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            // Mapear para DTO
            var dto = _mapper.ToDto(entity);
            
            stopwatch.Stop();
            _logger.LogInformation(
                "{{EntityName}} atualizado com sucesso: {Id} em {Duration}ms", 
                entity.Id, 
                stopwatch.ElapsedMilliseconds);
            
            return Result<{{EntityName}}Dto>.Success(dto);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, 
                "Erro ao atualizar {{EntityName}}: {Id} após {Duration}ms", 
                request.Id, 
                stopwatch.ElapsedMilliseconds);
            
            return Result<{{EntityName}}Dto>.Failure($"Erro interno: {ex.Message}");
        }
    }
}

/// <summary>
/// Handler para queries de busca de {{EntityName}}
/// </summary>
public class Get{{EntityName}}Handler : IRequestHandler<Get{{EntityName}}Query, Result<{{EntityName}}Dto>>
{
    private readonly I{{EntityName}}Repository _repository;
    private readonly ILogger<Get{{EntityName}}Handler> _logger;
    private readonly I{{EntityName}}Mapper _mapper;
    
    public Get{{EntityName}}Handler(
        I{{EntityName}}Repository repository,
        ILogger<Get{{EntityName}}Handler> logger,
        I{{EntityName}}Mapper mapper)
    {
        _repository = repository;
        _logger = logger;
        _mapper = mapper;
    }
    
    public async Task<Result<{{EntityName}}Dto>> Handle(Get{{EntityName}}Query request, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            _logger.LogDebug("Buscando {{EntityName}}: {Id}", request.Id);
            
            var entity = await _repository.GetByIdAsync(request.Id, cancellationToken);
            if (entity == null)
            {
                _logger.LogWarning("{{EntityName}} não encontrado: {Id}", request.Id);
                return Result<{{EntityName}}Dto>.Failure("{{EntityName}} não encontrado");
            }
            
            var dto = _mapper.ToDto(entity);
            
            stopwatch.Stop();
            _logger.LogDebug(
                "{{EntityName}} encontrado: {Id} em {Duration}ms", 
                entity.Id, 
                stopwatch.ElapsedMilliseconds);
            
            return Result<{{EntityName}}Dto>.Success(dto);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, 
                "Erro ao buscar {{EntityName}}: {Id} após {Duration}ms", 
                request.Id, 
                stopwatch.ElapsedMilliseconds);
            
            return Result<{{EntityName}}Dto>.Failure($"Erro interno: {ex.Message}");
        }
    }
}

/// <summary>
/// Handler para queries de listagem de {{EntityName}}
/// </summary>
public class Get{{EntityName}}ListHandler : IRequestHandler<Get{{EntityName}}ListQuery, Result<IEnumerable<{{EntityName}}Dto>>>
{
    private readonly I{{EntityName}}Repository _repository;
    private readonly ILogger<Get{{EntityName}}ListHandler> _logger;
    private readonly I{{EntityName}}Mapper _mapper;
    
    public Get{{EntityName}}ListHandler(
        I{{EntityName}}Repository repository,
        ILogger<Get{{EntityName}}ListHandler> logger,
        I{{EntityName}}Mapper mapper)
    {
        _repository = repository;
        _logger = logger;
        _mapper = mapper;
    }
    
    public async Task<Result<IEnumerable<{{EntityName}}Dto>>> Handle(Get{{EntityName}}ListQuery request, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            _logger.LogDebug("Buscando lista de {{EntityName}}s com filtros: {Filters}", request);
            
            IEnumerable<{{EntityName}}> entities;
            
            if (request.Status.HasValue)
            {
                entities = await _repository.GetByStatusAsync(request.Status.Value, cancellationToken);
            }
            else if (!string.IsNullOrWhiteSpace(request.CreatedBy))
            {
                entities = await _repository.GetCreatedByUserAsync(request.CreatedBy, cancellationToken);
            }
            else if (!string.IsNullOrWhiteSpace(request.SearchTerm))
            {
                entities = await _repository.SearchAsync(request.SearchTerm, null, request.Limit, cancellationToken);
            }
            else
            {
                entities = await _repository.GetAllAsync(cancellationToken);
            }
            
            // Aplicar limite se especificado
            if (request.Limit.HasValue && request.Limit > 0)
            {
                entities = entities.Take(request.Limit.Value);
            }
            
            var dtos = entities.Select(_mapper.ToDto);
            
            stopwatch.Stop();
            _logger.LogDebug(
                "Lista de {{EntityName}}s retornada: {Count} itens em {Duration}ms", 
                dtos.Count(), 
                stopwatch.ElapsedMilliseconds);
            
            return Result<IEnumerable<{{EntityName}}Dto>>.Success(dtos);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, 
                "Erro ao buscar lista de {{EntityName}}s após {Duration}ms", 
                stopwatch.ElapsedMilliseconds);
            
            return Result<IEnumerable<{{EntityName}}Dto>>.Failure($"Erro interno: {ex.Message}");
        }
    }
}

/// <summary>
/// Handler para comandos de exclusão de {{EntityName}}
/// </summary>
public class Delete{{EntityName}}Handler : IRequestHandler<Delete{{EntityName}}Command, Result<bool>>
{
    private readonly I{{EntityName}}Repository _repository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<Delete{{EntityName}}Handler> _logger;
    
    public Delete{{EntityName}}Handler(
        I{{EntityName}}Repository repository,
        IUnitOfWork unitOfWork,
        ILogger<Delete{{EntityName}}Handler> logger)
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }
    
    public async Task<Result<bool>> Handle(Delete{{EntityName}}Command request, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            _logger.LogInformation("Iniciando exclusão de {{EntityName}}: {Id}", request.Id);
            
            var entity = await _repository.GetByIdAsync(request.Id, cancellationToken);
            if (entity == null)
            {
                _logger.LogWarning("{{EntityName}} não encontrado para exclusão: {Id}", request.Id);
                return Result<bool>.Failure("{{EntityName}} não encontrado");
            }
            
            if (request.SoftDelete)
            {
                // Exclusão lógica
                entity.Delete(request.DeletedBy);
                await _repository.UpdateAsync(entity, cancellationToken);
            }
            else
            {
                // Exclusão física
                await _repository.DeleteAsync(entity, cancellationToken);
            }
            
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            stopwatch.Stop();
            _logger.LogInformation(
                "{{EntityName}} excluído com sucesso: {Id} ({DeleteType}) em {Duration}ms", 
                entity.Id, 
                request.SoftDelete ? "Lógica" : "Física",
                stopwatch.ElapsedMilliseconds);
            
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, 
                "Erro ao excluir {{EntityName}}: {Id} após {Duration}ms", 
                request.Id, 
                stopwatch.ElapsedMilliseconds);
            
            return Result<bool>.Failure($"Erro interno: {ex.Message}");
        }
    }
}