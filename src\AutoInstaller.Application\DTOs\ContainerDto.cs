using AutoInstaller.Core.Entities;

namespace AutoInstaller.Application.DTOs;

/// <summary>
/// DTO para Container
/// </summary>
public class ContainerDto
{
    /// <summary>
    /// ID do container
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Nome do container
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Nome da imagem
    /// </summary>
    public string ImageName { get; set; } = string.Empty;

    /// <summary>
    /// Tag da imagem
    /// </summary>
    public string ImageTag { get; set; } = string.Empty;

    /// <summary>
    /// Status do container
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// Runtime utilizado
    /// </summary>
    public string Runtime { get; set; } = string.Empty;

    /// <summary>
    /// ID do container no runtime
    /// </summary>
    public string? RuntimeId { get; set; }

    /// <summary>
    /// Comando executado
    /// </summary>
    public string? Command { get; set; }

    /// <summary>
    /// Argumentos do comando
    /// </summary>
    public string[]? Arguments { get; set; }

    /// <summary>
    /// Diretório de trabalho
    /// </summary>
    public string? WorkingDirectory { get; set; }

    /// <summary>
    /// Usuário de execução
    /// </summary>
    public string? User { get; set; }

    /// <summary>
    /// Auto restart habilitado
    /// </summary>
    public bool AutoRestart { get; set; }

    /// <summary>
    /// Política de reinicialização
    /// </summary>
    public string RestartPolicy { get; set; } = string.Empty;

    /// <summary>
    /// Limite de memória em bytes
    /// </summary>
    public long? MemoryLimit { get; set; }

    /// <summary>
    /// Limite de CPU
    /// </summary>
    public double? CpuLimit { get; set; }

    /// <summary>
    /// Data de início
    /// </summary>
    public DateTime? StartedAt { get; set; }

    /// <summary>
    /// Data de parada
    /// </summary>
    public DateTime? StoppedAt { get; set; }

    /// <summary>
    /// Portas expostas
    /// </summary>
    public List<PortDto> ExposedPorts { get; set; } = new();

    /// <summary>
    /// Variáveis de ambiente
    /// </summary>
    public Dictionary<string, string> EnvironmentVariables { get; set; } = new();

    /// <summary>
    /// Labels
    /// </summary>
    public Dictionary<string, string> Labels { get; set; } = new();

    /// <summary>
    /// Volumes
    /// </summary>
    public List<string> Volumes { get; set; } = new();

    /// <summary>
    /// Data de criação
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Data de atualização
    /// </summary>
    public DateTime? UpdatedAt { get; set; }

    /// <summary>
    /// Usuário que criou
    /// </summary>
    public string CreatedBy { get; set; } = string.Empty;

    /// <summary>
    /// Usuário que atualizou
    /// </summary>
    public string? UpdatedBy { get; set; }

    /// <summary>
    /// Indica se foi excluído
    /// </summary>
    public bool IsDeleted { get; set; }
}

/// <summary>
/// DTO para Porta
/// </summary>
public class PortDto
{
    /// <summary>
    /// Número da porta
    /// </summary>
    public int Number { get; set; }

    /// <summary>
    /// Protocolo (TCP/UDP)
    /// </summary>
    public string Protocol { get; set; } = string.Empty;

    /// <summary>
    /// Indica se é porta privilegiada
    /// </summary>
    public bool IsPrivileged { get; set; }

    /// <summary>
    /// Indica se é porta efêmera
    /// </summary>
    public bool IsEphemeral { get; set; }

    /// <summary>
    /// Representação string da porta
    /// </summary>
    public string DisplayName { get; set; } = string.Empty;
}

/// <summary>
/// DTO resumido para Container (para listas)
/// </summary>
public class ContainerSummaryDto
{
    /// <summary>
    /// ID do container
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Nome do container
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Nome da imagem
    /// </summary>
    public string ImageName { get; set; } = string.Empty;

    /// <summary>
    /// Tag da imagem
    /// </summary>
    public string ImageTag { get; set; } = string.Empty;

    /// <summary>
    /// Status do container
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// Runtime utilizado
    /// </summary>
    public string Runtime { get; set; } = string.Empty;

    /// <summary>
    /// Número de portas expostas
    /// </summary>
    public int ExposedPortsCount { get; set; }

    /// <summary>
    /// Data de criação
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Data de início (se em execução)
    /// </summary>
    public DateTime? StartedAt { get; set; }

    /// <summary>
    /// Usuário que criou
    /// </summary>
    public string CreatedBy { get; set; } = string.Empty;
}

/// <summary>
/// DTO para estatísticas de containers
/// </summary>
public class ContainerStatisticsDto
{
    /// <summary>
    /// Total de containers
    /// </summary>
    public int TotalContainers { get; set; }

    /// <summary>
    /// Containers em execução
    /// </summary>
    public int RunningContainers { get; set; }

    /// <summary>
    /// Containers parados
    /// </summary>
    public int StoppedContainers { get; set; }

    /// <summary>
    /// Containers pausados
    /// </summary>
    public int PausedContainers { get; set; }

    /// <summary>
    /// Containers com erro
    /// </summary>
    public int ErrorContainers { get; set; }

    /// <summary>
    /// Containers Docker
    /// </summary>
    public int DockerContainers { get; set; }

    /// <summary>
    /// Containers Podman
    /// </summary>
    public int PodmanContainers { get; set; }

    /// <summary>
    /// Uso total de memória
    /// </summary>
    public long TotalMemoryUsage { get; set; }

    /// <summary>
    /// Uso total de CPU
    /// </summary>
    public double TotalCpuUsage { get; set; }

    /// <summary>
    /// Data da última atualização
    /// </summary>
    public DateTime LastUpdated { get; set; }

    /// <summary>
    /// Distribuição por status
    /// </summary>
    public Dictionary<string, int> StatusDistribution { get; set; } = new();

    /// <summary>
    /// Distribuição por runtime
    /// </summary>
    public Dictionary<string, int> RuntimeDistribution { get; set; } = new();
}

/// <summary>
/// DTO para resultado paginado de containers
/// </summary>
public class PagedContainerResultDto
{
    /// <summary>
    /// Containers da página atual
    /// </summary>
    public List<ContainerSummaryDto> Items { get; set; } = new();

    /// <summary>
    /// Número da página atual
    /// </summary>
    public int PageNumber { get; set; }

    /// <summary>
    /// Tamanho da página
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// Total de itens
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// Total de páginas
    /// </summary>
    public int TotalPages { get; set; }

    /// <summary>
    /// Indica se há página anterior
    /// </summary>
    public bool HasPreviousPage { get; set; }

    /// <summary>
    /// Indica se há próxima página
    /// </summary>
    public bool HasNextPage { get; set; }
}

/// <summary>
/// DTO para criação de container
/// </summary>
public class CreateContainerDto
{
    /// <summary>
    /// Nome do container
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Nome da imagem
    /// </summary>
    public string ImageName { get; set; } = string.Empty;

    /// <summary>
    /// Tag da imagem
    /// </summary>
    public string ImageTag { get; set; } = "latest";

    /// <summary>
    /// Runtime a ser utilizado
    /// </summary>
    public string Runtime { get; set; } = "Docker";

    /// <summary>
    /// Comando a ser executado
    /// </summary>
    public string? Command { get; set; }

    /// <summary>
    /// Argumentos do comando
    /// </summary>
    public string[]? Arguments { get; set; }

    /// <summary>
    /// Diretório de trabalho
    /// </summary>
    public string? WorkingDirectory { get; set; }

    /// <summary>
    /// Usuário de execução
    /// </summary>
    public string? User { get; set; }

    /// <summary>
    /// Auto restart
    /// </summary>
    public bool AutoRestart { get; set; }

    /// <summary>
    /// Política de reinicialização
    /// </summary>
    public string RestartPolicy { get; set; } = "no";

    /// <summary>
    /// Limite de memória
    /// </summary>
    public long? MemoryLimit { get; set; }

    /// <summary>
    /// Limite de CPU
    /// </summary>
    public double? CpuLimit { get; set; }

    /// <summary>
    /// Portas a serem expostas
    /// </summary>
    public List<CreatePortDto> ExposedPorts { get; set; } = new();

    /// <summary>
    /// Variáveis de ambiente
    /// </summary>
    public Dictionary<string, string> EnvironmentVariables { get; set; } = new();

    /// <summary>
    /// Labels
    /// </summary>
    public Dictionary<string, string> Labels { get; set; } = new();

    /// <summary>
    /// Volumes
    /// </summary>
    public List<string> Volumes { get; set; } = new();
}

/// <summary>
/// DTO para criação de porta
/// </summary>
public class CreatePortDto
{
    /// <summary>
    /// Número da porta
    /// </summary>
    public int Number { get; set; }

    /// <summary>
    /// Protocolo
    /// </summary>
    public string Protocol { get; set; } = "TCP";
}
