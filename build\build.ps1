#!/usr/bin/env pwsh

<#
.SYNOPSIS
    Script de build para Auto-Instalador Desktop
.DESCRIPTION
    Script PowerShell para build, teste e empacotamento do Auto-Instalador Desktop
.PARAMETER Target
    Target de build: Build, Test, Pack, Publish, Clean, All
.PARAMETER Configuration
    Configuração de build: Debug ou Release
.PARAMETER Runtime
    Runtime target: win-x64, linux-x64, osx-x64, ou All
.PARAMETER SkipTests
    Pula a execução dos testes
.PARAMETER Verbose
    Habilita saída verbosa
.EXAMPLE
    .\build.ps1 -Target All -Configuration Release
.EXAMPLE
    .\build.ps1 -Target Publish -Runtime win-x64 -Configuration Release
#>

[CmdletBinding()]
param(
    [Parameter()]
    [ValidateSet('Build', 'Test', 'Pack', 'Publish', 'Clean', 'All')]
    [string]$Target = 'Build',
    
    [Parameter()]
    [ValidateSet('Debug', 'Release')]
    [string]$Configuration = 'Debug',
    
    [Parameter()]
    [ValidateSet('win-x64', 'linux-x64', 'osx-x64', 'All')]
    [string]$Runtime = 'win-x64',
    
    [Parameter()]
    [switch]$SkipTests,
    
    [Parameter()]
    [switch]$Verbose
)

# Configurações
$ErrorActionPreference = 'Stop'
$ProgressPreference = 'SilentlyContinue'

$RootDir = Split-Path $PSScriptRoot -Parent
$SrcDir = Join-Path $RootDir 'src'
$TestsDir = Join-Path $RootDir 'tests'
$ArtifactsDir = Join-Path $RootDir 'artifacts'
$PublishDir = Join-Path $ArtifactsDir 'publish'
$PackagesDir = Join-Path $ArtifactsDir 'packages'
$CoverageDir = Join-Path $ArtifactsDir 'coverage'

$MainProject = Join-Path $SrcDir 'AutoInstaller.UI' 'AutoInstaller.UI.csproj'
$SolutionFile = Join-Path $RootDir 'AutoInstaller.sln'

# Cores para output
$ColorReset = "`e[0m"
$ColorGreen = "`e[32m"
$ColorYellow = "`e[33m"
$ColorRed = "`e[31m"
$ColorBlue = "`e[34m"

function Write-Info {
    param([string]$Message)
    Write-Host "${ColorBlue}[INFO]${ColorReset} $Message"
}

function Write-Success {
    param([string]$Message)
    Write-Host "${ColorGreen}[SUCCESS]${ColorReset} $Message"
}

function Write-Warning {
    param([string]$Message)
    Write-Host "${ColorYellow}[WARNING]${ColorReset} $Message"
}

function Write-Error {
    param([string]$Message)
    Write-Host "${ColorRed}[ERROR]${ColorReset} $Message"
}

function Test-DotNetInstalled {
    try {
        $version = dotnet --version
        Write-Info ".NET SDK versão: $version"
        return $true
    }
    catch {
        Write-Error ".NET SDK não encontrado. Instale o .NET 9 SDK."
        return $false
    }
}

function Invoke-Clean {
    Write-Info "Limpando artefatos..."
    
    if (Test-Path $ArtifactsDir) {
        Remove-Item $ArtifactsDir -Recurse -Force
    }
    
    # Limpar bin e obj
    Get-ChildItem -Path $RootDir -Include 'bin', 'obj' -Recurse -Directory | Remove-Item -Recurse -Force
    
    Write-Success "Limpeza concluída"
}

function Invoke-Restore {
    Write-Info "Restaurando dependências..."
    
    $restoreArgs = @(
        'restore'
        $SolutionFile
        '--verbosity', ($Verbose ? 'normal' : 'minimal')
    )
    
    & dotnet @restoreArgs
    if ($LASTEXITCODE -ne 0) {
        throw "Falha na restauração de dependências"
    }
    
    Write-Success "Dependências restauradas"
}

function Invoke-Build {
    Write-Info "Compilando solução..."
    
    $buildArgs = @(
        'build'
        $SolutionFile
        '--configuration', $Configuration
        '--no-restore'
        '--verbosity', ($Verbose ? 'normal' : 'minimal')
    )
    
    & dotnet @buildArgs
    if ($LASTEXITCODE -ne 0) {
        throw "Falha na compilação"
    }
    
    Write-Success "Compilação concluída"
}

function Invoke-Test {
    if ($SkipTests) {
        Write-Warning "Testes pulados conforme solicitado"
        return
    }
    
    Write-Info "Executando testes..."
    
    # Criar diretório de cobertura
    New-Item -ItemType Directory -Path $CoverageDir -Force | Out-Null
    
    $testArgs = @(
        'test'
        $SolutionFile
        '--configuration', $Configuration
        '--no-build'
        '--collect:XPlat Code Coverage'
        '--results-directory', $CoverageDir
        '--logger', 'trx'
        '--verbosity', ($Verbose ? 'normal' : 'minimal')
    )
    
    & dotnet @testArgs
    if ($LASTEXITCODE -ne 0) {
        throw "Falha nos testes"
    }
    
    # Gerar relatório de cobertura se disponível
    $coverageFiles = Get-ChildItem -Path $CoverageDir -Filter "coverage.cobertura.xml" -Recurse
    if ($coverageFiles.Count -gt 0) {
        Write-Info "Gerando relatório de cobertura..."
        
        $reportArgs = @(
            'tool', 'run', 'reportgenerator'
            '--reports', ($coverageFiles.FullName -join ';')
            '--targetdir', (Join-Path $CoverageDir 'report')
            '--reporttypes', 'Html;Cobertura'
        )
        
        try {
            & dotnet @reportArgs 2>$null
            Write-Success "Relatório de cobertura gerado em: $(Join-Path $CoverageDir 'report')"
        }
        catch {
            Write-Warning "ReportGenerator não encontrado. Instale com: dotnet tool install -g dotnet-reportgenerator-globaltool"
        }
    }
    
    Write-Success "Testes concluídos"
}

function Invoke-Pack {
    Write-Info "Empacotando projetos..."
    
    # Criar diretório de pacotes
    New-Item -ItemType Directory -Path $PackagesDir -Force | Out-Null
    
    $packArgs = @(
        'pack'
        $SolutionFile
        '--configuration', $Configuration
        '--no-build'
        '--output', $PackagesDir
        '--verbosity', ($Verbose ? 'normal' : 'minimal')
    )
    
    & dotnet @packArgs
    if ($LASTEXITCODE -ne 0) {
        throw "Falha no empacotamento"
    }
    
    Write-Success "Empacotamento concluído"
}

function Invoke-Publish {
    param([string]$TargetRuntime)
    
    Write-Info "Publicando aplicação para $TargetRuntime..."
    
    $outputDir = Join-Path $PublishDir $TargetRuntime
    New-Item -ItemType Directory -Path $outputDir -Force | Out-Null
    
    $publishArgs = @(
        'publish'
        $MainProject
        '--configuration', $Configuration
        '--runtime', $TargetRuntime
        '--self-contained', 'true'
        '--output', $outputDir
        '-p:PublishSingleFile=true'
        '-p:PublishTrimmed=true'
        '-p:TrimMode=link'
        '-p:EnableCompressionInSingleFile=true'
        '-p:IncludeNativeLibrariesForSelfExtract=true'
        '--verbosity', ($Verbose ? 'normal' : 'minimal')
    )
    
    & dotnet @publishArgs
    if ($LASTEXITCODE -ne 0) {
        throw "Falha na publicação para $TargetRuntime"
    }
    
    # Criar pacote de distribuição
    $version = Get-Date -Format "yyyy.MM.dd"
    $packageName = "AutoInstaller-$TargetRuntime-$version"
    $packageDir = Join-Path $PublishDir $packageName
    
    # Copiar arquivos
    New-Item -ItemType Directory -Path $packageDir -Force | Out-Null
    Copy-Item -Path "$outputDir\*" -Destination $packageDir -Recurse
    
    # Copiar arquivos adicionais
    $readmePath = Join-Path $RootDir 'README.md'
    if (Test-Path $readmePath) {
        Copy-Item $readmePath $packageDir
    }
    
    $licensePath = Join-Path $RootDir 'LICENSE'
    if (Test-Path $licensePath) {
        Copy-Item $licensePath $packageDir
    }
    
    # Criar arquivo ZIP
    $zipPath = Join-Path $PublishDir "$packageName.zip"
    Compress-Archive -Path "$packageDir\*" -DestinationPath $zipPath -Force
    
    Write-Success "Publicação concluída: $zipPath"
}

function Invoke-PublishAll {
    $runtimes = @('win-x64', 'linux-x64', 'osx-x64')
    
    foreach ($runtime in $runtimes) {
        try {
            Invoke-Publish -TargetRuntime $runtime
        }
        catch {
            Write-Error "Falha na publicação para $runtime`: $_"
        }
    }
}

function Show-Summary {
    Write-Info "=== RESUMO DO BUILD ==="
    Write-Info "Target: $Target"
    Write-Info "Configuration: $Configuration"
    Write-Info "Runtime: $Runtime"
    Write-Info "Skip Tests: $SkipTests"
    
    if (Test-Path $ArtifactsDir) {
        $artifacts = Get-ChildItem $ArtifactsDir -Recurse -File
        Write-Info "Artefatos gerados: $($artifacts.Count) arquivos"
        
        if ($artifacts.Count -gt 0) {
            Write-Info "Localização dos artefatos: $ArtifactsDir"
        }
    }
    
    Write-Success "Build concluído com sucesso!"
}

# Função principal
function Main {
    try {
        Write-Info "=== AUTO-INSTALADOR DESKTOP - BUILD SCRIPT ==="
        Write-Info "Iniciando build com target: $Target"
        
        # Verificar pré-requisitos
        if (-not (Test-DotNetInstalled)) {
            exit 1
        }
        
        # Executar targets
        switch ($Target) {
            'Clean' {
                Invoke-Clean
            }
            'Build' {
                Invoke-Restore
                Invoke-Build
            }
            'Test' {
                Invoke-Restore
                Invoke-Build
                Invoke-Test
            }
            'Pack' {
                Invoke-Restore
                Invoke-Build
                if (-not $SkipTests) { Invoke-Test }
                Invoke-Pack
            }
            'Publish' {
                Invoke-Restore
                Invoke-Build
                if (-not $SkipTests) { Invoke-Test }
                
                if ($Runtime -eq 'All') {
                    Invoke-PublishAll
                } else {
                    Invoke-Publish -TargetRuntime $Runtime
                }
            }
            'All' {
                Invoke-Clean
                Invoke-Restore
                Invoke-Build
                if (-not $SkipTests) { Invoke-Test }
                Invoke-Pack
                
                if ($Runtime -eq 'All') {
                    Invoke-PublishAll
                } else {
                    Invoke-Publish -TargetRuntime $Runtime
                }
            }
        }
        
        Show-Summary
    }
    catch {
        Write-Error "Build falhou: $_"
        exit 1
    }
}

# Executar script principal
Main
