<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:vm="using:AutoInstaller.UI.ViewModels.{{ModuleName}}"
             xmlns:controls="using:AutoInstaller.UI.Controls"
             xmlns:converters="using:AutoInstaller.UI.Converters"
             mc:Ignorable="d" d:DesignWidth="1200" d:DesignHeight="800"
             x:Class="AutoInstaller.UI.Views.{{ModuleName}}.{{EntityName}}View"
             x:DataType="vm:{{EntityName}}ViewModel">

  <UserControl.Resources>
    <converters:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter" />
    <converters:StatusToColorConverter x:Key="StatusToColorConverter" />
    <converters:InverseBoolConverter x:Key="InverseBoolConverter" />
  </UserControl.Resources>

  <Design.DataContext>
    <vm:{{EntityName}}ViewModel />
  </Design.DataContext>

  <Grid RowDefinitions="Auto,*,Auto">
    
    <!-- Header -->
    <Border Grid.Row="0" 
            Classes="header-panel"
            Padding="24,16">
      <Grid ColumnDefinitions="*,Auto">
        
        <!-- Título e Breadcrumb -->
        <StackPanel Grid.Column="0" Spacing="8">
          <TextBlock Text="{{EntityName}}s" 
                     Classes="page-title" />
          <controls:Breadcrumb>
            <controls:BreadcrumbItem Text="Dashboard" NavigationPath="/dashboard" />
            <controls:BreadcrumbItem Text="{{ModuleName}}" NavigationPath="/{{moduleName}}" />
            <controls:BreadcrumbItem Text="{{EntityName}}s" IsActive="True" />
          </controls:Breadcrumb>
        </StackPanel>
        
        <!-- Ações do Header -->
        <StackPanel Grid.Column="1" 
                    Orientation="Horizontal" 
                    Spacing="12">
          
          <!-- Botão Refresh -->
          <Button Classes="icon-button secondary"
                  Command="{Binding RefreshCommand}"
                  ToolTip.Tip="Atualizar">
            <controls:Icon IconType="Refresh" 
                          Size="16"
                          IsSpinning="{Binding IsRefreshing}" />
          </Button>
          
          <!-- Botão Novo -->
          <Button Classes="primary"
                  Command="{Binding StartCreateCommand}"
                  IsVisible="{Binding !IsFormVisible}">
            <StackPanel Orientation="Horizontal" Spacing="8">
              <controls:Icon IconType="Plus" Size="16" />
              <TextBlock Text="Novo {{EntityName}}" />
            </StackPanel>
          </Button>
          
        </StackPanel>
      </Grid>
    </Border>

    <!-- Conteúdo Principal -->
    <Grid Grid.Row="1" ColumnDefinitions="*,Auto">
      
      <!-- Lista Principal -->
      <Border Grid.Column="0" 
              Classes="content-panel"
              Margin="24,0,12,0">
        
        <Grid RowDefinitions="Auto,Auto,*,Auto">
          
          <!-- Barra de Pesquisa e Filtros -->
          <Border Grid.Row="0" 
                  Classes="search-panel"
                  Padding="16"
                  Margin="0,0,0,16">
            
            <Grid ColumnDefinitions="*,Auto,Auto">
              
              <!-- Campo de Pesquisa -->
              <controls:SearchBox Grid.Column="0"
                                 Placeholder="Pesquisar {{entityName}}s..."
                                 Text="{Binding SearchText}"
                                 SearchCommand="{Binding SearchCommand}"
                                 ClearCommand="{Binding ClearSearchCommand}" />
              
              <!-- Filtro de Status -->
              <ComboBox Grid.Column="1"
                       Margin="12,0,0,0"
                       MinWidth="150"
                       PlaceholderText="Status"
                       SelectedItem="{Binding StatusFilter}">
                <ComboBox.ItemTemplate>
                  <DataTemplate>
                    <StackPanel Orientation="Horizontal" Spacing="8">
                      <Ellipse Width="8" Height="8"
                              Fill="{Binding Converter={StaticResource StatusToColorConverter}}" />
                      <TextBlock Text="{Binding}" />
                    </StackPanel>
                  </DataTemplate>
                </ComboBox.ItemTemplate>
              </ComboBox>
              
              <!-- Botão Limpar Filtros -->
              <Button Grid.Column="2"
                     Classes="icon-button secondary"
                     Margin="12,0,0,0"
                     Command="{Binding ClearSearchCommand}"
                     ToolTip.Tip="Limpar filtros">
                <controls:Icon IconType="FilterX" Size="16" />
              </Button>
              
            </Grid>
          </Border>
          
          <!-- Indicadores de Status -->
          <Border Grid.Row="1" 
                  Classes="status-panel"
                  Padding="16,12"
                  Margin="0,0,0,16"
                  IsVisible="{Binding IsLoading, Converter={StaticResource BoolToVisibilityConverter}}">
            
            <StackPanel Orientation="Horizontal" Spacing="12">
              <controls:LoadingSpinner Size="16" />
              <TextBlock Text="Carregando {{entityName}}s..." 
                        Classes="status-text" />
            </StackPanel>
          </Border>
          
          <!-- Lista de {{EntityName}}s -->
          <Border Grid.Row="2" 
                  Classes="list-container">
            
            <ScrollViewer>
              <ItemsControl ItemsSource="{Binding {{EntityName}}s}">
                <ItemsControl.ItemTemplate>
                  <DataTemplate>
                    
                    <Border Classes="list-item"
                           Margin="0,0,0,8">
                      
                      <Grid ColumnDefinitions="Auto,*,Auto,Auto,Auto" 
                            Padding="16">
                        
                        <!-- Status Indicator -->
                        <Ellipse Grid.Column="0"
                                Width="12" Height="12"
                                Fill="{Binding Status, Converter={StaticResource StatusToColorConverter}}"
                                Margin="0,0,16,0" />
                        
                        <!-- Informações Principais -->
                        <StackPanel Grid.Column="1" Spacing="4">
                          <TextBlock Text="{Binding Name}" 
                                    Classes="item-title" />
                          <TextBlock Text="{Binding Description}" 
                                    Classes="item-description"
                                    TextWrapping="Wrap" />
                          <StackPanel Orientation="Horizontal" Spacing="16">
                            <TextBlock Classes="item-metadata">
                              <Run Text="Criado em:" />
                              <Run Text="{Binding CreatedAt, StringFormat='{}{0:dd/MM/yyyy HH:mm}'}" />
                            </TextBlock>
                            <TextBlock Text="{Binding CreatedBy}" 
                                      Classes="item-metadata" />
                          </StackPanel>
                        </StackPanel>
                        
                        <!-- Status Badge -->
                        <Border Grid.Column="2"
                               Classes="status-badge"
                               Margin="16,0">
                          <TextBlock Text="{Binding Status}" 
                                    Classes="status-text" />
                        </Border>
                        
                        <!-- Ações Rápidas -->
                        <StackPanel Grid.Column="3" 
                                   Orientation="Horizontal" 
                                   Spacing="8"
                                   Margin="16,0,0,0">
                          
                          <!-- Botão Editar -->
                          <Button Classes="icon-button secondary small"
                                 Command="{Binding $parent[UserControl].((vm:{{EntityName}}ViewModel)DataContext).StartEditCommand}"
                                 CommandParameter="{Binding}"
                                 ToolTip.Tip="Editar">
                            <controls:Icon IconType="Edit" Size="14" />
                          </Button>
                          
                          <!-- Botão Ativar/Desativar -->
                          <Button Classes="icon-button secondary small"
                                 Command="{Binding $parent[UserControl].((vm:{{EntityName}}ViewModel)DataContext).ActivateCommand}"
                                 CommandParameter="{Binding}"
                                 IsVisible="{Binding Status, Converter={StaticResource StatusInactiveConverter}}"
                                 ToolTip.Tip="Ativar">
                            <controls:Icon IconType="Play" Size="14" />
                          </Button>
                          
                          <Button Classes="icon-button warning small"
                                 Command="{Binding $parent[UserControl].((vm:{{EntityName}}ViewModel)DataContext).DeactivateCommand}"
                                 CommandParameter="{Binding}"
                                 IsVisible="{Binding Status, Converter={StaticResource StatusActiveConverter}}"
                                 ToolTip.Tip="Desativar">
                            <controls:Icon IconType="Pause" Size="14" />
                          </Button>
                          
                          <!-- Botão Excluir -->
                          <Button Classes="icon-button danger small"
                                 Command="{Binding $parent[UserControl].((vm:{{EntityName}}ViewModel)DataContext).DeleteCommand}"
                                 CommandParameter="{Binding}"
                                 ToolTip.Tip="Excluir">
                            <controls:Icon IconType="Trash" Size="14" />
                          </Button>
                          
                        </StackPanel>
                        
                        <!-- Seleção -->
                        <CheckBox Grid.Column="4"
                                 Margin="16,0,0,0"
                                 IsChecked="{Binding IsSelected}" />
                        
                      </Grid>
                    </Border>
                    
                  </DataTemplate>
                </ItemsControl.ItemTemplate>
              </ItemsControl>
            </ScrollViewer>
            
          </Border>
          
          <!-- Footer com Paginação -->
          <Border Grid.Row="3" 
                  Classes="footer-panel"
                  Padding="16"
                  Margin="0,16,0,0">
            
            <Grid ColumnDefinitions="*,Auto">
              
              <!-- Informações de Total -->
              <TextBlock Grid.Column="0"
                        Classes="footer-info">
                <Run Text="Total:" />
                <Run Text="{Binding TotalCount}" />
                <Run Text="{{entityName}}s" />
              </TextBlock>
              
              <!-- Controles de Paginação -->
              <StackPanel Grid.Column="1" 
                         Orientation="Horizontal" 
                         Spacing="8">
                <!-- TODO: Implementar paginação -->
              </StackPanel>
              
            </Grid>
          </Border>
          
        </Grid>
      </Border>
      
      <!-- Painel de Formulário -->
      <Border Grid.Column="1" 
              Classes="form-panel"
              Width="400"
              Margin="12,0,24,0"
              IsVisible="{Binding IsFormVisible}">
        
        <Grid RowDefinitions="Auto,*,Auto">
          
          <!-- Header do Formulário -->
          <Border Grid.Row="0" 
                  Classes="form-header"
                  Padding="20,16">
            
            <Grid ColumnDefinitions="*,Auto">
              
              <TextBlock Grid.Column="0" 
                        Classes="form-title">
                <Run Text="{Binding IsCreating, Converter={StaticResource BoolToTextConverter}, ConverterParameter='Novo {{EntityName}}|Editar {{EntityName}}'}" />
              </TextBlock>
              
              <Button Grid.Column="1"
                     Classes="icon-button secondary small"
                     Command="{Binding CancelEditCommand}"
                     ToolTip.Tip="Fechar">
                <controls:Icon IconType="X" Size="16" />
              </Button>
              
            </Grid>
          </Border>
          
          <!-- Conteúdo do Formulário -->
          <ScrollViewer Grid.Row="1" 
                       Padding="20">
            
            <StackPanel Spacing="20">
              
              <!-- Campo Nome -->
              <controls:FormField Label="Nome" 
                                 IsRequired="True">
                <TextBox Text="{Binding Name}"
                        Watermark="Digite o nome do {{entityName}}..."
                        Classes="form-input" />
              </controls:FormField>
              
              <!-- Campo Descrição -->
              <controls:FormField Label="Descrição">
                <TextBox Text="{Binding Description}"
                        Watermark="Digite uma descrição..."
                        AcceptsReturn="True"
                        TextWrapping="Wrap"
                        MinHeight="80"
                        Classes="form-input multiline" />
              </controls:FormField>
              
              <!-- Informações Adicionais (apenas na edição) -->
              <StackPanel Spacing="12"
                         IsVisible="{Binding IsEditing}">
                
                <Separator Classes="form-separator" />
                
                <controls:InfoPanel Title="Informações">
                  <StackPanel Spacing="8">
                    
                    <Grid ColumnDefinitions="Auto,*">
                      <TextBlock Grid.Column="0" 
                                Text="ID:" 
                                Classes="info-label" />
                      <TextBlock Grid.Column="1" 
                                Text="{Binding Selected{{EntityName}}.Id}" 
                                Classes="info-value" />
                    </Grid>
                    
                    <Grid ColumnDefinitions="Auto,*">
                      <TextBlock Grid.Column="0" 
                                Text="Status:" 
                                Classes="info-label" />
                      <StackPanel Grid.Column="1" 
                                 Orientation="Horizontal" 
                                 Spacing="8">
                        <Ellipse Width="8" Height="8"
                                Fill="{Binding Selected{{EntityName}}.Status, Converter={StaticResource StatusToColorConverter}}" />
                        <TextBlock Text="{Binding Selected{{EntityName}}.Status}" 
                                  Classes="info-value" />
                      </StackPanel>
                    </Grid>
                    
                    <Grid ColumnDefinitions="Auto,*">
                      <TextBlock Grid.Column="0" 
                                Text="Criado em:" 
                                Classes="info-label" />
                      <TextBlock Grid.Column="1" 
                                Text="{Binding Selected{{EntityName}}.CreatedAt, StringFormat='{}{0:dd/MM/yyyy HH:mm}'}" 
                                Classes="info-value" />
                    </Grid>
                    
                    <Grid ColumnDefinitions="Auto,*">
                      <TextBlock Grid.Column="0" 
                                Text="Criado por:" 
                                Classes="info-label" />
                      <TextBlock Grid.Column="1" 
                                Text="{Binding Selected{{EntityName}}.CreatedBy}" 
                                Classes="info-value" />
                    </Grid>
                    
                    <Grid ColumnDefinitions="Auto,*"
                         IsVisible="{Binding Selected{{EntityName}}.UpdatedAt, Converter={StaticResource NotNullConverter}}">
                      <TextBlock Grid.Column="0" 
                                Text="Atualizado em:" 
                                Classes="info-label" />
                      <TextBlock Grid.Column="1" 
                                Text="{Binding Selected{{EntityName}}.UpdatedAt, StringFormat='{}{0:dd/MM/yyyy HH:mm}'}" 
                                Classes="info-value" />
                    </Grid>
                    
                  </StackPanel>
                </controls:InfoPanel>
                
              </StackPanel>
              
            </StackPanel>
          </ScrollViewer>
          
          <!-- Footer do Formulário -->
          <Border Grid.Row="2" 
                  Classes="form-footer"
                  Padding="20,16">
            
            <Grid ColumnDefinitions="*,Auto,Auto">
              
              <!-- Mensagem de Erro -->
              <controls:ErrorMessage Grid.Column="0"
                                    Message="{Binding ErrorMessage}"
                                    IsVisible="{Binding ErrorMessage, Converter={StaticResource NotNullConverter}}" />
              
              <!-- Botões de Ação -->
              <StackPanel Grid.Column="2" 
                         Orientation="Horizontal" 
                         Spacing="12">
                
                <Button Classes="secondary"
                       Command="{Binding CancelEditCommand}">
                  <TextBlock Text="Cancelar" />
                </Button>
                
                <Button Classes="primary"
                       Command="{Binding CreateCommand}"
                       IsVisible="{Binding IsCreating}">
                  <StackPanel Orientation="Horizontal" Spacing="8">
                    <controls:Icon IconType="Plus" 
                                  Size="16"
                                  IsVisible="{Binding !IsLoading}" />
                    <controls:LoadingSpinner Size="16"
                                           IsVisible="{Binding IsLoading}" />
                    <TextBlock Text="Criar" />
                  </StackPanel>
                </Button>
                
                <Button Classes="primary"
                       Command="{Binding UpdateCommand}"
                       IsVisible="{Binding IsEditing}">
                  <StackPanel Orientation="Horizontal" Spacing="8">
                    <controls:Icon IconType="Save" 
                                  Size="16"
                                  IsVisible="{Binding !IsLoading}" />
                    <controls:LoadingSpinner Size="16"
                                           IsVisible="{Binding IsLoading}" />
                    <TextBlock Text="Salvar" />
                  </StackPanel>
                </Button>
                
              </StackPanel>
            </Grid>
          </Border>
          
        </Grid>
      </Border>
      
    </Grid>

    <!-- Overlay de Loading Global -->
    <Border Grid.Row="0" Grid.RowSpan="3"
           Classes="loading-overlay"
           IsVisible="{Binding IsLoading}">
      
      <StackPanel HorizontalAlignment="Center" 
                 VerticalAlignment="Center" 
                 Spacing="16">
        <controls:LoadingSpinner Size="32" />
        <TextBlock Text="Processando..." 
                  Classes="loading-text" />
      </StackPanel>
      
    </Border>

  </Grid>

</UserControl>