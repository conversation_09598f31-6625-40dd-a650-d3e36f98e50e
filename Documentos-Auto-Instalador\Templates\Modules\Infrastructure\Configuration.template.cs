using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using System.ComponentModel.DataAnnotations;

namespace {{RootNamespace}}.Infrastructure.Modules.{{ModuleName}}.Configuration;

/// <summary>
/// Configurações para o módulo {{ModuleName}}
/// Implementa validação de configurações e binding automático
/// </summary>
public class {{ModuleName}}Configuration : IValidatableObject
{
    public const string SectionName = "{{ModuleName}}";

    /// <summary>
    /// Configurações de banco de dados
    /// </summary>
    public DatabaseConfiguration Database { get; set; } = new();

    /// <summary>
    /// Configurações de cache
    /// </summary>
    public CacheConfiguration Cache { get; set; } = new();

    /// <summary>
    /// Configurações de logging
    /// </summary>
    public LoggingConfiguration Logging { get; set; } = new();

    /// <summary>
    /// Configurações de serviços externos
    /// </summary>
    public ExternalServicesConfiguration ExternalServices { get; set; } = new();

    /// <summary>
    /// Configurações de segurança
    /// </summary>
    public SecurityConfiguration Security { get; set; } = new();

    /// <summary>
    /// Configurações de performance
    /// </summary>
    public PerformanceConfiguration Performance { get; set; } = new();

    /// <summary>
    /// Configurações específicas do módulo
    /// </summary>
    public {{ModuleName}}SpecificConfiguration Specific { get; set; } = new();

    /// <summary>
    /// Valida as configurações do módulo
    /// </summary>
    /// <param name="validationContext">Contexto de validação</param>
    /// <returns>Resultados da validação</returns>
    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        var results = new List<ValidationResult>();

        // Validar configurações de banco de dados
        if (string.IsNullOrWhiteSpace(Database.ConnectionString))
        {
            results.Add(new ValidationResult(
                "Connection string é obrigatória",
                new[] { nameof(Database.ConnectionString) }));
        }

        // Validar configurações de cache
        if (Cache.DefaultExpirationMinutes <= 0)
        {
            results.Add(new ValidationResult(
                "Tempo de expiração do cache deve ser maior que zero",
                new[] { nameof(Cache.DefaultExpirationMinutes) }));
        }

        // Validar configurações de performance
        if (Performance.MaxConcurrentOperations <= 0)
        {
            results.Add(new ValidationResult(
                "Número máximo de operações concorrentes deve ser maior que zero",
                new[] { nameof(Performance.MaxConcurrentOperations) }));
        }

        // Validar configurações específicas do módulo
        var specificValidation = Specific.Validate(validationContext);
        results.AddRange(specificValidation);

        return results;
    }
}

/// <summary>
/// Configurações de banco de dados
/// </summary>
public class DatabaseConfiguration
{
    [Required(ErrorMessage = "Connection string é obrigatória")]
    public string ConnectionString { get; set; } = string.Empty;

    [Range(1, 3600, ErrorMessage = "Timeout deve estar entre 1 e 3600 segundos")]
    public int CommandTimeoutSeconds { get; set; } = 30;

    [Range(1, 100, ErrorMessage = "Pool size deve estar entre 1 e 100")]
    public int MaxPoolSize { get; set; } = 20;

    public bool EnableSensitiveDataLogging { get; set; } = false;
    public bool EnableDetailedErrors { get; set; } = false;
    public bool EnableRetryOnFailure { get; set; } = true;
    public int MaxRetryCount { get; set; } = 3;
    public int MaxRetryDelaySeconds { get; set; } = 30;
}

/// <summary>
/// Configurações de cache
/// </summary>
public class CacheConfiguration
{
    public bool Enabled { get; set; } = true;
    
    [Range(1, 1440, ErrorMessage = "Expiração padrão deve estar entre 1 e 1440 minutos")]
    public int DefaultExpirationMinutes { get; set; } = 60;
    
    [Range(1, 10000, ErrorMessage = "Tamanho máximo deve estar entre 1 e 10000 MB")]
    public int MaxSizeMB { get; set; } = 100;
    
    public bool SlidingExpiration { get; set; } = true;
    public string KeyPrefix { get; set; } = "{{ModuleName}}:";
    public bool CompressValues { get; set; } = false;
}

/// <summary>
/// Configurações de logging
/// </summary>
public class LoggingConfiguration
{
    public string MinimumLevel { get; set; } = "Information";
    public bool EnableStructuredLogging { get; set; } = true;
    public bool EnablePerformanceLogging { get; set; } = true;
    public bool EnableSqlLogging { get; set; } = false;
    public string LogTemplate { get; set; } = "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}";
    public List<string> ExcludeProperties { get; set; } = new();
    public Dictionary<string, string> EnrichWith { get; set; } = new();
}

/// <summary>
/// Configurações de serviços externos
/// </summary>
public class ExternalServicesConfiguration
{
    [Range(1, 300, ErrorMessage = "Timeout deve estar entre 1 e 300 segundos")]
    public int DefaultTimeoutSeconds { get; set; } = 30;
    
    [Range(0, 10, ErrorMessage = "Tentativas de retry devem estar entre 0 e 10")]
    public int DefaultRetryAttempts { get; set; } = 3;
    
    [Range(1, 60, ErrorMessage = "Delay de retry deve estar entre 1 e 60 segundos")]
    public int DefaultRetryDelaySeconds { get; set; } = 2;
    
    public bool EnableCircuitBreaker { get; set; } = true;
    public int CircuitBreakerThreshold { get; set; } = 5;
    public int CircuitBreakerTimeoutSeconds { get; set; } = 60;
    
    public Dictionary<string, ServiceEndpointConfiguration> Endpoints { get; set; } = new();
}

/// <summary>
/// Configuração de endpoint de serviço
/// </summary>
public class ServiceEndpointConfiguration
{
    [Required(ErrorMessage = "URL base é obrigatória")]
    [Url(ErrorMessage = "URL base deve ser uma URL válida")]
    public string BaseUrl { get; set; } = string.Empty;
    
    public string ApiKey { get; set; } = string.Empty;
    public int TimeoutSeconds { get; set; } = 30;
    public int RetryAttempts { get; set; } = 3;
    public bool EnableHealthCheck { get; set; } = true;
    public string HealthCheckEndpoint { get; set; } = "/health";
}

/// <summary>
/// Configurações de segurança
/// </summary>
public class SecurityConfiguration
{
    public bool EnableEncryption { get; set; } = true;
    public string EncryptionKey { get; set; } = string.Empty;
    public bool EnableAuditLogging { get; set; } = true;
    public bool EnableDataMasking { get; set; } = true;
    public List<string> SensitiveFields { get; set; } = new() { "password", "token", "key", "secret" };
    public int SessionTimeoutMinutes { get; set; } = 30;
    public bool RequireHttps { get; set; } = true;
}

/// <summary>
/// Configurações de performance
/// </summary>
public class PerformanceConfiguration
{
    [Range(1, 1000, ErrorMessage = "Operações concorrentes devem estar entre 1 e 1000")]
    public int MaxConcurrentOperations { get; set; } = 10;
    
    [Range(1, 10000, ErrorMessage = "Tamanho do batch deve estar entre 1 e 10000")]
    public int BatchSize { get; set; } = 100;
    
    [Range(1, 3600, ErrorMessage = "Timeout de operação deve estar entre 1 e 3600 segundos")]
    public int OperationTimeoutSeconds { get; set; } = 300;
    
    public bool EnablePerformanceCounters { get; set; } = true;
    public bool EnableMemoryOptimization { get; set; } = true;
    public int MaxMemoryUsageMB { get; set; } = 500;
}

/// <summary>
/// Configurações específicas do módulo {{ModuleName}}
/// </summary>
public class {{ModuleName}}SpecificConfiguration : IValidatableObject
{
    // Adicione aqui configurações específicas do módulo {{ModuleName}}
    public string SpecificSetting1 { get; set; } = string.Empty;
    public int SpecificSetting2 { get; set; } = 0;
    public bool SpecificSetting3 { get; set; } = false;
    
    /// <summary>
    /// Configurações customizadas do módulo
    /// </summary>
    public Dictionary<string, object> CustomSettings { get; set; } = new();

    /// <summary>
    /// Valida as configurações específicas do módulo
    /// </summary>
    /// <param name="validationContext">Contexto de validação</param>
    /// <returns>Resultados da validação</returns>
    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        var results = new List<ValidationResult>();

        // Adicione validações específicas do módulo aqui
        if (string.IsNullOrWhiteSpace(SpecificSetting1))
        {
            results.Add(new ValidationResult(
                "SpecificSetting1 é obrigatório",
                new[] { nameof(SpecificSetting1) }));
        }

        if (SpecificSetting2 < 0)
        {
            results.Add(new ValidationResult(
                "SpecificSetting2 deve ser maior ou igual a zero",
                new[] { nameof(SpecificSetting2) }));
        }

        return results;
    }
}

/// <summary>
/// Extensões para configuração do módulo {{ModuleName}}
/// </summary>
public static class {{ModuleName}}ConfigurationExtensions
{
    /// <summary>
    /// Adiciona e configura as opções do módulo {{ModuleName}}
    /// </summary>
    /// <param name="services">Coleção de serviços</param>
    /// <param name="configuration">Configuração da aplicação</param>
    /// <returns>Coleção de serviços para encadeamento</returns>
    public static IServiceCollection Add{{ModuleName}}Configuration(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        // Configurar e validar as opções do módulo
        services.Configure<{{ModuleName}}Configuration>(configuration.GetSection({{ModuleName}}Configuration.SectionName));
        
        // Adicionar validação das opções
        services.AddSingleton<IValidateOptions<{{ModuleName}}Configuration>, {{ModuleName}}ConfigurationValidator>();
        
        // Configurar opções específicas
        services.Configure<DatabaseConfiguration>(
            configuration.GetSection($"{{{ModuleName}}Configuration.SectionName}:Database"));
        
        services.Configure<CacheConfiguration>(
            configuration.GetSection($"{{{ModuleName}}Configuration.SectionName}:Cache"));
        
        services.Configure<ExternalServicesConfiguration>(
            configuration.GetSection($"{{{ModuleName}}Configuration.SectionName}:ExternalServices"));
        
        services.Configure<SecurityConfiguration>(
            configuration.GetSection($"{{{ModuleName}}Configuration.SectionName}:Security"));
        
        services.Configure<PerformanceConfiguration>(
            configuration.GetSection($"{{{ModuleName}}Configuration.SectionName}:Performance"));
        
        services.Configure<{{ModuleName}}SpecificConfiguration>(
            configuration.GetSection($"{{{ModuleName}}Configuration.SectionName}:Specific"));

        return services;
    }

    /// <summary>
    /// Obtém a configuração do módulo {{ModuleName}} validada
    /// </summary>
    /// <param name="configuration">Configuração da aplicação</param>
    /// <returns>Configuração do módulo validada</returns>
    public static {{ModuleName}}Configuration Get{{ModuleName}}Configuration(this IConfiguration configuration)
    {
        var moduleConfig = new {{ModuleName}}Configuration();
        configuration.GetSection({{ModuleName}}Configuration.SectionName).Bind(moduleConfig);
        
        // Validar configuração
        var validationContext = new ValidationContext(moduleConfig);
        var validationResults = moduleConfig.Validate(validationContext);
        
        if (validationResults.Any())
        {
            var errors = string.Join("; ", validationResults.Select(r => r.ErrorMessage));
            throw new InvalidOperationException($"Configuração inválida para módulo {{ModuleName}}: {errors}");
        }
        
        return moduleConfig;
    }
}

/// <summary>
/// Validador para configurações do módulo {{ModuleName}}
/// </summary>
public class {{ModuleName}}ConfigurationValidator : IValidateOptions<{{ModuleName}}Configuration>
{
    /// <summary>
    /// Valida as opções de configuração
    /// </summary>
    /// <param name="name">Nome das opções</param>
    /// <param name="options">Opções a serem validadas</param>
    /// <returns>Resultado da validação</returns>
    public ValidateOptionsResult Validate(string? name, {{ModuleName}}Configuration options)
    {
        var validationContext = new ValidationContext(options);
        var validationResults = options.Validate(validationContext);
        
        if (validationResults.Any())
        {
            var errors = validationResults.Select(r => r.ErrorMessage ?? "Erro de validação").ToList();
            return ValidateOptionsResult.Fail(errors);
        }
        
        return ValidateOptionsResult.Success;
    }
}