using AutoInstaller.Core.Exceptions;
using AutoInstaller.Core.ValueObjects;

namespace AutoInstaller.Tests.Unit.Core.ValueObjects;

/// <summary>
/// Testes para ContainerName Value Object
/// </summary>
public class ContainerNameTests
{
    [Theory]
    [InlineData("nginx")]
    [InlineData("my-app")]
    [InlineData("web_server")]
    [InlineData("app.test")]
    [InlineData("container123")]
    [InlineData("a")]
    [InlineData("test-container-with-long-name-but-still-valid")]
    public void Create_WithValidName_ShouldCreateContainerName(string validName)
    {
        // Act
        var containerName = ContainerName.Create(validName);

        // Assert
        containerName.Should().NotBeNull();
        containerName.Value.Should().Be(validName);
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    [InlineData("   ")]
    public void Create_WithNullOrWhitespaceName_ShouldThrowDomainException(string? invalidName)
    {
        // Act & Assert
        var act = () => ContainerName.Create(invalidName!);
        act.Should().Throw<DomainException>()
           .WithMessage("Nome do container não pode ser vazio");
    }

    [Theory]
    [InlineData("UPPERCASE")]
    [InlineData("Mixed-Case")]
    [InlineData("camelCase")]
    public void Create_WithUppercaseLetters_ShouldThrowDomainException(string invalidName)
    {
        // Act & Assert
        var act = () => ContainerName.Create(invalidName);
        act.Should().Throw<DomainException>()
           .WithMessage("Nome do container deve conter apenas letras minúsculas, números, hífens, underscores e pontos");
    }

    [Theory]
    [InlineData("-start-with-hyphen")]
    [InlineData(".start-with-dot")]
    [InlineData("_start-with-underscore")]
    public void Create_WithInvalidStartCharacter_ShouldThrowDomainException(string invalidName)
    {
        // Act & Assert
        var act = () => ContainerName.Create(invalidName);
        act.Should().Throw<DomainException>()
           .WithMessage("Nome do container deve começar e terminar com letra ou número");
    }

    [Theory]
    [InlineData("end-with-hyphen-")]
    [InlineData("end-with-dot.")]
    [InlineData("end-with-underscore_")]
    public void Create_WithInvalidEndCharacter_ShouldThrowDomainException(string invalidName)
    {
        // Act & Assert
        var act = () => ContainerName.Create(invalidName);
        act.Should().Throw<DomainException>()
           .WithMessage("Nome do container deve começar e terminar com letra ou número");
    }

    [Theory]
    [InlineData("name@with@special")]
    [InlineData("name#with#hash")]
    [InlineData("name$with$dollar")]
    [InlineData("name%with%percent")]
    [InlineData("name with space")]
    public void Create_WithInvalidCharacters_ShouldThrowDomainException(string invalidName)
    {
        // Act & Assert
        var act = () => ContainerName.Create(invalidName);
        act.Should().Throw<DomainException>()
           .WithMessage("Nome do container deve conter apenas letras minúsculas, números, hífens, underscores e pontos");
    }

    [Fact]
    public void Create_WithNameTooLong_ShouldThrowDomainException()
    {
        // Arrange
        var longName = new string('a', 254); // 254 caracteres (limite é 253)

        // Act & Assert
        var act = () => ContainerName.Create(longName);
        act.Should().Throw<DomainException>()
           .WithMessage("Nome do container deve ter no máximo 253 caracteres");
    }

    [Fact]
    public void Create_WithMaxLengthName_ShouldCreateContainerName()
    {
        // Arrange
        var maxLengthName = new string('a', 253); // Exatamente 253 caracteres

        // Act
        var containerName = ContainerName.Create(maxLengthName);

        // Assert
        containerName.Should().NotBeNull();
        containerName.Value.Should().Be(maxLengthName);
        containerName.Value.Length.Should().Be(253);
    }

    [Theory]
    [InlineData("nginx", "nginx", true)]
    [InlineData("nginx", "apache", false)]
    [InlineData("my-app", "my-app", true)]
    public void Equals_WithContainerNames_ShouldReturnCorrectResult(string name1, string name2, bool expectedEqual)
    {
        // Arrange
        var containerName1 = ContainerName.Create(name1);
        var containerName2 = ContainerName.Create(name2);

        // Act & Assert
        containerName1.Equals(containerName2).Should().Be(expectedEqual);
        (containerName1 == containerName2).Should().Be(expectedEqual);
        (containerName1 != containerName2).Should().Be(!expectedEqual);
    }

    [Fact]
    public void Equals_WithNull_ShouldReturnFalse()
    {
        // Arrange
        var containerName = ContainerName.Create("nginx");

        // Act & Assert
        containerName.Equals(null).Should().BeFalse();
        (containerName == null).Should().BeFalse();
        (containerName != null).Should().BeTrue();
    }

    [Fact]
    public void Equals_WithDifferentType_ShouldReturnFalse()
    {
        // Arrange
        var containerName = ContainerName.Create("nginx");
        var stringValue = "nginx";

        // Act & Assert
        containerName.Equals(stringValue).Should().BeFalse();
    }

    [Theory]
    [InlineData("nginx")]
    [InlineData("my-app")]
    [InlineData("web_server")]
    public void GetHashCode_WithSameValue_ShouldReturnSameHashCode(string name)
    {
        // Arrange
        var containerName1 = ContainerName.Create(name);
        var containerName2 = ContainerName.Create(name);

        // Act & Assert
        containerName1.GetHashCode().Should().Be(containerName2.GetHashCode());
    }

    [Fact]
    public void GetHashCode_WithDifferentValues_ShouldReturnDifferentHashCodes()
    {
        // Arrange
        var containerName1 = ContainerName.Create("nginx");
        var containerName2 = ContainerName.Create("apache");

        // Act & Assert
        containerName1.GetHashCode().Should().NotBe(containerName2.GetHashCode());
    }

    [Theory]
    [InlineData("nginx")]
    [InlineData("my-app")]
    [InlineData("web_server")]
    public void ToString_ShouldReturnValue(string name)
    {
        // Arrange
        var containerName = ContainerName.Create(name);

        // Act
        var result = containerName.ToString();

        // Assert
        result.Should().Be(name);
    }

    [Theory]
    [InlineData("nginx")]
    [InlineData("my-app")]
    [InlineData("web_server")]
    public void ImplicitConversion_FromString_ShouldCreateContainerName(string name)
    {
        // Act
        ContainerName containerName = name;

        // Assert
        containerName.Should().NotBeNull();
        containerName.Value.Should().Be(name);
    }

    [Theory]
    [InlineData("nginx")]
    [InlineData("my-app")]
    [InlineData("web_server")]
    public void ImplicitConversion_ToString_ShouldReturnValue(string name)
    {
        // Arrange
        var containerName = ContainerName.Create(name);

        // Act
        string result = containerName;

        // Assert
        result.Should().Be(name);
    }

    [Theory]
    [InlineData("nginx", true)]
    [InlineData("my-app", true)]
    [InlineData("web_server", true)]
    [InlineData("", false)]
    [InlineData(" ", false)]
    [InlineData("UPPERCASE", false)]
    [InlineData("-invalid", false)]
    [InlineData("invalid-", false)]
    [InlineData("name@invalid", false)]
    public void IsValid_WithVariousInputs_ShouldReturnCorrectResult(string name, bool expectedValid)
    {
        // Act
        var result = ContainerName.IsValid(name);

        // Assert
        result.Should().Be(expectedValid);
    }

    [Fact]
    public void IsValid_WithNullInput_ShouldReturnFalse()
    {
        // Act
        var result = ContainerName.IsValid(null);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void IsValid_WithTooLongName_ShouldReturnFalse()
    {
        // Arrange
        var longName = new string('a', 254);

        // Act
        var result = ContainerName.IsValid(longName);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void IsValid_WithMaxLengthName_ShouldReturnTrue()
    {
        // Arrange
        var maxLengthName = new string('a', 253);

        // Act
        var result = ContainerName.IsValid(maxLengthName);

        // Assert
        result.Should().BeTrue();
    }

    [Theory]
    [InlineData("test.container")]
    [InlineData("my_app")]
    [InlineData("web-server")]
    [InlineData("app123")]
    [InlineData("123app")]
    public void Create_WithValidComplexNames_ShouldCreateContainerName(string validName)
    {
        // Act
        var containerName = ContainerName.Create(validName);

        // Assert
        containerName.Should().NotBeNull();
        containerName.Value.Should().Be(validName);
    }

    [Theory]
    [InlineData("test..double.dot")]
    [InlineData("test--double-hyphen")]
    [InlineData("test__double_underscore")]
    public void Create_WithConsecutiveSpecialCharacters_ShouldCreateContainerName(string validName)
    {
        // Act
        var containerName = ContainerName.Create(validName);

        // Assert
        containerName.Should().NotBeNull();
        containerName.Value.Should().Be(validName);
    }
}
