# Especificações Técnicas - Auto-Instalador Desktop

## Visão Geral
Este documento contém as especificações técnicas detalhadas para o desenvolvimento do Auto-Instalador Desktop multiplataforma, incluindo requisitos, configurações e diretrizes de implementação.

---

## 1. Avalonia UI (Versão 11.3.4)

### 1.1 Requisitos da Framework
- **Versão Mínima**: 11.3.4
- **Versão Recomendada**: 11.3.4 (última estável)
- **Compatibilidade**: .NET 6.0+ (otimizado para .NET 9)

### 1.2 Pacotes Essenciais
```xml
<PackageReference Include="Avalonia" Version="11.3.4" />
<PackageReference Include="Avalonia.Desktop" Version="11.3.4" />
<PackageReference Include="Avalonia.Themes.Fluent" Version="11.3.4" />
<PackageReference Include="Avalonia.Fonts.Inter" Version="11.3.4" />
<PackageReference Include="Avalonia.ReactiveUI" Version="11.3.4" />
<PackageReference Include="Avalonia.Xaml.Behaviors" Version="********" />
<PackageReference Include="Avalonia.Controls.DataGrid" Version="11.3.4" />
<PackageReference Include="Avalonia.Svg.Skia" Version="11.3.0" />
```

### 1.3 Configurações de Projeto
```xml
<PropertyGroup>
  <OutputType>WinExe</OutputType>
  <TargetFramework>net9.0</TargetFramework>
  <Nullable>enable</Nullable>
  <BuiltInComInteropSupport>true</BuiltInComInteropSupport>
  <ApplicationManifest>app.manifest</ApplicationManifest>
  <AvaloniaUseCompiledBindingsByDefault>true</AvaloniaUseCompiledBindingsByDefault>
</PropertyGroup>
```

### 1.4 Recursos Visuais
- **Tema Base**: Fluent Dark (inspirado no Docker Desktop)
- **Fontes**: Inter (padrão), Segoe UI (fallback)
- **Ícones**: Material Design Icons via Avalonia.Svg
- **Animações**: Transições suaves com duração de 200-300ms

### 1.5 Estrutura de Estilos
```
Styles/
├── Colors.axaml          # Paleta de cores do tema
├── Typography.axaml      # Definições tipográficas
├── Controls/
│   ├── Button.axaml      # Estilos de botões
│   ├── TextBox.axaml     # Estilos de campos de texto
│   ├── DataGrid.axaml    # Estilos de tabelas
│   └── Modal.axaml       # Estilos de modais
└── Animations.axaml      # Definições de animações
```

---

## 2. Docker.DotNet (Versão 3.125.15)

### 2.1 Especificações da Biblioteca
- **Versão Mínima**: 3.125.15
- **Versão Recomendada**: 3.125.15 (última estável)
- **Compatibilidade**: Docker Engine API 1.51+

### 2.2 Configuração de Dependências
```xml
<PackageReference Include="Docker.DotNet" Version="3.125.15" />
<PackageReference Include="Docker.DotNet.X509" Version="3.125.15" />
```

### 2.3 Configuração do Cliente Docker
```csharp
public class DockerClientConfiguration
{
    public static DockerClient CreateClient()
    {
        var config = new DockerClientConfiguration(
            new Uri("npipe://./pipe/docker_engine")) // Windows
            // new Uri("unix:///var/run/docker.sock") // Linux/macOS
        {
            DefaultTimeout = TimeSpan.FromMinutes(5)
        };
        
        return config.CreateClient();
    }
}
```

### 2.4 Operações Suportadas
- **Gerenciamento de Containers**:
  - Criar, iniciar, parar, remover containers
  - Monitoramento de status e logs
  - Execução de comandos em containers

- **Gerenciamento de Imagens**:
  - Pull/Push de imagens
  - Listagem e remoção de imagens
  - Build de imagens a partir de Dockerfile

- **Gerenciamento de Redes**:
  - Criação e configuração de redes Docker
  - Conexão de containers a redes

- **Gerenciamento de Volumes**:
  - Criação e montagem de volumes
  - Backup e restore de dados

### 2.5 Tratamento de Erros
```csharp
public async Task<bool> IsDockerAvailableAsync()
{
    try
    {
        using var client = DockerClientConfiguration.CreateClient();
        await client.System.PingAsync();
        return true;
    }
    catch (DockerApiException ex)
    {
        // Log específico para erros da API Docker
        return false;
    }
    catch (Exception ex)
    {
        // Log para outros erros (Docker não instalado, etc.)
        return false;
    }
}
```


```

### 3.4 Diferenças de Implementação
- **Rootless por Padrão**: Podman executa sem privilégios de root
- **Pods Nativos**: Suporte a agrupamento de containers em pods
- **Compatibilidade Docker**: API compatível com Docker para facilitar migração

### 3.5 Configurações Específicas
```csharp
public class PodmanSettings
{
    public bool UseRootless { get; set; } = true;
    public string MachineProfile { get; set; } = "default";
    public bool EnablePodSupport { get; set; } = true;
    public string DefaultRegistry { get; set; } = "docker.io";
}
```

---

## 4. .NET 9 (LTS)

### 4.1 Requisitos da Plataforma
- **Versão**: .NET 9.0.7 (Long Term Support)
- **Runtime**: Microsoft.NETCore.App 9.0.7
- **SDK**: 9.0.303 (para desenvolvimento)

### 4.2 Features Utilizadas

#### 4.2.1 Performance
- **AOT (Ahead-of-Time) Compilation**: Para reduzir tempo de inicialização
- **Trimming**: Remoção de código não utilizado
- **ReadyToRun**: Imagens pré-compiladas para melhor performance

#### 4.2.2 Linguagem (C# 13)
- **Primary Constructors**: Para classes de configuração
- **Collection Expressions**: Sintaxe simplificada para coleções
- **Required Members**: Propriedades obrigatórias em DTOs
- **File-scoped Types**: Para helpers internos

#### 4.2.3 APIs e Bibliotecas
- **System.Text.Json**: Serialização JSON de alta performance
- **Microsoft.Extensions.Hosting**: Host genérico para serviços
- **Microsoft.Extensions.DependencyInjection**: Injeção de dependência
- **Microsoft.Extensions.Configuration**: Sistema de configuração
- **Microsoft.Extensions.Logging**: Sistema de logging estruturado

### 4.3 Configuração de Projeto
```xml
<PropertyGroup>
  <TargetFramework>net9.0.7</TargetFramework>
  <LangVersion>13.0</LangVersion>
  <Nullable>enable</Nullable>
  <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  <EnableNETAnalyzers>true</EnableNETAnalyzers>
  <AnalysisLevel>latest</AnalysisLevel>
  <PublishAot>false</PublishAot>
  <PublishTrimmed>true</PublishTrimmed>
  <PublishReadyToRun>true</PublishReadyToRun>
</PropertyGroup>
```

### 4.4 Pacotes de Extensão
```xml
<PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.0" />
<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.0" />
<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.0" />
<PackageReference Include="Microsoft.Extensions.Logging.Console" Version="9.0.0" />
<PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="9.0.0" />
<PackageReference Include="System.Text.Json" Version="9.0.0" />
```

### 4.5 Configuração de Deployment
```xml
<!-- Para Windows -->
<RuntimeIdentifier>win-x64</RuntimeIdentifier>
<SelfContained>true</SelfContained>

<!-- Para Linux -->
<RuntimeIdentifier>linux-x64</RuntimeIdentifier>
<SelfContained>true</SelfContained>

<!-- Para macOS -->
<RuntimeIdentifier>osx-x64</RuntimeIdentifier>
<SelfContained>true</SelfContained>
```

---

## 5. Requisitos de Sistema

### 5.1 Requisitos Mínimos
- **SO**: Windows 10 1903+, Ubuntu 20.04+, macOS 11+
- **RAM**: 4 GB (8 GB recomendado)
- **Armazenamento**: 2 GB livres
- **Docker/Podman**: Versão compatível instalada

### 5.2 Dependências Externas
- **Docker Desktop** (Windows/macOS) ou **Docker Engine** (Linux)
- **Podman** (opcional, como alternativa ao Docker)
- **.NET 9 Runtime** (incluído no instalador)

### 5.3 Permissões Necessárias
- **Windows**: Acesso ao Docker Desktop (usuário no grupo docker-users)
- **Linux**: Acesso ao socket Docker ou Podman
- **macOS**: Acesso ao Docker Desktop

---

## 6. Considerações de Segurança

### 6.1 Comunicação com Container Engines
- Validação de certificados TLS quando aplicável
- Timeout configurável para operações
- Sanitização de inputs do usuário

### 6.2 Gerenciamento de Credenciais
- Armazenamento seguro de credenciais de registry
- Criptografia de dados sensíveis
- Não exposição de secrets em logs

### 6.3 Validação de Imagens
- Verificação de assinaturas quando disponível
- Scan de vulnerabilidades (integração futura)
- Whitelist de registries confiáveis

---

## 7. Monitoramento e Logging

### 7.1 Estrutura de Logs
```csharp
public static class LogCategories
{
    public const string Docker = "AutoInstaller.Docker";
    public const string Podman = "AutoInstaller.Podman";
    public const string UI = "AutoInstaller.UI";
    public const string Application = "AutoInstaller.Application";
}
```

### 7.2 Níveis de Log
- **Trace**: Operações detalhadas de debugging
- **Debug**: Informações de desenvolvimento
- **Information**: Operações normais do sistema
- **Warning**: Situações que requerem atenção
- **Error**: Erros que não impedem a execução
- **Critical**: Erros que impedem a execução

---

*Documento gerado em: Janeiro 2025*
*Versão: 1.0*
*Autor: Arquiteto de Software - Auto-Instalador*