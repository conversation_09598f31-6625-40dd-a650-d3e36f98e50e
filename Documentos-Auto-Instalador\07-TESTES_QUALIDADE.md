# Testes e Qualidade - Auto-Instalador Desktop

## Índice
1. [Estratégia de Testes](#estratégia-de-testes)
2. [Tipos de Testes](#tipos-de-testes)
3. [Ferramentas e Frameworks](#ferramentas-e-frameworks)
4. [Estrutura de Testes](#estrutura-de-testes)
5. [Cobertura de Código](#cobertura-de-código)
6. [Testes de Performance](#testes-de-performance)
7. [Testes de UI](#testes-de-ui)
8. [Integração Contínua](#integração-contínua)
9. [Métricas de Qualidade](#métricas-de-qualidade)
10. [Boas Práticas](#boas-práticas)

---

## Estratégia de Testes

### Pirâmide de Testes

```
        /\     E2E Tests (10%)
       /  \    - Testes de interface completa
      /    \   - Cenários críticos de usuário
     /______\  
    /        \  Integration Tests (20%)
   /          \ - Testes de integração entre camadas
  /            \- Testes com containers reais
 /______________\
/                \ Unit Tests (70%)
\________________/ - Testes unitários isolados
                   - Mocks e stubs
```

### Objetivos de Qualidade
- **Cobertura de Código**: Mínimo 80%, Meta 90%
- **Tempo de Execução**: Testes unitários < 5min, Integração < 15min
- **Confiabilidade**: 99% de sucesso em builds
- **Manutenibilidade**: Testes legíveis e bem documentados

---

## Tipos de Testes

### 1. Testes Unitários

**Escopo**: Testam unidades isoladas de código (métodos, classes)

**Características**:
- Execução rápida (< 100ms por teste)
- Isolados (sem dependências externas)
- Determinísticos (sempre o mesmo resultado)
- Focados em uma única responsabilidade

**Exemplo**:
```csharp
[Test]
public async Task CreateContainer_WithValidData_ShouldReturnContainer()
{
    // Arrange
    var command = new CreateContainerCommand
    {
        Name = "test-container",
        Image = "nginx:latest",
        Ports = new[] { "80:8080" }
    };
    
    var mockRepository = new Mock<IContainerRepository>();
    var handler = new CreateContainerHandler(mockRepository.Object);
    
    // Act
    var result = await handler.Handle(command, CancellationToken.None);
    
    // Assert
    result.Should().NotBeNull();
    result.Name.Should().Be("test-container");
    mockRepository.Verify(x => x.AddAsync(It.IsAny<Container>()), Times.Once);
}
```

### 2. Testes de Integração

**Escopo**: Testam a integração entre componentes

**Características**:
- Testam fluxos completos
- Usam dependências reais quando possível
- Incluem testes com Docker/Podman
- Validam configurações

**Exemplo**:
```csharp
[Test]
public async Task DockerService_CreateContainer_ShouldCreateRealContainer()
{
    // Arrange
    using var dockerClient = new DockerClientConfiguration().CreateClient();
    var service = new DockerContainerService(dockerClient);
    
    var createRequest = new CreateContainerRequest
    {
        Image = "hello-world:latest",
        Name = "integration-test-container"
    };
    
    try
    {
        // Act
        var container = await service.CreateContainerAsync(createRequest);
        
        // Assert
        container.Should().NotBeNull();
        container.Id.Should().NotBeNullOrEmpty();
        
        // Verify container exists in Docker
        var containers = await dockerClient.Containers.ListContainersAsync(
            new ContainersListParameters { All = true });
        containers.Should().Contain(c => c.ID == container.Id);
    }
    finally
    {
        // Cleanup
        await service.RemoveContainerAsync(container.Id, force: true);
    }
}
```

### 3. Testes de UI

**Escopo**: Testam a interface de usuário Avalonia

**Características**:
- Testam ViewModels e interações
- Validam binding de dados
- Testam comandos e navegação
- Simulam interações do usuário

**Exemplo**:
```csharp
[Test]
public async Task ContainerListViewModel_LoadContainers_ShouldPopulateList()
{
    // Arrange
    var mockService = new Mock<IContainerService>();
    var containers = new List<ContainerDto>
    {
        new() { Id = "1", Name = "container1", Status = "running" },
        new() { Id = "2", Name = "container2", Status = "stopped" }
    };
    
    mockService.Setup(x => x.GetContainersAsync())
               .ReturnsAsync(containers);
    
    var viewModel = new ContainerListViewModel(mockService.Object);
    
    // Act
    await viewModel.LoadContainersCommand.Execute();
    
    // Assert
    viewModel.Containers.Should().HaveCount(2);
    viewModel.Containers[0].Name.Should().Be("container1");
    viewModel.IsLoading.Should().BeFalse();
}
```

### 4. Testes End-to-End (E2E)

**Escopo**: Testam cenários completos de usuário

**Características**:
- Simulam uso real da aplicação
- Testam fluxos críticos
- Incluem validação visual
- Executam em ambiente similar à produção

---

## Ferramentas e Frameworks

### Frameworks de Teste

#### xUnit.net
```xml
<PackageReference Include="xunit" Version="2.9.3" />
<PackageReference Include="xunit.runner.visualstudio" Version="3.1.4" />
<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
```

#### FluentAssertions
```xml
<PackageReference Include="FluentAssertions" Version="6.12.1" />
```

#### Moq
```xml
<PackageReference Include="Moq" Version="4.20.70" />
```

#### TestContainers
```xml
<PackageReference Include="Testcontainers" Version="4.6.0" />
<PackageReference Include="Testcontainers.PostgreSql" Version="4.6.0" />
```

#### BenchmarkDotNet
```xml
<PackageReference Include="BenchmarkDotNet" Version="0.13.12" />
```

### Ferramentas de Cobertura

#### Coverlet
```xml
<PackageReference Include="coverlet.collector" Version="6.0.0" />
<PackageReference Include="coverlet.msbuild" Version="6.0.0" />
```

#### ReportGenerator
```bash
dotnet tool install -g dotnet-reportgenerator-globaltool
```

---

## Estrutura de Testes

### Organização de Projetos

```
tests/
├── AutoInstaller.Core.Tests/           # Testes da camada de domínio
│   ├── Entities/
│   ├── ValueObjects/
│   ├── Services/
│   └── Specifications/
├── AutoInstaller.Application.Tests/     # Testes da camada de aplicação
│   ├── Commands/
│   ├── Queries/
│   ├── Handlers/
│   └── Validators/
├── AutoInstaller.Infrastructure.Tests/  # Testes da camada de infraestrutura
│   ├── Repositories/
│   ├── External/
│   └── Services/
├── AutoInstaller.UI.Tests/             # Testes da camada de apresentação
│   ├── ViewModels/
│   ├── Converters/
│   └── Services/
├── AutoInstaller.Integration.Tests/     # Testes de integração
│   ├── Docker/
│   └── Database/
└── AutoInstaller.E2E.Tests/            # Testes end-to-end
    ├── Scenarios/
    ├── PageObjects/
    └── Fixtures/
```

### Convenções de Nomenclatura

#### Classes de Teste
```csharp
// Padrão: {ClasseTestada}Tests
public class ContainerServiceTests { }
public class CreateContainerHandlerTests { }
public class ContainerViewModelTests { }
```

#### Métodos de Teste
```csharp
// Padrão: {Método}_{Cenário}_{ResultadoEsperado}
[Test]
public void CreateContainer_WithValidData_ShouldReturnContainer() { }

[Test]
public void CreateContainer_WithInvalidName_ShouldThrowValidationException() { }

[Test]
public async Task GetContainers_WhenEmpty_ShouldReturnEmptyList() { }
```

---

## Cobertura de Código

### Configuração

#### coverlet.runsettings
```xml
<?xml version="1.0" encoding="utf-8" ?>
<RunSettings>
  <DataCollectionRunSettings>
    <DataCollectors>
      <DataCollector friendlyName="XPlat code coverage">
        <Configuration>
          <Format>cobertura,opencover</Format>
          <Exclude>[*.Tests]*,[*]*.Program,[*]*.Startup</Exclude>
          <ExcludeByAttribute>Obsolete,GeneratedCodeAttribute,CompilerGeneratedAttribute</ExcludeByAttribute>
        </Configuration>
      </DataCollector>
    </DataCollectors>
  </DataCollectionRunSettings>
</RunSettings>
```

### Comandos de Execução

```bash
# Executar testes com cobertura
dotnet test --collect:"XPlat Code Coverage" --settings coverlet.runsettings

# Gerar relatório HTML
reportgenerator \
  -reports:"**/coverage.cobertura.xml" \
  -targetdir:"coverage-report" \
  -reporttypes:Html

# Verificar cobertura mínima
dotnet test /p:CollectCoverage=true /p:Threshold=80 /p:ThresholdType=line
```

### Métricas de Cobertura

- **Line Coverage**: Percentual de linhas executadas
- **Branch Coverage**: Percentual de branches executados
- **Method Coverage**: Percentual de métodos executados
- **Class Coverage**: Percentual de classes testadas

---

## Testes de Performance

### BenchmarkDotNet

```csharp
[MemoryDiagnoser]
[SimpleJob(RuntimeMoniker.Net90)]
public class ContainerOperationsBenchmark
{
    private IContainerService _containerService;
    
    [GlobalSetup]
    public void Setup()
    {
        _containerService = new DockerContainerService(/* config */);
    }
    
    [Benchmark]
    public async Task CreateContainer()
    {
        var request = new CreateContainerRequest
        {
            Image = "nginx:latest",
            Name = $"benchmark-{Guid.NewGuid()}"
        };
        
        var container = await _containerService.CreateContainerAsync(request);
        await _containerService.RemoveContainerAsync(container.Id, force: true);
    }
    
    [Benchmark]
    [Arguments(10)]
    [Arguments(50)]
    [Arguments(100)]
    public async Task ListContainers(int containerCount)
    {
        // Setup containers
        var containers = new List<string>();
        for (int i = 0; i < containerCount; i++)
        {
            var container = await _containerService.CreateContainerAsync(
                new CreateContainerRequest { Image = "alpine:latest" });
            containers.Add(container.Id);
        }
        
        // Benchmark
        var result = await _containerService.GetContainersAsync();
        
        // Cleanup
        foreach (var containerId in containers)
        {
            await _containerService.RemoveContainerAsync(containerId, force: true);
        }
    }
}
```

### Testes de Carga

```csharp
[Test]
public async Task ContainerService_ConcurrentOperations_ShouldHandleLoad()
{
    const int concurrentOperations = 50;
    var tasks = new List<Task>();
    
    for (int i = 0; i < concurrentOperations; i++)
    {
        tasks.Add(Task.Run(async () =>
        {
            var container = await _containerService.CreateContainerAsync(
                new CreateContainerRequest { Image = "alpine:latest" });
            
            await _containerService.StartContainerAsync(container.Id);
            await Task.Delay(100); // Simulate work
            await _containerService.StopContainerAsync(container.Id);
            await _containerService.RemoveContainerAsync(container.Id, force: true);
        }));
    }
    
    // All operations should complete without errors
    await Task.WhenAll(tasks);
}
```

---

## Testes de UI

### Testes de ViewModel

```csharp
public class ContainerListViewModelTests
{
    private readonly Mock<IContainerService> _mockContainerService;
    private readonly Mock<IDialogService> _mockDialogService;
    private readonly ContainerListViewModel _viewModel;
    
    public ContainerListViewModelTests()
    {
        _mockContainerService = new Mock<IContainerService>();
        _mockDialogService = new Mock<IDialogService>();
        _viewModel = new ContainerListViewModel(
            _mockContainerService.Object,
            _mockDialogService.Object);
    }
    
    [Test]
    public async Task RefreshCommand_ShouldLoadContainers()
    {
        // Arrange
        var containers = new List<ContainerDto>
        {
            new() { Id = "1", Name = "test1", Status = "running" }
        };
        
        _mockContainerService.Setup(x => x.GetContainersAsync())
                            .ReturnsAsync(containers);
        
        // Act
        await _viewModel.RefreshCommand.Execute();
        
        // Assert
        _viewModel.Containers.Should().HaveCount(1);
        _viewModel.Containers[0].Name.Should().Be("test1");
        _viewModel.IsLoading.Should().BeFalse();
    }
    
    [Test]
    public async Task DeleteContainerCommand_WithConfirmation_ShouldDeleteContainer()
    {
        // Arrange
        var container = new ContainerDto { Id = "1", Name = "test1" };
        _viewModel.Containers.Add(container);
        
        _mockDialogService.Setup(x => x.ShowConfirmationAsync(It.IsAny<string>()))
                         .ReturnsAsync(true);
        
        _mockContainerService.Setup(x => x.DeleteContainerAsync("1"))
                            .Returns(Task.CompletedTask);
        
        // Act
        await _viewModel.DeleteContainerCommand.Execute(container);
        
        // Assert
        _viewModel.Containers.Should().BeEmpty();
        _mockContainerService.Verify(x => x.DeleteContainerAsync("1"), Times.Once);
    }
}
```

### Testes de Binding

```csharp
[Test]
public void ContainerViewModel_PropertyChanges_ShouldNotifyUI()
{
    // Arrange
    var container = new ContainerDto { Id = "1", Name = "test", Status = "stopped" };
    var viewModel = new ContainerViewModel(container);
    
    var propertyChangedEvents = new List<string>();
    viewModel.PropertyChanged += (s, e) => propertyChangedEvents.Add(e.PropertyName);
    
    // Act
    viewModel.Status = "running";
    
    // Assert
    propertyChangedEvents.Should().Contain(nameof(ContainerViewModel.Status));
    propertyChangedEvents.Should().Contain(nameof(ContainerViewModel.IsRunning));
    propertyChangedEvents.Should().Contain(nameof(ContainerViewModel.CanStart));
    propertyChangedEvents.Should().Contain(nameof(ContainerViewModel.CanStop));
}
```

---

## Integração Contínua

### GitHub Actions Workflow

```yaml
name: Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        dotnet-version: ['9.0.x']
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ matrix.dotnet-version }}
    
    - name: Restore dependencies
      run: dotnet restore
    
    - name: Build
      run: dotnet build --no-restore --configuration Release
    
    - name: Run Unit Tests
      run: |
        dotnet test tests/AutoInstaller.Core.Tests \
          --no-build --configuration Release \
          --collect:"XPlat Code Coverage" \
          --logger trx --results-directory TestResults/
    
    - name: Run Integration Tests
      run: |
        dotnet test tests/AutoInstaller.Integration.Tests \
          --no-build --configuration Release \
          --logger trx --results-directory TestResults/
    
    - name: Generate Coverage Report
      run: |
        dotnet tool install -g dotnet-reportgenerator-globaltool
        reportgenerator \
          -reports:"TestResults/**/coverage.cobertura.xml" \
          -targetdir:"coverage-report" \
          -reporttypes:"Html;Cobertura"
    
    - name: Upload Coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: coverage-report/Cobertura.xml
        fail_ci_if_error: true
    
    - name: Publish Test Results
      uses: dorny/test-reporter@v1
      if: success() || failure()
      with:
        name: Test Results (${{ matrix.os }})
        path: TestResults/*.trx
        reporter: dotnet-trx
```

### Quality Gates

```yaml
# .github/workflows/quality-gate.yml
name: Quality Gate

on:
  pull_request:
    branches: [ main ]

jobs:
  quality-gate:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '9.0.x'
    
    - name: Run Tests with Coverage
      run: |
        dotnet test --collect:"XPlat Code Coverage" \
          /p:Threshold=80 /p:ThresholdType=line \
          /p:ThresholdStat=total
    
    - name: Run Security Scan
      run: dotnet list package --vulnerable --include-transitive
    
    - name: Run Code Analysis
      run: dotnet build --verbosity normal /p:TreatWarningsAsErrors=true
    
    - name: Check Code Formatting
      run: dotnet format --verify-no-changes --verbosity diagnostic
```

---

## Métricas de Qualidade

### Métricas de Código

#### Complexidade Ciclomática
- **Baixa**: 1-10 (Simples)
- **Moderada**: 11-20 (Moderadamente complexo)
- **Alta**: 21-50 (Complexo)
- **Muito Alta**: >50 (Muito complexo - refatorar)

#### Métricas de Manutenibilidade
- **Maintainability Index**: >85 (Boa), 70-85 (Moderada), <70 (Baixa)
- **Lines of Code**: <100 por método, <500 por classe
- **Depth of Inheritance**: <6 níveis
- **Coupling**: <10 dependências por classe

### Métricas de Teste

#### Cobertura
- **Line Coverage**: >80%
- **Branch Coverage**: >75%
- **Method Coverage**: >90%
- **Class Coverage**: >95%

#### Performance
- **Unit Tests**: <100ms por teste
- **Integration Tests**: <5s por teste
- **E2E Tests**: <30s por cenário
- **Total Test Suite**: <15min

### Relatórios de Qualidade

```bash
# Gerar relatório de métricas
dotnet tool install -g dotnet-metrics
dotnet metrics --project src/AutoInstaller.sln --output metrics-report.json

# Análise de código estático
dotnet tool install -g security-scan
security-scan src/AutoInstaller.sln

# Relatório de dependências vulneráveis
dotnet list package --vulnerable --include-transitive > vulnerability-report.txt
```

---

## Boas Práticas

### Princípios FIRST

- **F**ast: Testes devem ser rápidos
- **I**ndependent: Testes não devem depender uns dos outros
- **R**epeatable: Testes devem ser repetíveis em qualquer ambiente
- **S**elf-Validating: Testes devem ter resultado claro (pass/fail)
- **T**imely: Testes devem ser escritos no momento certo

### Padrão AAA (Arrange-Act-Assert)

```csharp
[Test]
public async Task ExampleTest()
{
    // Arrange - Configurar o cenário de teste
    var mockService = new Mock<IService>();
    var handler = new Handler(mockService.Object);
    var command = new Command { /* dados */ };
    
    // Act - Executar a ação sendo testada
    var result = await handler.Handle(command);
    
    // Assert - Verificar o resultado
    result.Should().NotBeNull();
    result.Success.Should().BeTrue();
}
```

### Testes Determinísticos

```csharp
// ❌ Evitar - Teste não determinístico
[Test]
public void BadTest()
{
    var result = DateTime.Now.AddDays(Random.Next(1, 10));
    result.Should().BeAfter(DateTime.Now); // Pode falhar
}

// ✅ Correto - Teste determinístico
[Test]
public void GoodTest()
{
    var baseDate = new DateTime(2024, 1, 1);
    var result = baseDate.AddDays(5);
    result.Should().Be(new DateTime(2024, 1, 6));
}
```

### Isolamento de Testes

```csharp
// ✅ Cada teste é independente
public class IsolatedTests
{
    [Test]
    public void Test1()
    {
        var service = CreateService(); // Novo para cada teste
        // teste específico
    }
    
    [Test]
    public void Test2()
    {
        var service = CreateService(); // Novo para cada teste
        // teste específico
    }
    
    private IService CreateService()
    {
        return new Service(new Mock<IDependency>().Object);
    }
}
```

### Nomenclatura Descritiva

```csharp
// ✅ Nome descritivo que explica o cenário
[Test]
public async Task CreateContainer_WhenDockerIsNotRunning_ShouldThrowDockerNotAvailableException()
{
    // Teste específico para quando Docker não está rodando
}

// ✅ Teste com múltiplos cenários
[TestCase("valid-name", true)]
[TestCase("invalid name with spaces", false)]
[TestCase("", false)]
[TestCase(null, false)]
public void ValidateContainerName_WithDifferentInputs_ShouldReturnExpectedResult(
    string containerName, bool expectedValid)
{
    var result = ContainerNameValidator.IsValid(containerName);
    result.Should().Be(expectedValid);
}
```

### Cleanup e Recursos

```csharp
public class ResourceManagementTests : IDisposable
{
    private readonly IContainerService _containerService;
    private readonly List<string> _createdContainers;
    
    public ResourceManagementTests()
    {
        _containerService = new DockerContainerService();
        _createdContainers = new List<string>();
    }
    
    [Test]
    public async Task TestWithCleanup()
    {
        // Arrange
        var container = await _containerService.CreateContainerAsync(
            new CreateContainerRequest { Image = "alpine:latest" });
        _createdContainers.Add(container.Id);
        
        // Act & Assert
        // ... teste
    }
    
    public void Dispose()
    {
        // Cleanup automático de todos os containers criados
        foreach (var containerId in _createdContainers)
        {
            try
            {
                _containerService.RemoveContainerAsync(containerId, force: true)
                                .GetAwaiter().GetResult();
            }
            catch
            {
                // Log error but don't fail cleanup
            }
        }
    }
}
```

---

## Conclusão

Este documento estabelece as diretrizes completas para testes e qualidade no projeto Auto-Instalador Desktop. A implementação rigorosa dessas práticas garante:

- **Confiabilidade**: Código testado e validado
- **Manutenibilidade**: Testes claros e bem estruturados
- **Performance**: Aplicação otimizada e monitorada
- **Qualidade**: Métricas objetivas de qualidade de código
- **Automação**: Integração contínua com validação automática

Todos os desenvolvedores devem seguir estas diretrizes para manter a alta qualidade do projeto.