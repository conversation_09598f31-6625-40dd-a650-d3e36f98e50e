using MediatR;

namespace AutoInstaller.Application.Common;

/// <summary>
/// Interface base para comandos sem retorno
/// </summary>
public interface ICommand : IRequest
{
}

/// <summary>
/// Interface base para comandos com retorno
/// </summary>
/// <typeparam name="TResponse">Tipo da resposta</typeparam>
public interface ICommand<out TResponse> : IRequest<TResponse>
{
}

/// <summary>
/// Interface base para handlers de comandos sem retorno
/// </summary>
/// <typeparam name="TCommand">Tipo do comando</typeparam>
public interface ICommandHandler<in TCommand> : IRequestHandler<TCommand>
    where TCommand : ICommand
{
}

/// <summary>
/// Interface base para handlers de comandos com retorno
/// </summary>
/// <typeparam name="TCommand">Tipo do comando</typeparam>
/// <typeparam name="TResponse">Tipo da resposta</typeparam>
public interface ICommandHandler<in TCommand, TResponse> : IRequestHandler<TCommand, TResponse>
    where TCommand : ICommand<TResponse>
{
}

/// <summary>
/// Classe base abstrata para comandos
/// </summary>
public abstract class BaseCommand : ICommand
{
    /// <summary>
    /// Usuário que está executando o comando
    /// </summary>
    public string ExecutedBy { get; set; } = string.Empty;

    /// <summary>
    /// Timestamp da execução
    /// </summary>
    public DateTime ExecutedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// ID de correlação para rastreamento
    /// </summary>
    public string CorrelationId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// Metadados adicionais
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// Construtor protegido
    /// </summary>
    protected BaseCommand()
    {
    }

    /// <summary>
    /// Construtor com usuário executor
    /// </summary>
    /// <param name="executedBy">Usuário que executa o comando</param>
    protected BaseCommand(string executedBy)
    {
        if (string.IsNullOrWhiteSpace(executedBy))
            throw new ArgumentException("Usuário executor não pode ser vazio", nameof(executedBy));

        ExecutedBy = executedBy.Trim();
    }

    /// <summary>
    /// Adiciona metadados ao comando
    /// </summary>
    /// <param name="key">Chave do metadado</param>
    /// <param name="value">Valor do metadado</param>
    public void AddMetadata(string key, object value)
    {
        if (string.IsNullOrWhiteSpace(key))
            throw new ArgumentException("Chave do metadado não pode ser vazia", nameof(key));

        Metadata[key.Trim()] = value;
    }

    /// <summary>
    /// Obtém um metadado
    /// </summary>
    /// <typeparam name="T">Tipo do metadado</typeparam>
    /// <param name="key">Chave do metadado</param>
    /// <returns>Valor do metadado ou default(T)</returns>
    public T? GetMetadata<T>(string key)
    {
        if (string.IsNullOrWhiteSpace(key) || !Metadata.TryGetValue(key.Trim(), out var value))
            return default;

        if (value is T typedValue)
            return typedValue;

        try
        {
            return (T)Convert.ChangeType(value, typeof(T));
        }
        catch
        {
            return default;
        }
    }
}

/// <summary>
/// Classe base abstrata para comandos com retorno
/// </summary>
/// <typeparam name="TResponse">Tipo da resposta</typeparam>
public abstract class BaseCommand<TResponse> : ICommand<TResponse>
{
    /// <summary>
    /// Usuário que está executando o comando
    /// </summary>
    public string ExecutedBy { get; set; } = string.Empty;

    /// <summary>
    /// Timestamp da execução
    /// </summary>
    public DateTime ExecutedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// ID de correlação para rastreamento
    /// </summary>
    public string CorrelationId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// Metadados adicionais
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// Construtor protegido
    /// </summary>
    protected BaseCommand()
    {
    }

    /// <summary>
    /// Construtor com usuário executor
    /// </summary>
    /// <param name="executedBy">Usuário que executa o comando</param>
    protected BaseCommand(string executedBy)
    {
        if (string.IsNullOrWhiteSpace(executedBy))
            throw new ArgumentException("Usuário executor não pode ser vazio", nameof(executedBy));

        ExecutedBy = executedBy.Trim();
    }

    /// <summary>
    /// Adiciona metadados ao comando
    /// </summary>
    /// <param name="key">Chave do metadado</param>
    /// <param name="value">Valor do metadado</param>
    public void AddMetadata(string key, object value)
    {
        if (string.IsNullOrWhiteSpace(key))
            throw new ArgumentException("Chave do metadado não pode ser vazia", nameof(key));

        Metadata[key.Trim()] = value;
    }

    /// <summary>
    /// Obtém um metadado
    /// </summary>
    /// <typeparam name="T">Tipo do metadado</typeparam>
    /// <param name="key">Chave do metadado</param>
    /// <returns>Valor do metadado ou default(T)</returns>
    public T? GetMetadata<T>(string key)
    {
        if (string.IsNullOrWhiteSpace(key) || !Metadata.TryGetValue(key.Trim(), out var value))
            return default;

        if (value is T typedValue)
            return typedValue;

        try
        {
            return (T)Convert.ChangeType(value, typeof(T));
        }
        catch
        {
            return default;
        }
    }
}
