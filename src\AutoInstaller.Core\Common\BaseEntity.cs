using AutoInstaller.Core.Interfaces;

namespace AutoInstaller.Core.Common;

/// <summary>
/// Classe base para todas as entidades do domínio
/// </summary>
public abstract class BaseEntity
{
    private readonly List<IDomainEvent> _domainEvents = new();

    /// <summary>
    /// Identificador único da entidade
    /// </summary>
    public Guid Id { get; protected set; }

    /// <summary>
    /// Indica se a entidade foi excluída (soft delete)
    /// </summary>
    public bool IsDeleted { get; protected set; }

    /// <summary>
    /// Data de criação da entidade
    /// </summary>
    public DateTime CreatedAt { get; protected set; }

    /// <summary>
    /// Data da última atualização
    /// </summary>
    public DateTime? UpdatedAt { get; protected set; }

    /// <summary>
    /// Usuário que criou a entidade
    /// </summary>
    public string CreatedBy { get; protected set; } = string.Empty;

    /// <summary>
    /// Usuário que fez a última atualização
    /// </summary>
    public string? UpdatedBy { get; protected set; }

    /// <summary>
    /// Eventos de domínio da entidade
    /// </summary>
    public IReadOnlyCollection<IDomainEvent> DomainEvents => _domainEvents.AsReadOnly();

    /// <summary>
    /// Construtor protegido para EF Core
    /// </summary>
    protected BaseEntity()
    {
        Id = Guid.NewGuid();
        CreatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Construtor com usuário criador
    /// </summary>
    /// <param name="createdBy">Usuário que criou a entidade</param>
    protected BaseEntity(string createdBy) : this()
    {
        if (string.IsNullOrWhiteSpace(createdBy))
            throw new ArgumentException("Usuário criador não pode ser vazio", nameof(createdBy));

        CreatedBy = createdBy.Trim();
    }

    /// <summary>
    /// Adiciona um evento de domínio
    /// </summary>
    /// <param name="domainEvent">Evento a ser adicionado</param>
    protected void AddDomainEvent(IDomainEvent domainEvent)
    {
        _domainEvents.Add(domainEvent);
    }

    /// <summary>
    /// Remove um evento de domínio
    /// </summary>
    /// <param name="domainEvent">Evento a ser removido</param>
    protected void RemoveDomainEvent(IDomainEvent domainEvent)
    {
        _domainEvents.Remove(domainEvent);
    }

    /// <summary>
    /// Limpa todos os eventos de domínio
    /// </summary>
    public void ClearDomainEvents()
    {
        _domainEvents.Clear();
    }

    /// <summary>
    /// Marca a entidade como atualizada
    /// </summary>
    /// <param name="updatedBy">Usuário que fez a atualização</param>
    protected void MarkAsUpdated(string updatedBy)
    {
        if (string.IsNullOrWhiteSpace(updatedBy))
            throw new ArgumentException("Usuário que atualizou não pode ser vazio", nameof(updatedBy));

        UpdatedAt = DateTime.UtcNow;
        UpdatedBy = updatedBy.Trim();
    }

    /// <summary>
    /// Marca a entidade como excluída (soft delete)
    /// </summary>
    /// <param name="deletedBy">Usuário que excluiu a entidade</param>
    public virtual void MarkAsDeleted(string deletedBy)
    {
        if (string.IsNullOrWhiteSpace(deletedBy))
            throw new ArgumentException("Usuário que excluiu não pode ser vazio", nameof(deletedBy));

        IsDeleted = true;
        MarkAsUpdated(deletedBy);
    }

    /// <summary>
    /// Restaura uma entidade excluída
    /// </summary>
    /// <param name="restoredBy">Usuário que restaurou a entidade</param>
    public virtual void Restore(string restoredBy)
    {
        if (string.IsNullOrWhiteSpace(restoredBy))
            throw new ArgumentException("Usuário que restaurou não pode ser vazio", nameof(restoredBy));

        IsDeleted = false;
        MarkAsUpdated(restoredBy);
    }

    /// <summary>
    /// Verifica se duas entidades são iguais baseado no Id
    /// </summary>
    public override bool Equals(object? obj)
    {
        if (obj is not BaseEntity other)
            return false;

        if (ReferenceEquals(this, other))
            return true;

        if (GetType() != other.GetType())
            return false;

        return Id.Equals(other.Id);
    }

    /// <summary>
    /// Retorna o hash code baseado no Id
    /// </summary>
    public override int GetHashCode()
    {
        return Id.GetHashCode();
    }

    /// <summary>
    /// Operador de igualdade
    /// </summary>
    public static bool operator ==(BaseEntity? left, BaseEntity? right)
    {
        return Equals(left, right);
    }

    /// <summary>
    /// Operador de desigualdade
    /// </summary>
    public static bool operator !=(BaseEntity? left, BaseEntity? right)
    {
        return !Equals(left, right);
    }
}
