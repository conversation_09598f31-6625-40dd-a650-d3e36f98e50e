{"version": 2, "dgSpecHash": "XNNNfrEvw78=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\Projetos_IA\\auto-instalador-max\\src\\AutoInstaller.Core\\AutoInstaller.Core.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\fluentvalidation\\11.10.0\\fluentvalidation.11.10.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mediatr\\11.1.0\\mediatr.11.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mediatr.contracts\\1.0.1\\mediatr.contracts.1.0.1.nupkg.sha512"], "logs": []}