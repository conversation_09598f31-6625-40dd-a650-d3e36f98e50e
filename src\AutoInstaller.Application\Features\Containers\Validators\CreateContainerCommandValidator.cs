using AutoInstaller.Application.DTOs;
using AutoInstaller.Application.Features.Containers.Commands;
using AutoInstaller.Core.ValueObjects;
using FluentValidation;

namespace AutoInstaller.Application.Features.Containers.Validators;

/// <summary>
/// Validator para CreateContainerCommand
/// </summary>
public class CreateContainerCommandValidator : AbstractValidator<CreateContainerCommand>
{
    public CreateContainerCommandValidator()
    {
        RuleFor(x => x.ExecutedBy)
            .NotEmpty()
            .WithMessage("Usuário executor é obrigatório")
            .MaximumLength(100)
            .WithMessage("Usuário executor deve ter no máximo 100 caracteres");

        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("Nome do container é obrigatório")
            .Must(BeValidContainerName)
            .WithMessage("Nome do container deve ser válido (apenas letras, números, underscore, ponto ou hífen)");

        RuleFor(x => x.ImageName)
            .NotEmpty()
            .WithMessage("Nome da imagem é obrigatório")
            .MaximumLength(255)
            .WithMessage("Nome da imagem deve ter no máximo 255 caracteres")
            .Must(BeValidImageName)
            .WithMessage("Nome da imagem deve ser válido");

        RuleFor(x => x.ImageTag)
            .NotEmpty()
            .WithMessage("Tag da imagem é obrigatória")
            .Must(BeValidImageTag)
            .WithMessage("Tag da imagem deve ser válida");

        RuleFor(x => x.Runtime)
            .IsInEnum()
            .WithMessage("Runtime deve ser Docker ou Podman");

        RuleFor(x => x.Command)
            .MaximumLength(500)
            .WithMessage("Comando deve ter no máximo 500 caracteres")
            .When(x => !string.IsNullOrEmpty(x.Command));

        RuleFor(x => x.Arguments)
            .Must(HaveValidArguments)
            .WithMessage("Argumentos devem ser válidos")
            .When(x => x.Arguments != null);

        RuleFor(x => x.WorkingDirectory)
            .MaximumLength(500)
            .WithMessage("Diretório de trabalho deve ter no máximo 500 caracteres")
            .When(x => !string.IsNullOrEmpty(x.WorkingDirectory));

        RuleFor(x => x.User)
            .MaximumLength(100)
            .WithMessage("Usuário deve ter no máximo 100 caracteres")
            .When(x => !string.IsNullOrEmpty(x.User));

        RuleFor(x => x.RestartPolicy)
            .NotEmpty()
            .WithMessage("Política de reinicialização é obrigatória")
            .Must(BeValidRestartPolicy)
            .WithMessage("Política de reinicialização deve ser 'no', 'always', 'unless-stopped' ou 'on-failure'");

        RuleFor(x => x.MemoryLimit)
            .GreaterThan(0)
            .WithMessage("Limite de memória deve ser positivo")
            .When(x => x.MemoryLimit.HasValue);

        RuleFor(x => x.CpuLimit)
            .GreaterThan(0)
            .WithMessage("Limite de CPU deve ser positivo")
            .When(x => x.CpuLimit.HasValue);

        RuleFor(x => x.ExposedPorts)
            .Must(HaveValidPorts)
            .WithMessage("Portas expostas devem ser válidas")
            .When(x => x.ExposedPorts.Any());

        RuleFor(x => x.EnvironmentVariables)
            .Must(HaveValidEnvironmentVariables)
            .WithMessage("Variáveis de ambiente devem ter chaves válidas")
            .When(x => x.EnvironmentVariables.Any());

        RuleFor(x => x.Labels)
            .Must(HaveValidLabels)
            .WithMessage("Labels devem ter chaves válidas")
            .When(x => x.Labels.Any());

        RuleFor(x => x.Volumes)
            .Must(HaveValidVolumes)
            .WithMessage("Volumes devem ter definições válidas")
            .When(x => x.Volumes.Any());
    }

    private static bool BeValidContainerName(string name)
    {
        return ContainerName.IsValid(name);
    }

    private static bool BeValidImageName(string imageName)
    {
        if (string.IsNullOrWhiteSpace(imageName))
            return false;

        // Validação básica de nome de imagem Docker/Podman
        // Pode conter letras minúsculas, números, pontos, hífens e barras
        var validChars = imageName.All(c => char.IsLetterOrDigit(c) || c == '.' || c == '-' || c == '/' || c == '_');
        var notStartsWithSpecialChar = !imageName.StartsWith('.') && !imageName.StartsWith('-') && !imageName.StartsWith('/');
        var notEndsWithSpecialChar = !imageName.EndsWith('.') && !imageName.EndsWith('-') && !imageName.EndsWith('/');

        return validChars && notStartsWithSpecialChar && notEndsWithSpecialChar;
    }

    private static bool BeValidImageTag(string imageTag)
    {
        return ImageTag.IsValid(imageTag);
    }

    private static bool HaveValidArguments(string[]? arguments)
    {
        if (arguments == null)
            return true;

        return arguments.All(arg => !string.IsNullOrWhiteSpace(arg) && arg.Length <= 1000);
    }

    private static bool BeValidRestartPolicy(string? restartPolicy)
    {
        if (string.IsNullOrEmpty(restartPolicy))
            return false;

        var validPolicies = new[] { "no", "always", "unless-stopped", "on-failure" };
        return validPolicies.Contains(restartPolicy.ToLowerInvariant());
    }

    private static bool HaveValidPorts(List<CreatePortDto> ports)
    {
        if (!ports.Any())
            return true;

        foreach (var port in ports)
        {
            if (!Port.IsValid(port.Number))
                return false;

            if (!Enum.TryParse<PortProtocol>(port.Protocol, true, out _))
                return false;
        }

        // Verificar se não há portas duplicadas
        var duplicates = ports.GroupBy(p => new { p.Number, Protocol = p.Protocol.ToUpperInvariant() })
                              .Where(g => g.Count() > 1)
                              .Any();

        return !duplicates;
    }

    private static bool HaveValidEnvironmentVariables(Dictionary<string, string> envVars)
    {
        if (!envVars.Any())
            return true;

        foreach (var kvp in envVars)
        {
            // Chave não pode ser vazia e deve seguir padrão de variável de ambiente
            if (string.IsNullOrWhiteSpace(kvp.Key))
                return false;

            // Chave deve começar com letra ou underscore e conter apenas letras, números e underscores
            if (!System.Text.RegularExpressions.Regex.IsMatch(kvp.Key, @"^[a-zA-Z_][a-zA-Z0-9_]*$"))
                return false;

            // Valor pode ser qualquer string (incluindo vazia)
            if (kvp.Value == null)
                return false;
        }

        return true;
    }

    private static bool HaveValidLabels(Dictionary<string, string> labels)
    {
        if (!labels.Any())
            return true;

        foreach (var kvp in labels)
        {
            // Chave não pode ser vazia
            if (string.IsNullOrWhiteSpace(kvp.Key))
                return false;

            // Chave deve ter no máximo 63 caracteres (padrão Docker)
            if (kvp.Key.Length > 63)
                return false;

            // Valor pode ser qualquer string (incluindo vazia)
            if (kvp.Value == null)
                return false;

            // Valor deve ter no máximo 255 caracteres
            if (kvp.Value.Length > 255)
                return false;
        }

        return true;
    }

    private static bool HaveValidVolumes(List<string> volumes)
    {
        if (!volumes.Any())
            return true;

        foreach (var volume in volumes)
        {
            if (string.IsNullOrWhiteSpace(volume))
                return false;

            // Validação básica de definição de volume
            // Pode ser: /host/path:/container/path, volume_name:/container/path, etc.
            var parts = volume.Split(':');
            if (parts.Length < 1 || parts.Length > 3)
                return false;

            // Cada parte não pode ser vazia
            if (parts.Any(string.IsNullOrWhiteSpace))
                return false;
        }

        return true;
    }
}

/// <summary>
/// Validator para UpdateContainerCommand
/// </summary>
public class UpdateContainerCommandValidator : AbstractValidator<UpdateContainerCommand>
{
    public UpdateContainerCommandValidator()
    {
        RuleFor(x => x.ExecutedBy)
            .NotEmpty()
            .WithMessage("Usuário executor é obrigatório")
            .MaximumLength(100)
            .WithMessage("Usuário executor deve ter no máximo 100 caracteres");

        RuleFor(x => x.Id)
            .NotEmpty()
            .WithMessage("ID do container é obrigatório");

        RuleFor(x => x.Command)
            .MaximumLength(500)
            .WithMessage("Comando deve ter no máximo 500 caracteres")
            .When(x => !string.IsNullOrEmpty(x.Command));

        RuleFor(x => x.Arguments)
            .Must(HaveValidArguments)
            .WithMessage("Argumentos devem ser válidos")
            .When(x => x.Arguments != null);

        RuleFor(x => x.WorkingDirectory)
            .MaximumLength(500)
            .WithMessage("Diretório de trabalho deve ter no máximo 500 caracteres")
            .When(x => !string.IsNullOrEmpty(x.WorkingDirectory));

        RuleFor(x => x.User)
            .MaximumLength(100)
            .WithMessage("Usuário deve ter no máximo 100 caracteres")
            .When(x => !string.IsNullOrEmpty(x.User));

        RuleFor(x => x.RestartPolicy)
            .Must(BeValidRestartPolicy)
            .WithMessage("Política de reinicialização deve ser 'no', 'always', 'unless-stopped' ou 'on-failure'")
            .When(x => !string.IsNullOrEmpty(x.RestartPolicy));

        RuleFor(x => x.MemoryLimit)
            .GreaterThan(0)
            .WithMessage("Limite de memória deve ser positivo")
            .When(x => x.MemoryLimit.HasValue);

        RuleFor(x => x.CpuLimit)
            .GreaterThan(0)
            .WithMessage("Limite de CPU deve ser positivo")
            .When(x => x.CpuLimit.HasValue);
    }

    private static bool HaveValidArguments(string[]? arguments)
    {
        if (arguments == null)
            return true;

        return arguments.All(arg => !string.IsNullOrWhiteSpace(arg) && arg.Length <= 1000);
    }

    private static bool BeValidRestartPolicy(string? restartPolicy)
    {
        if (string.IsNullOrEmpty(restartPolicy))
            return false;

        var validPolicies = new[] { "no", "always", "unless-stopped", "on-failure" };
        return validPolicies.Contains(restartPolicy.ToLowerInvariant());
    }
}

/// <summary>
/// Validator para DeleteContainerCommand
/// </summary>
public class DeleteContainerCommandValidator : AbstractValidator<DeleteContainerCommand>
{
    public DeleteContainerCommandValidator()
    {
        RuleFor(x => x.ExecutedBy)
            .NotEmpty()
            .WithMessage("Usuário executor é obrigatório")
            .MaximumLength(100)
            .WithMessage("Usuário executor deve ter no máximo 100 caracteres");

        RuleFor(x => x.Id)
            .NotEmpty()
            .WithMessage("ID do container é obrigatório");
    }
}

/// <summary>
/// Validator para StartContainerCommand
/// </summary>
public class StartContainerCommandValidator : AbstractValidator<StartContainerCommand>
{
    public StartContainerCommandValidator()
    {
        RuleFor(x => x.ExecutedBy)
            .NotEmpty()
            .WithMessage("Usuário executor é obrigatório")
            .MaximumLength(100)
            .WithMessage("Usuário executor deve ter no máximo 100 caracteres");

        RuleFor(x => x.Id)
            .NotEmpty()
            .WithMessage("ID do container é obrigatório");
    }
}

/// <summary>
/// Validator para StopContainerCommand
/// </summary>
public class StopContainerCommandValidator : AbstractValidator<StopContainerCommand>
{
    public StopContainerCommandValidator()
    {
        RuleFor(x => x.ExecutedBy)
            .NotEmpty()
            .WithMessage("Usuário executor é obrigatório")
            .MaximumLength(100)
            .WithMessage("Usuário executor deve ter no máximo 100 caracteres");

        RuleFor(x => x.Id)
            .NotEmpty()
            .WithMessage("ID do container é obrigatório");

        RuleFor(x => x.TimeoutSeconds)
            .GreaterThan(0)
            .WithMessage("Timeout deve ser positivo")
            .LessThanOrEqualTo(300)
            .WithMessage("Timeout deve ser no máximo 300 segundos");
    }
}

/// <summary>
/// Validator para RestartContainerCommand
/// </summary>
public class RestartContainerCommandValidator : AbstractValidator<RestartContainerCommand>
{
    public RestartContainerCommandValidator()
    {
        RuleFor(x => x.ExecutedBy)
            .NotEmpty()
            .WithMessage("Usuário executor é obrigatório")
            .MaximumLength(100)
            .WithMessage("Usuário executor deve ter no máximo 100 caracteres");

        RuleFor(x => x.Id)
            .NotEmpty()
            .WithMessage("ID do container é obrigatório");

        RuleFor(x => x.TimeoutSeconds)
            .GreaterThan(0)
            .WithMessage("Timeout deve ser positivo")
            .LessThanOrEqualTo(300)
            .WithMessage("Timeout deve ser no máximo 300 segundos");
    }
}
