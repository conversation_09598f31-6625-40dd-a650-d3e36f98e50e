using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Collections.ObjectModel;
using ReactiveUI;
using System.Reactive;
using System.Reactive.Linq;
using Microsoft.Extensions.Logging;

namespace AutoInstaller.UI.ViewModels;

/// <summary>
/// ViewModel base com implementação de INotifyPropertyChanged e funcionalidades comuns
/// </summary>
public abstract class BaseViewModel : ReactiveObject, INotifyPropertyChanged
{
    protected readonly ILogger _logger;
    private bool _isBusy;
    private string _title = string.Empty;
    private string _errorMessage = string.Empty;
    private bool _hasError;

    /// <summary>
    /// Indica se o ViewModel está ocupado (carregando)
    /// </summary>
    public bool IsBusy
    {
        get => _isBusy;
        set => this.RaiseAndSetIfChanged(ref _isBusy, value);
    }

    /// <summary>
    /// Título da view/página
    /// </summary>
    public string Title
    {
        get => _title;
        set => this.RaiseAndSetIfChanged(ref _title, value);
    }

    /// <summary>
    /// Mensagem de erro atual
    /// </summary>
    public string ErrorMessage
    {
        get => _errorMessage;
        set => this.RaiseAndSetIfChanged(ref _errorMessage, value);
    }

    /// <summary>
    /// Indica se há erro
    /// </summary>
    public bool HasError
    {
        get => _hasError;
        set => this.RaiseAndSetIfChanged(ref _hasError, value);
    }

    /// <summary>
    /// Comando para limpar erro
    /// </summary>
    public ReactiveCommand<Unit, Unit> ClearErrorCommand { get; }

    /// <summary>
    /// Construtor
    /// </summary>
    /// <param name="logger">Logger</param>
    protected BaseViewModel(ILogger logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        // Comando para limpar erro
        ClearErrorCommand = ReactiveCommand.Create(ClearError);

        // Observar mudanças no ErrorMessage para atualizar HasError
        this.WhenAnyValue(x => x.ErrorMessage)
            .Select(error => !string.IsNullOrWhiteSpace(error))
            .ToPropertyEx(this, x => x.HasError);
    }

    /// <summary>
    /// Limpa a mensagem de erro
    /// </summary>
    protected virtual void ClearError()
    {
        ErrorMessage = string.Empty;
        HasError = false;
    }

    /// <summary>
    /// Define uma mensagem de erro
    /// </summary>
    /// <param name="message">Mensagem de erro</param>
    protected virtual void SetError(string message)
    {
        ErrorMessage = message;
        HasError = true;
        _logger.LogWarning("Erro definido no ViewModel {ViewModelType}: {ErrorMessage}", 
            GetType().Name, message);
    }

    /// <summary>
    /// Define uma mensagem de erro a partir de uma exceção
    /// </summary>
    /// <param name="exception">Exceção</param>
    /// <param name="userMessage">Mensagem amigável para o usuário</param>
    protected virtual void SetError(Exception exception, string? userMessage = null)
    {
        var message = userMessage ?? "Ocorreu um erro inesperado. Tente novamente.";
        ErrorMessage = message;
        HasError = true;
        
        _logger.LogError(exception, "Erro no ViewModel {ViewModelType}: {ErrorMessage}", 
            GetType().Name, exception.Message);
    }

    /// <summary>
    /// Executa uma operação assíncrona com tratamento de erro e indicador de carregamento
    /// </summary>
    /// <param name="operation">Operação a ser executada</param>
    /// <param name="errorMessage">Mensagem de erro personalizada</param>
    /// <returns>Task</returns>
    protected async Task ExecuteAsync(Func<Task> operation, string? errorMessage = null)
    {
        if (IsBusy)
            return;

        try
        {
            IsBusy = true;
            ClearError();
            await operation();
        }
        catch (Exception ex)
        {
            SetError(ex, errorMessage);
        }
        finally
        {
            IsBusy = false;
        }
    }

    /// <summary>
    /// Executa uma operação assíncrona com retorno, tratamento de erro e indicador de carregamento
    /// </summary>
    /// <typeparam name="T">Tipo do retorno</typeparam>
    /// <param name="operation">Operação a ser executada</param>
    /// <param name="errorMessage">Mensagem de erro personalizada</param>
    /// <returns>Resultado da operação ou default(T) em caso de erro</returns>
    protected async Task<T?> ExecuteAsync<T>(Func<Task<T>> operation, string? errorMessage = null)
    {
        if (IsBusy)
            return default;

        try
        {
            IsBusy = true;
            ClearError();
            return await operation();
        }
        catch (Exception ex)
        {
            SetError(ex, errorMessage);
            return default;
        }
        finally
        {
            IsBusy = false;
        }
    }

    /// <summary>
    /// Cria um ReactiveCommand com tratamento de erro automático
    /// </summary>
    /// <param name="execute">Função a ser executada</param>
    /// <param name="canExecute">Observable que determina se o comando pode ser executado</param>
    /// <param name="errorMessage">Mensagem de erro personalizada</param>
    /// <returns>ReactiveCommand configurado</returns>
    protected ReactiveCommand<Unit, Unit> CreateCommand(
        Func<Task> execute,
        IObservable<bool>? canExecute = null,
        string? errorMessage = null)
    {
        canExecute ??= this.WhenAnyValue(x => x.IsBusy).Select(busy => !busy);

        return ReactiveCommand.CreateFromTask(async () =>
        {
            await ExecuteAsync(execute, errorMessage);
        }, canExecute);
    }

    /// <summary>
    /// Cria um ReactiveCommand com parâmetro e tratamento de erro automático
    /// </summary>
    /// <typeparam name="T">Tipo do parâmetro</typeparam>
    /// <param name="execute">Função a ser executada</param>
    /// <param name="canExecute">Observable que determina se o comando pode ser executado</param>
    /// <param name="errorMessage">Mensagem de erro personalizada</param>
    /// <returns>ReactiveCommand configurado</returns>
    protected ReactiveCommand<T, Unit> CreateCommand<T>(
        Func<T, Task> execute,
        IObservable<bool>? canExecute = null,
        string? errorMessage = null)
    {
        canExecute ??= this.WhenAnyValue(x => x.IsBusy).Select(busy => !busy);

        return ReactiveCommand.CreateFromTask<T>(async parameter =>
        {
            await ExecuteAsync(() => execute(parameter), errorMessage);
        }, canExecute);
    }

    /// <summary>
    /// Cria um ReactiveCommand com retorno e tratamento de erro automático
    /// </summary>
    /// <typeparam name="TResult">Tipo do retorno</typeparam>
    /// <param name="execute">Função a ser executada</param>
    /// <param name="canExecute">Observable que determina se o comando pode ser executado</param>
    /// <param name="errorMessage">Mensagem de erro personalizada</param>
    /// <returns>ReactiveCommand configurado</returns>
    protected ReactiveCommand<Unit, TResult> CreateCommand<TResult>(
        Func<Task<TResult>> execute,
        IObservable<bool>? canExecute = null,
        string? errorMessage = null)
    {
        canExecute ??= this.WhenAnyValue(x => x.IsBusy).Select(busy => !busy);

        return ReactiveCommand.CreateFromTask(async () =>
        {
            var result = await ExecuteAsync(execute, errorMessage);
            return result!;
        }, canExecute);
    }

    /// <summary>
    /// Valida uma propriedade
    /// </summary>
    /// <param name="value">Valor a ser validado</param>
    /// <param name="validators">Lista de validadores</param>
    /// <param name="propertyName">Nome da propriedade</param>
    /// <returns>True se válido</returns>
    protected bool ValidateProperty<T>(T value, IEnumerable<Func<T, string?>> validators, [CallerMemberName] string? propertyName = null)
    {
        var errors = validators
            .Select(validator => validator(value))
            .Where(error => !string.IsNullOrWhiteSpace(error))
            .ToList();

        if (errors.Any())
        {
            SetError($"Erro de validação em {propertyName}: {string.Join(", ", errors)}");
            return false;
        }

        return true;
    }

    /// <summary>
    /// Método virtual para inicialização assíncrona
    /// </summary>
    /// <returns>Task</returns>
    public virtual Task InitializeAsync()
    {
        return Task.CompletedTask;
    }

    /// <summary>
    /// Método virtual para limpeza de recursos
    /// </summary>
    public virtual void Cleanup()
    {
        // Implementação base vazia - pode ser sobrescrita
    }

    /// <summary>
    /// Método virtual chamado quando a view é ativada
    /// </summary>
    public virtual void OnActivated()
    {
        _logger.LogDebug("ViewModel {ViewModelType} ativado", GetType().Name);
    }

    /// <summary>
    /// Método virtual chamado quando a view é desativada
    /// </summary>
    public virtual void OnDeactivated()
    {
        _logger.LogDebug("ViewModel {ViewModelType} desativado", GetType().Name);
    }
}

/// <summary>
/// ViewModel base para listas com funcionalidades de paginação e busca
/// </summary>
/// <typeparam name="T">Tipo dos itens da lista</typeparam>
public abstract class BaseListViewModel<T> : BaseViewModel
{
    private ObservableCollection<T> _items = new();
    private T? _selectedItem;
    private string _searchText = string.Empty;
    private int _currentPage = 1;
    private int _pageSize = 20;
    private int _totalPages = 1;
    private int _totalItems = 0;
    private bool _hasNextPage;
    private bool _hasPreviousPage;

    /// <summary>
    /// Itens da lista
    /// </summary>
    public ObservableCollection<T> Items
    {
        get => _items;
        set => this.RaiseAndSetIfChanged(ref _items, value);
    }

    /// <summary>
    /// Item selecionado
    /// </summary>
    public T? SelectedItem
    {
        get => _selectedItem;
        set => this.RaiseAndSetIfChanged(ref _selectedItem, value);
    }

    /// <summary>
    /// Texto de busca
    /// </summary>
    public string SearchText
    {
        get => _searchText;
        set => this.RaiseAndSetIfChanged(ref _searchText, value);
    }

    /// <summary>
    /// Página atual
    /// </summary>
    public int CurrentPage
    {
        get => _currentPage;
        set => this.RaiseAndSetIfChanged(ref _currentPage, value);
    }

    /// <summary>
    /// Tamanho da página
    /// </summary>
    public int PageSize
    {
        get => _pageSize;
        set => this.RaiseAndSetIfChanged(ref _pageSize, value);
    }

    /// <summary>
    /// Total de páginas
    /// </summary>
    public int TotalPages
    {
        get => _totalPages;
        set => this.RaiseAndSetIfChanged(ref _totalPages, value);
    }

    /// <summary>
    /// Total de itens
    /// </summary>
    public int TotalItems
    {
        get => _totalItems;
        set => this.RaiseAndSetIfChanged(ref _totalItems, value);
    }

    /// <summary>
    /// Indica se há próxima página
    /// </summary>
    public bool HasNextPage
    {
        get => _hasNextPage;
        set => this.RaiseAndSetIfChanged(ref _hasNextPage, value);
    }

    /// <summary>
    /// Indica se há página anterior
    /// </summary>
    public bool HasPreviousPage
    {
        get => _hasPreviousPage;
        set => this.RaiseAndSetIfChanged(ref _hasPreviousPage, value);
    }

    /// <summary>
    /// Comando para buscar
    /// </summary>
    public ReactiveCommand<Unit, Unit> SearchCommand { get; }

    /// <summary>
    /// Comando para ir para próxima página
    /// </summary>
    public ReactiveCommand<Unit, Unit> NextPageCommand { get; }

    /// <summary>
    /// Comando para ir para página anterior
    /// </summary>
    public ReactiveCommand<Unit, Unit> PreviousPageCommand { get; }

    /// <summary>
    /// Comando para atualizar lista
    /// </summary>
    public ReactiveCommand<Unit, Unit> RefreshCommand { get; }

    /// <summary>
    /// Construtor
    /// </summary>
    /// <param name="logger">Logger</param>
    protected BaseListViewModel(ILogger logger) : base(logger)
    {
        // Comandos de paginação
        NextPageCommand = CreateCommand(NextPageAsync, this.WhenAnyValue(x => x.HasNextPage));
        PreviousPageCommand = CreateCommand(PreviousPageAsync, this.WhenAnyValue(x => x.HasPreviousPage));
        
        // Comando de busca
        SearchCommand = CreateCommand(SearchAsync);
        
        // Comando de atualização
        RefreshCommand = CreateCommand(LoadItemsAsync);

        // Busca automática quando o texto muda (com debounce)
        this.WhenAnyValue(x => x.SearchText)
            .Throttle(TimeSpan.FromMilliseconds(500))
            .ObserveOn(RxApp.MainThreadScheduler)
            .Subscribe(_ => SearchCommand.Execute().Subscribe());
    }

    /// <summary>
    /// Carrega os itens da lista
    /// </summary>
    /// <returns>Task</returns>
    protected abstract Task LoadItemsAsync();

    /// <summary>
    /// Vai para a próxima página
    /// </summary>
    /// <returns>Task</returns>
    protected virtual async Task NextPageAsync()
    {
        if (HasNextPage)
        {
            CurrentPage++;
            await LoadItemsAsync();
        }
    }

    /// <summary>
    /// Vai para a página anterior
    /// </summary>
    /// <returns>Task</returns>
    protected virtual async Task PreviousPageAsync()
    {
        if (HasPreviousPage)
        {
            CurrentPage--;
            await LoadItemsAsync();
        }
    }

    /// <summary>
    /// Executa busca
    /// </summary>
    /// <returns>Task</returns>
    protected virtual async Task SearchAsync()
    {
        CurrentPage = 1; // Reset para primeira página
        await LoadItemsAsync();
    }

    /// <summary>
    /// Atualiza informações de paginação
    /// </summary>
    /// <param name="totalItems">Total de itens</param>
    /// <param name="currentPage">Página atual</param>
    /// <param name="pageSize">Tamanho da página</param>
    protected void UpdatePagination(int totalItems, int currentPage, int pageSize)
    {
        TotalItems = totalItems;
        CurrentPage = currentPage;
        PageSize = pageSize;
        TotalPages = (int)Math.Ceiling((double)totalItems / pageSize);
        HasNextPage = currentPage < TotalPages;
        HasPreviousPage = currentPage > 1;
    }
}
