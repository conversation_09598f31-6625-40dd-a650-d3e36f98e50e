using AutoInstaller.Core.Common;
using AutoInstaller.Core.Interfaces;

namespace AutoInstaller.Core.Modules.{{ModuleName}}.Entities;

/// <summary>
/// Entidade {{EntityName}} - Representa {{EntityName}} no domínio do Auto-Instalador
/// </summary>
public class {{EntityName}} : BaseEntity, IAggregateRoot
{
    private readonly List<IDomainEvent> _domainEvents = new();
    
    // Propriedades principais
    public string Name { get; private set; } = string.Empty;
    public string Description { get; private set; } = string.Empty;
    public {{EntityName}}Status Status { get; private set; }
    
    // Propriedades de auditoria
    public DateTime CreatedAt { get; private set; }
    public DateTime? UpdatedAt { get; private set; }
    public string CreatedBy { get; private set; } = string.Empty;
    public string? UpdatedBy { get; private set; }
    
    // Construtor protegido para EF Core
    protected {{EntityName}}() { }
    
    // Construtor principal
    public {{EntityName}}(string name, string description, string createdBy)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Nome não pode ser vazio", nameof(name));
            
        if (string.IsNullOrWhiteSpace(createdBy))
            throw new ArgumentException("Criador não pode ser vazio", nameof(createdBy));
        
        Id = Guid.NewGuid();
        Name = name.Trim();
        Description = description?.Trim() ?? string.Empty;
        Status = {{EntityName}}Status.Created;
        CreatedAt = DateTime.UtcNow;
        CreatedBy = createdBy.Trim();
        
        // Adicionar evento de domínio
        AddDomainEvent(new {{EntityName}}CreatedEvent(this));
    }
    
    // Métodos de negócio
    public void UpdateName(string newName, string updatedBy)
    {
        if (string.IsNullOrWhiteSpace(newName))
            throw new ArgumentException("Nome não pode ser vazio", nameof(newName));
            
        if (string.IsNullOrWhiteSpace(updatedBy))
            throw new ArgumentException("Usuário que atualizou não pode ser vazio", nameof(updatedBy));
        
        var oldName = Name;
        Name = newName.Trim();
        UpdatedAt = DateTime.UtcNow;
        UpdatedBy = updatedBy.Trim();
        
        AddDomainEvent(new {{EntityName}}NameUpdatedEvent(this, oldName, newName));
    }
    
    public void UpdateDescription(string newDescription, string updatedBy)
    {
        if (string.IsNullOrWhiteSpace(updatedBy))
            throw new ArgumentException("Usuário que atualizou não pode ser vazio", nameof(updatedBy));
        
        var oldDescription = Description;
        Description = newDescription?.Trim() ?? string.Empty;
        UpdatedAt = DateTime.UtcNow;
        UpdatedBy = updatedBy.Trim();
        
        AddDomainEvent(new {{EntityName}}DescriptionUpdatedEvent(this, oldDescription, Description));
    }
    
    public void Activate(string activatedBy)
    {
        if (Status == {{EntityName}}Status.Active)
            throw new InvalidOperationException($"{{EntityName}} já está ativo");
            
        if (string.IsNullOrWhiteSpace(activatedBy))
            throw new ArgumentException("Usuário que ativou não pode ser vazio", nameof(activatedBy));
        
        Status = {{EntityName}}Status.Active;
        UpdatedAt = DateTime.UtcNow;
        UpdatedBy = activatedBy.Trim();
        
        AddDomainEvent(new {{EntityName}}ActivatedEvent(this));
    }
    
    public void Deactivate(string deactivatedBy)
    {
        if (Status == {{EntityName}}Status.Inactive)
            throw new InvalidOperationException($"{{EntityName}} já está inativo");
            
        if (string.IsNullOrWhiteSpace(deactivatedBy))
            throw new ArgumentException("Usuário que desativou não pode ser vazio", nameof(deactivatedBy));
        
        Status = {{EntityName}}Status.Inactive;
        UpdatedAt = DateTime.UtcNow;
        UpdatedBy = deactivatedBy.Trim();
        
        AddDomainEvent(new {{EntityName}}DeactivatedEvent(this));
    }
    
    public void Delete(string deletedBy)
    {
        if (Status == {{EntityName}}Status.Deleted)
            throw new InvalidOperationException($"{{EntityName}} já foi excluído");
            
        if (string.IsNullOrWhiteSpace(deletedBy))
            throw new ArgumentException("Usuário que excluiu não pode ser vazio", nameof(deletedBy));
        
        Status = {{EntityName}}Status.Deleted;
        UpdatedAt = DateTime.UtcNow;
        UpdatedBy = deletedBy.Trim();
        
        AddDomainEvent(new {{EntityName}}DeletedEvent(this));
    }
    
    // Métodos para eventos de domínio
    public IReadOnlyCollection<IDomainEvent> GetDomainEvents() => _domainEvents.AsReadOnly();
    
    public void ClearDomainEvents() => _domainEvents.Clear();
    
    private void AddDomainEvent(IDomainEvent domainEvent)
    {
        _domainEvents.Add(domainEvent);
    }
    
    // Métodos de validação
    public bool IsValid()
    {
        return !string.IsNullOrWhiteSpace(Name) &&
               !string.IsNullOrWhiteSpace(CreatedBy) &&
               CreatedAt != default &&
               Status != {{EntityName}}Status.Unknown;
    }
    
    public IEnumerable<string> GetValidationErrors()
    {
        var errors = new List<string>();
        
        if (string.IsNullOrWhiteSpace(Name))
            errors.Add("Nome é obrigatório");
            
        if (string.IsNullOrWhiteSpace(CreatedBy))
            errors.Add("Criador é obrigatório");
            
        if (CreatedAt == default)
            errors.Add("Data de criação é obrigatória");
            
        if (Status == {{EntityName}}Status.Unknown)
            errors.Add("Status deve ser definido");
        
        return errors;
    }
    
    // Override de métodos base
    public override bool Equals(object? obj)
    {
        if (obj is not {{EntityName}} other)
            return false;
            
        if (ReferenceEquals(this, other))
            return true;
            
        return Id.Equals(other.Id);
    }
    
    public override int GetHashCode()
    {
        return Id.GetHashCode();
    }
    
    public override string ToString()
    {
        return $"{{EntityName}} [Id: {Id}, Name: {Name}, Status: {Status}]";
    }
}

/// <summary>
/// Enum para status da entidade {{EntityName}}
/// </summary>
public enum {{EntityName}}Status
{
    Unknown = 0,
    Created = 1,
    Active = 2,
    Inactive = 3,
    Deleted = 4
}

// Eventos de domínio
public record {{EntityName}}CreatedEvent({{EntityName}} {{EntityName}}) : IDomainEvent;
public record {{EntityName}}NameUpdatedEvent({{EntityName}} {{EntityName}}, string OldName, string NewName) : IDomainEvent;
public record {{EntityName}}DescriptionUpdatedEvent({{EntityName}} {{EntityName}}, string OldDescription, string NewDescription) : IDomainEvent;
public record {{EntityName}}ActivatedEvent({{EntityName}} {{EntityName}}) : IDomainEvent;
public record {{EntityName}}DeactivatedEvent({{EntityName}} {{EntityName}}) : IDomainEvent;
public record {{EntityName}}DeletedEvent({{EntityName}} {{EntityName}}) : IDomainEvent;