namespace AutoInstaller.Core.Exceptions;

/// <summary>
/// Exceção base para violações de regras de domínio
/// </summary>
public class DomainException : Exception
{
    /// <summary>
    /// Código do erro
    /// </summary>
    public string ErrorCode { get; }

    /// <summary>
    /// Detalhes adicionais do erro
    /// </summary>
    public Dictionary<string, object> Details { get; }

    /// <summary>
    /// Construtor básico
    /// </summary>
    /// <param name="message">Mensagem do erro</param>
    public DomainException(string message) : base(message)
    {
        ErrorCode = "DOMAIN_ERROR";
        Details = new Dictionary<string, object>();
    }

    /// <summary>
    /// Construtor com código de erro
    /// </summary>
    /// <param name="message">Mensagem do erro</param>
    /// <param name="errorCode">Código do erro</param>
    public DomainException(string message, string errorCode) : base(message)
    {
        ErrorCode = errorCode;
        Details = new Dictionary<string, object>();
    }

    /// <summary>
    /// Construtor com exceção interna
    /// </summary>
    /// <param name="message">Mensagem do erro</param>
    /// <param name="innerException">Exceção interna</param>
    public DomainException(string message, Exception innerException) : base(message, innerException)
    {
        ErrorCode = "DOMAIN_ERROR";
        Details = new Dictionary<string, object>();
    }

    /// <summary>
    /// Construtor completo
    /// </summary>
    /// <param name="message">Mensagem do erro</param>
    /// <param name="errorCode">Código do erro</param>
    /// <param name="details">Detalhes adicionais</param>
    public DomainException(string message, string errorCode, Dictionary<string, object> details) : base(message)
    {
        ErrorCode = errorCode;
        Details = details ?? new Dictionary<string, object>();
    }

    /// <summary>
    /// Construtor completo com exceção interna
    /// </summary>
    /// <param name="message">Mensagem do erro</param>
    /// <param name="errorCode">Código do erro</param>
    /// <param name="details">Detalhes adicionais</param>
    /// <param name="innerException">Exceção interna</param>
    public DomainException(string message, string errorCode, Dictionary<string, object> details, Exception innerException) 
        : base(message, innerException)
    {
        ErrorCode = errorCode;
        Details = details ?? new Dictionary<string, object>();
    }

    /// <summary>
    /// Adiciona um detalhe ao erro
    /// </summary>
    /// <param name="key">Chave do detalhe</param>
    /// <param name="value">Valor do detalhe</param>
    public void AddDetail(string key, object value)
    {
        Details[key] = value;
    }

    /// <summary>
    /// Retorna uma representação string da exceção
    /// </summary>
    /// <returns>String representando a exceção</returns>
    public override string ToString()
    {
        var result = $"[{ErrorCode}] {Message}";
        
        if (Details.Any())
        {
            var detailsStr = string.Join(", ", Details.Select(d => $"{d.Key}: {d.Value}"));
            result += $" | Details: {detailsStr}";
        }

        if (InnerException != null)
        {
            result += $" | Inner: {InnerException.Message}";
        }

        return result;
    }
}

/// <summary>
/// Exceção para violações de regras de negócio
/// </summary>
public class BusinessRuleException : DomainException
{
    public BusinessRuleException(string message) : base(message, "BUSINESS_RULE_VIOLATION")
    {
    }

    public BusinessRuleException(string message, Dictionary<string, object> details) 
        : base(message, "BUSINESS_RULE_VIOLATION", details)
    {
    }
}

/// <summary>
/// Exceção para entidades não encontradas
/// </summary>
public class EntityNotFoundException : DomainException
{
    public EntityNotFoundException(string entityName, object id) 
        : base($"{entityName} with id '{id}' was not found", "ENTITY_NOT_FOUND")
    {
        AddDetail("EntityName", entityName);
        AddDetail("Id", id);
    }

    public EntityNotFoundException(string entityName, string propertyName, object propertyValue) 
        : base($"{entityName} with {propertyName} '{propertyValue}' was not found", "ENTITY_NOT_FOUND")
    {
        AddDetail("EntityName", entityName);
        AddDetail("PropertyName", propertyName);
        AddDetail("PropertyValue", propertyValue);
    }
}

/// <summary>
/// Exceção para conflitos de entidades
/// </summary>
public class EntityConflictException : DomainException
{
    public EntityConflictException(string message) : base(message, "ENTITY_CONFLICT")
    {
    }

    public EntityConflictException(string entityName, string reason) 
        : base($"Conflict with {entityName}: {reason}", "ENTITY_CONFLICT")
    {
        AddDetail("EntityName", entityName);
        AddDetail("Reason", reason);
    }
}

/// <summary>
/// Exceção para validações de Value Objects
/// </summary>
public class ValueObjectValidationException : DomainException
{
    public ValueObjectValidationException(string valueObjectName, string value, string reason) 
        : base($"Invalid {valueObjectName}: {reason}", "VALUE_OBJECT_VALIDATION")
    {
        AddDetail("ValueObjectName", valueObjectName);
        AddDetail("Value", value);
        AddDetail("Reason", reason);
    }
}
