namespace AutoInstaller.Core.Common;

/// <summary>
/// Classe base abstrata para Value Objects
/// </summary>
public abstract class ValueObject
{
    /// <summary>
    /// Retorna os componentes que definem a igualdade do Value Object
    /// </summary>
    /// <returns>Componentes para comparação</returns>
    protected abstract IEnumerable<object> GetEqualityComponents();

    /// <summary>
    /// Verifica se dois Value Objects são iguais
    /// </summary>
    /// <param name="obj">Objeto a ser comparado</param>
    /// <returns>True se forem iguais, false caso contrário</returns>
    public override bool Equals(object? obj)
    {
        if (obj == null || obj.GetType() != GetType())
            return false;

        var other = (ValueObject)obj;

        return GetEqualityComponents().SequenceEqual(other.GetEqualityComponents());
    }

    /// <summary>
    /// Retorna o hash code do Value Object
    /// </summary>
    /// <returns>Hash code</returns>
    public override int GetHashCode()
    {
        return GetEqualityComponents()
            .Select(x => x?.GetHashCode() ?? 0)
            .Aggregate((x, y) => x ^ y);
    }

    /// <summary>
    /// Operador de igualdade
    /// </summary>
    /// <param name="left">Value Object da esquerda</param>
    /// <param name="right">Value Object da direita</param>
    /// <returns>True se forem iguais</returns>
    public static bool operator ==(ValueObject? left, ValueObject? right)
    {
        return Equals(left, right);
    }

    /// <summary>
    /// Operador de desigualdade
    /// </summary>
    /// <param name="left">Value Object da esquerda</param>
    /// <param name="right">Value Object da direita</param>
    /// <returns>True se forem diferentes</returns>
    public static bool operator !=(ValueObject? left, ValueObject? right)
    {
        return !Equals(left, right);
    }

    /// <summary>
    /// Cria uma cópia do Value Object
    /// </summary>
    /// <returns>Nova instância com os mesmos valores</returns>
    protected T Copy<T>() where T : ValueObject
    {
        return (T)MemberwiseClone();
    }
}
