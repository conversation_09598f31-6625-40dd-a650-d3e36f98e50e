<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <RootNamespace>AutoInstaller.Infrastructure</RootNamespace>
    <AssemblyName>AutoInstaller.Infrastructure</AssemblyName>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\AutoInstaller.Core\AutoInstaller.Core.csproj" />
    <ProjectReference Include="..\AutoInstaller.Application\AutoInstaller.Application.csproj" />
    <ProjectReference Include="..\AutoInstaller.Shared\AutoInstaller.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <!-- Entity Framework -->
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.0" />

    <!-- Docker Integration -->
    <PackageReference Include="Docker.DotNet" Version="3.125.15" />
    <PackageReference Include="Docker.DotNet.X509" Version="3.125.15" />



    <!-- Microsoft Extensions -->
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks" Version="9.0.0" />

    <!-- Logging -->
    <PackageReference Include="Serilog" Version="4.1.0" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="8.0.4" />

    <!-- Utilitários -->
    <PackageReference Include="Polly" Version="8.4.2" />
    <PackageReference Include="Polly.Extensions.Http" Version="3.0.0" />
    <PackageReference Include="System.Text.Json" Version="9.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

</Project>
