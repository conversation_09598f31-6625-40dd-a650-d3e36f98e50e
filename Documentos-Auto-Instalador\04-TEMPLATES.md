# Templates de Desenvolvimento - Auto-Instalador Desktop

## Visão Geral
Este documento contém templates padronizados para acelerar o desenvolvimento de novos módulos, componentes UI, testes unitários e configurações de ambiente no projeto Auto-Instalador Desktop.

---

## 1. Estrutura de Templates

### 1.1 Organização dos Templates

```
Templates/
├── Modules/
│   ├── Domain/
│   │   ├── Entity.template.cs
│   │   ├── ValueObject.template.cs
│   │   ├── Repository.interface.template.cs
│   │   └── DomainService.template.cs
│   ├── Application/
│   │   ├── Command.template.cs
│   │   ├── Query.template.cs
│   │   ├── Handler.template.cs
│   │   ├── Validator.template.cs
│   │   └── Mapper.template.cs
│   ├── Infrastructure/
│   │   ├── Repository.template.cs
│   │   ├── ExternalService.template.cs
│   │   └── Configuration.template.cs
│   └── UI/
│       ├── ViewModel.template.cs
│       ├── View.template.axaml
│       └── UserControl.template.axaml
├── Components/
│   ├── Card.template.axaml
│   ├── Modal.template.axaml
│   ├── DataGrid.template.axaml
│   └── Form.template.axaml
├── Tests/
│   ├── UnitTest.template.cs
│   ├── IntegrationTest.template.cs
│   ├── ViewModelTest.template.cs
│   └── RepositoryTest.template.cs
└── Configuration/
    ├── appsettings.template.json
    ├── launchSettings.template.json
    └── Directory.Build.props.template
```

### 1.2 Convenções de Nomenclatura

- **Placeholders**: `{{ClassName}}`, `{{PropertyName}}`, `{{Namespace}}`
- **Sufixos**: `.template.cs`, `.template.axaml`, `.template.json`
- **Variáveis**: `$SAFE_PROJECT_NAME$`, `$ITEM_NAME$`, `$ROOT_NAMESPACE$`

---

## 2. Templates de Módulos

### 2.1 Estrutura Padrão de Módulo

Cada novo módulo deve seguir a estrutura:

```
{{ModuleName}}/
├── Domain/
│   ├── Entities/
│   │   └── {{EntityName}}.cs
│   ├── ValueObjects/
│   │   └── {{ValueObjectName}}.cs
│   ├── Interfaces/
│   │   └── I{{EntityName}}Repository.cs
│   └── Services/
│       └── {{EntityName}}DomainService.cs
├── Application/
│   ├── Commands/
│   │   ├── Create{{EntityName}}Command.cs
│   │   ├── Update{{EntityName}}Command.cs
│   │   └── Delete{{EntityName}}Command.cs
│   ├── Queries/
│   │   ├── Get{{EntityName}}Query.cs
│   │   └── Get{{EntityName}}ListQuery.cs
│   ├── Handlers/
│   │   ├── Create{{EntityName}}Handler.cs
│   │   └── Get{{EntityName}}Handler.cs
│   ├── DTOs/
│   │   └── {{EntityName}}Dto.cs
│   ├── Validators/
│   │   └── Create{{EntityName}}Validator.cs
│   └── Mappers/
│       └── {{EntityName}}Mapper.cs
├── Infrastructure/
│   ├── Repositories/
│   │   └── {{EntityName}}Repository.cs
│   └── Configurations/
│       └── {{EntityName}}Configuration.cs
└── UI/
    ├── ViewModels/
    │   └── {{EntityName}}ViewModel.cs
    └── Views/
        └── {{EntityName}}View.axaml
```

### 2.2 Script de Geração de Módulo

```powershell
# Scripts/New-Module.ps1
param(
    [Parameter(Mandatory=$true)]
    [string]$ModuleName,
    
    [Parameter(Mandatory=$true)]
    [string]$EntityName,
    
    [string]$RootNamespace = "AutoInstaller"
)

$ErrorActionPreference = "Stop"

Write-Host "Creating new module: $ModuleName" -ForegroundColor Green

# Definir caminhos
$templatesPath = "Documentos-Auto-Instalador/Templates"
$srcPath = "src"

# Criar estrutura de pastas
$modulePaths = @(
    "$srcPath/AutoInstaller.Core/Modules/$ModuleName",
    "$srcPath/AutoInstaller.Application/Modules/$ModuleName",
    "$srcPath/AutoInstaller.Infrastructure/Modules/$ModuleName",
    "$srcPath/AutoInstaller.UI/Modules/$ModuleName"
)

foreach ($path in $modulePaths) {
    if (!(Test-Path $path)) {
        New-Item -ItemType Directory -Path $path -Force | Out-Null
        Write-Host "Created directory: $path" -ForegroundColor Yellow
    }
}

# Função para processar templates
function Process-Template {
    param(
        [string]$TemplatePath,
        [string]$OutputPath,
        [hashtable]$Replacements
    )
    
    $content = Get-Content $TemplatePath -Raw
    
    foreach ($key in $Replacements.Keys) {
        $content = $content -replace "\{\{$key\}\}", $Replacements[$key]
    }
    
    Set-Content -Path $OutputPath -Value $content -Encoding UTF8
    Write-Host "Generated: $OutputPath" -ForegroundColor Cyan
}

# Definir substituições
$replacements = @{
    "ModuleName" = $ModuleName
    "EntityName" = $EntityName
    "RootNamespace" = $RootNamespace
    "LowerEntityName" = $EntityName.ToLower()
    "PluralEntityName" = "$($EntityName)s"
}

# Processar templates de Domain
$domainTemplates = @(
    @{ Template = "$templatesPath/Modules/Domain/Entity.template.cs"; Output = "$srcPath/AutoInstaller.Core/Modules/$ModuleName/Entities/$EntityName.cs" },
    @{ Template = "$templatesPath/Modules/Domain/Repository.interface.template.cs"; Output = "$srcPath/AutoInstaller.Core/Modules/$ModuleName/Interfaces/I$($EntityName)Repository.cs" }
)

foreach ($template in $domainTemplates) {
    if (Test-Path $template.Template) {
        Process-Template -TemplatePath $template.Template -OutputPath $template.Output -Replacements $replacements
    }
}

Write-Host "Module $ModuleName created successfully!" -ForegroundColor Green
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Review generated files" -ForegroundColor White
Write-Host "2. Implement business logic" -ForegroundColor White
Write-Host "3. Add to dependency injection" -ForegroundColor White
Write-Host "4. Create tests" -ForegroundColor White
```

---

## 3. Templates de Componentes UI

### 3.1 Template de Card Genérico

Arquivo: `Templates/Components/Card.template.axaml`

```xml
<UserControl x:Class="AutoInstaller.UI.Controls.{{ComponentName}}Card"
             xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d" d:DesignWidth="300" d:DesignHeight="200">
    
    <Design.DataContext>
        <{{ComponentName}}CardViewModel />
    </Design.DataContext>
    
    <Border Classes="content-card" 
            Background="{DynamicResource SecondaryBackgroundBrush}"
            BorderBrush="{DynamicResource BorderBrush}"
            BorderThickness="1"
            CornerRadius="8"
            Padding="16">
        
        <Grid RowDefinitions="Auto,*,Auto">
            
            <!-- Header -->
            <Grid Grid.Row="0" ColumnDefinitions="*,Auto" Margin="0,0,0,12">
                <TextBlock Grid.Column="0"
                          Text="{Binding Title}"
                          FontSize="16"
                          FontWeight="Medium"
                          Foreground="{DynamicResource PrimaryTextBrush}" />
                          
                <Button Grid.Column="1"
                       Classes="icon-button"
                       Command="{Binding OptionsCommand}"
                       IsVisible="{Binding HasOptions}">
                    <PathIcon Data="{StaticResource MoreHorizontalIcon}" />
                </Button>
            </Grid>
            
            <!-- Content -->
            <ContentControl Grid.Row="1"
                           Content="{Binding Content}"
                           ContentTemplate="{Binding ContentTemplate}" />
            
            <!-- Footer -->
            <StackPanel Grid.Row="2" 
                       Orientation="Horizontal" 
                       HorizontalAlignment="Right"
                       Spacing="8"
                       Margin="0,12,0,0"
                       IsVisible="{Binding HasActions}">
                       
                <Button Content="{Binding SecondaryActionText}"
                       Command="{Binding SecondaryActionCommand}"
                       Classes="secondary"
                       IsVisible="{Binding HasSecondaryAction}" />
                       
                <Button Content="{Binding PrimaryActionText}"
                       Command="{Binding PrimaryActionCommand}"
                       Classes="primary"
                       IsVisible="{Binding HasPrimaryAction}" />
            </StackPanel>
            
        </Grid>
    </Border>
</UserControl>
```

### 3.2 Template de Modal Genérico

Arquivo: `Templates/Components/Modal.template.axaml`

```xml
<UserControl x:Class="AutoInstaller.UI.Modals.{{ModalName}}Modal"
             xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d" d:DesignWidth="600" d:DesignHeight="400">
    
    <Design.DataContext>
        <{{ModalName}}ModalViewModel />
    </Design.DataContext>
    
    <!-- Modal Overlay -->
    <Border Background="{DynamicResource ModalOverlayBrush}"
            IsVisible="{Binding IsVisible}"
            ZIndex="1000">
        
        <Border Classes="modal-container"
               Width="{Binding ModalWidth, FallbackValue=600}"
               Height="{Binding ModalHeight, FallbackValue=400}"
               MaxWidth="90vw"
               MaxHeight="90vh"
               HorizontalAlignment="Center"
               VerticalAlignment="Center"
               Background="{DynamicResource SecondaryBackgroundBrush}"
               BorderBrush="{DynamicResource BorderBrush}"
               BorderThickness="1"
               CornerRadius="8"
               BoxShadow="0 8 32 rgba(0,0,0,0.3)">
            
            <Grid RowDefinitions="Auto,*,Auto">
                
                <!-- Header -->
                <Border Grid.Row="0" 
                       Classes="modal-header"
                       Background="{DynamicResource TertiaryBackgroundBrush}"
                       BorderBrush="{DynamicResource BorderBrush}"
                       BorderThickness="0,0,0,1"
                       Padding="20,16">
                    
                    <Grid ColumnDefinitions="*,Auto">
                        <TextBlock Grid.Column="0"
                                  Text="{Binding Title}"
                                  FontSize="18"
                                  FontWeight="Medium"
                                  Foreground="{DynamicResource PrimaryTextBrush}"
                                  VerticalAlignment="Center" />
                                  
                        <Button Grid.Column="1"
                               Classes="icon-button close-button"
                               Command="{Binding CloseCommand}"
                               ToolTip.Tip="Fechar">
                            <PathIcon Data="{StaticResource CloseIcon}" />
                        </Button>
                    </Grid>
                </Border>
                
                <!-- Content -->
                <ScrollViewer Grid.Row="1" 
                             Classes="modal-content"
                             Padding="20">
                    <ContentControl Content="{Binding Content}"
                                   ContentTemplate="{Binding ContentTemplate}" />
                </ScrollViewer>
                
                <!-- Footer -->
                <Border Grid.Row="2" 
                       Classes="modal-footer"
                       Background="{DynamicResource TertiaryBackgroundBrush}"
                       BorderBrush="{DynamicResource BorderBrush}"
                       BorderThickness="0,1,0,0"
                       Padding="20,16">
                    
                    <StackPanel Orientation="Horizontal" 
                               HorizontalAlignment="Right" 
                               Spacing="12">
                               
                        <Button Content="{Binding CancelButtonText, FallbackValue=Cancelar}"
                               Command="{Binding CancelCommand}"
                               Classes="secondary"
                               IsVisible="{Binding ShowCancelButton}" />
                               
                        <Button Content="{Binding ConfirmButtonText, FallbackValue=Confirmar}"
                               Command="{Binding ConfirmCommand}"
                               Classes="primary"
                               IsEnabled="{Binding CanConfirm}"
                               IsVisible="{Binding ShowConfirmButton}" />
                    </StackPanel>
                </Border>
                
            </Grid>
        </Border>
    </Border>
</UserControl>
```

### 3.3 Template de ViewModel para Componentes

Arquivo: `Templates/Components/ComponentViewModel.template.cs`

```csharp
using ReactiveUI;
using System.Reactive;
using System.Reactive.Disposables;
using AutoInstaller.UI.ViewModels.Base;
using MediatR;
using Microsoft.Extensions.Logging;

namespace AutoInstaller.UI.ViewModels.Components;

public class {{ComponentName}}ViewModel : ViewModelBase
{
    private string _title = string.Empty;
    private bool _isLoading;
    private bool _isVisible = true;
    private string? _errorMessage;
    
    public string Title
    {
        get => _title;
        set => this.RaiseAndSetIfChanged(ref _title, value);
    }
    
    public bool IsLoading
    {
        get => _isLoading;
        set => this.RaiseAndSetIfChanged(ref _isLoading, value);
    }
    
    public bool IsVisible
    {
        get => _isVisible;
        set => this.RaiseAndSetIfChanged(ref _isVisible, value);
    }
    
    public string? ErrorMessage
    {
        get => _errorMessage;
        set => this.RaiseAndSetIfChanged(ref _errorMessage, value);
    }
    
    // Commands
    public ReactiveCommand<Unit, Unit> LoadCommand { get; }
    public ReactiveCommand<Unit, Unit> RefreshCommand { get; }
    public ReactiveCommand<Unit, Unit> CloseCommand { get; }
    
    public {{ComponentName}}ViewModel(IMediator mediator, ILogger<{{ComponentName}}ViewModel> logger) 
        : base(mediator, logger)
    {
        // Initialize commands
        LoadCommand = ReactiveCommand.CreateFromTask(LoadAsync);
        RefreshCommand = ReactiveCommand.CreateFromTask(RefreshAsync);
        CloseCommand = ReactiveCommand.Create(Close);
        
        // Setup reactive subscriptions
        this.WhenActivated(disposables =>
        {
            // Subscribe to command execution
            LoadCommand.IsExecuting
                .Subscribe(isExecuting => IsLoading = isExecuting)
                .DisposeWith(disposables);
                
            RefreshCommand.IsExecuting
                .Subscribe(isExecuting => IsLoading = isExecuting)
                .DisposeWith(disposables);
            
            // Handle command errors
            LoadCommand.ThrownExceptions
                .Subscribe(HandleError)
                .DisposeWith(disposables);
                
            RefreshCommand.ThrownExceptions
                .Subscribe(HandleError)
                .DisposeWith(disposables);
        });
    }
    
    protected override void OnActivated()
    {
        base.OnActivated();
        
        // Auto-load when activated
        if (LoadCommand.CanExecute.FirstAsync().Wait())
        {
            LoadCommand.Execute().Subscribe();
        }
    }
    
    private async Task LoadAsync()
    {
        try
        {
            Logger.LogDebug("Loading {{ComponentName}} data");
            
            // TODO: Implement loading logic
            await Task.Delay(1000); // Simulate async operation
            
            Logger.LogDebug("{{ComponentName}} data loaded successfully");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading {{ComponentName}} data");
            throw;
        }
    }
    
    private async Task RefreshAsync()
    {
        try
        {
            Logger.LogDebug("Refreshing {{ComponentName}} data");
            
            // TODO: Implement refresh logic
            await LoadAsync();
            
            Logger.LogDebug("{{ComponentName}} data refreshed successfully");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error refreshing {{ComponentName}} data");
            throw;
        }
    }
    
    private void Close()
    {
        Logger.LogDebug("Closing {{ComponentName}}");
        IsVisible = false;
    }
    
    private void HandleError(Exception exception)
    {
        Logger.LogError(exception, "{{ComponentName}} error: {ErrorMessage}", exception.Message);
        ErrorMessage = exception.Message;
    }
}
```

---

## 4. Templates de Testes

### 4.1 Template de Teste Unitário

Arquivo: `Templates/Tests/UnitTest.template.cs`

```csharp
using Xunit;
using FluentAssertions;
using Moq;
using Microsoft.Extensions.Logging;
using AutoInstaller.{{Layer}}.{{Module}};
using AutoInstaller.Core.Interfaces;

namespace AutoInstaller.{{Layer}}.Tests.{{Module}};

public class {{ClassName}}Tests
{
    private readonly Mock<{{IDependency}}> _{{dependencyName}}Mock;
    private readonly Mock<ILogger<{{ClassName}}>> _loggerMock;
    private readonly {{ClassName}} _sut;
    
    public {{ClassName}}Tests()
    {
        _{{dependencyName}}Mock = new Mock<{{IDependency}}>();
        _loggerMock = new Mock<ILogger<{{ClassName}}>>();
        _sut = new {{ClassName}}(_{{dependencyName}}Mock.Object, _loggerMock.Object);
    }
    
    [Fact]
    public void Constructor_WithValidParameters_ShouldCreateInstance()
    {
        // Arrange & Act
        var instance = new {{ClassName}}(_{{dependencyName}}Mock.Object, _loggerMock.Object);
        
        // Assert
        instance.Should().NotBeNull();
    }
    
    [Fact]
    public async Task {{MethodName}}_WithValidInput_ShouldReturnExpectedResult()
    {
        // Arrange
        var input = CreateValidInput();
        var expectedResult = CreateExpectedResult();
        
        _{{dependencyName}}Mock
            .Setup(x => x.{{DependencyMethod}}(It.IsAny<{{InputType}}>()))
            .ReturnsAsync(expectedResult);
        
        // Act
        var result = await _sut.{{MethodName}}(input);
        
        // Assert
        result.Should().NotBeNull();
        result.Should().BeEquivalentTo(expectedResult);
        
        _{{dependencyName}}Mock.Verify(
            x => x.{{DependencyMethod}}(It.Is<{{InputType}}>(i => i.Equals(input))), 
            Times.Once);
    }
    
    [Fact]
    public async Task {{MethodName}}_WithInvalidInput_ShouldThrowException()
    {
        // Arrange
        var invalidInput = CreateInvalidInput();
        
        // Act & Assert
        await _sut.Invoking(s => s.{{MethodName}}(invalidInput))
            .Should().ThrowAsync<{{ExceptionType}}>()
            .WithMessage("*{{ExpectedErrorMessage}}*");
    }
    
    [Fact]
    public async Task {{MethodName}}_WhenDependencyThrows_ShouldPropagateException()
    {
        // Arrange
        var input = CreateValidInput();
        var expectedException = new {{ExceptionType}}("Dependency error");
        
        _{{dependencyName}}Mock
            .Setup(x => x.{{DependencyMethod}}(It.IsAny<{{InputType}}>()))
            .ThrowsAsync(expectedException);
        
        // Act & Assert
        await _sut.Invoking(s => s.{{MethodName}}(input))
            .Should().ThrowAsync<{{ExceptionType}}>()
            .WithMessage(expectedException.Message);
    }
    
    [Theory]
    [InlineData({{TestData1}})]
    [InlineData({{TestData2}})]
    [InlineData({{TestData3}})]
    public async Task {{MethodName}}_WithVariousInputs_ShouldHandleCorrectly({{ParameterType}} parameter)
    {
        // Arrange
        var input = CreateInputWithParameter(parameter);
        
        // Act
        var result = await _sut.{{MethodName}}(input);
        
        // Assert
        result.Should().NotBeNull();
        // Add specific assertions based on parameter
    }
    
    private {{InputType}} CreateValidInput()
    {
        return new {{InputType}}
        {
            // TODO: Initialize with valid test data
        };
    }
    
    private {{InputType}} CreateInvalidInput()
    {
        return new {{InputType}}
        {
            // TODO: Initialize with invalid test data
        };
    }
    
    private {{ResultType}} CreateExpectedResult()
    {
        return new {{ResultType}}
        {
            // TODO: Initialize with expected result data
        };
    }
    
    private {{InputType}} CreateInputWithParameter({{ParameterType}} parameter)
    {
        return new {{InputType}}
        {
            // TODO: Initialize with parameter-specific data
        };
    }
}
```

### 4.2 Template de Teste de Integração

Arquivo: `Templates/Tests/IntegrationTest.template.cs`

```csharp
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using Xunit;
using FluentAssertions;
using AutoInstaller.Infrastructure.Data;
using AutoInstaller.{{Layer}}.{{Module}};

namespace AutoInstaller.{{Layer}}.Tests.Integration.{{Module}};

public class {{ClassName}}IntegrationTests : IClassFixture<{{TestFixture}}>, IDisposable
{
    private readonly {{TestFixture}} _fixture;
    private readonly IServiceScope _scope;
    private readonly {{ClassName}} _sut;
    private readonly ApplicationDbContext _dbContext;
    
    public {{ClassName}}IntegrationTests({{TestFixture}} fixture)
    {
        _fixture = fixture;
        _scope = _fixture.ServiceProvider.CreateScope();
        _sut = _scope.ServiceProvider.GetRequiredService<{{ClassName}}>();
        _dbContext = _scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
    }
    
    [Fact]
    public async Task {{MethodName}}_WithRealDatabase_ShouldPersistData()
    {
        // Arrange
        var input = CreateValidInput();
        
        // Act
        var result = await _sut.{{MethodName}}(input);
        
        // Assert
        result.Should().NotBeNull();
        
        // Verify data was persisted
        var persistedEntity = await _dbContext.{{EntitySet}}
            .FirstOrDefaultAsync(e => e.Id == result.Id);
            
        persistedEntity.Should().NotBeNull();
        persistedEntity.Should().BeEquivalentTo(result, options => 
            options.Excluding(e => e.CreatedAt));
    }
    
    [Fact]
    public async Task {{MethodName}}_WithExternalDependency_ShouldIntegrateCorrectly()
    {
        // Arrange
        var input = CreateValidInput();
        
        // Act
        var result = await _sut.{{MethodName}}(input);
        
        // Assert
        result.Should().NotBeNull();
        
        // Verify external service was called correctly
        // This might involve checking logs, database state, or external service state
    }
    
    [Fact]
    public async Task {{MethodName}}_WithConcurrentRequests_ShouldHandleCorrectly()
    {
        // Arrange
        var inputs = Enumerable.Range(1, 10)
            .Select(i => CreateValidInputWithId(i))
            .ToList();
        
        // Act
        var tasks = inputs.Select(input => _sut.{{MethodName}}(input));
        var results = await Task.WhenAll(tasks);
        
        // Assert
        results.Should().HaveCount(10);
        results.Should().OnlyContain(r => r != null);
        
        // Verify all data was persisted correctly
        var persistedCount = await _dbContext.{{EntitySet}}.CountAsync();
        persistedCount.Should().BeGreaterOrEqualTo(10);
    }
    
    private {{InputType}} CreateValidInput()
    {
        return new {{InputType}}
        {
            // TODO: Initialize with valid test data
        };
    }
    
    private {{InputType}} CreateValidInputWithId(int id)
    {
        return new {{InputType}}
        {
            Id = id,
            // TODO: Initialize with valid test data
        };
    }
    
    public void Dispose()
    {
        _scope?.Dispose();
    }
}

public class {{TestFixture}} : IDisposable
{
    public IServiceProvider ServiceProvider { get; }
    
    public {{TestFixture}}()
    {
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string>
            {
                ["ConnectionStrings:DefaultConnection"] = "Data Source=:memory:"
            })
            .Build();
        
        var services = new ServiceCollection();
        
        // Configure test services
        services.AddDbContext<ApplicationDbContext>(options =>
            options.UseSqlite("Data Source=:memory:"));
            
        // Add application services
        services.AddScoped<{{ClassName}}>();
        
        // TODO: Add other required services
        
        ServiceProvider = services.BuildServiceProvider();
        
        // Initialize database
        using var scope = ServiceProvider.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        dbContext.Database.OpenConnection();
        dbContext.Database.EnsureCreated();
    }
    
    public void Dispose()
    {
        if (ServiceProvider is IDisposable disposable)
        {
            disposable.Dispose();
        }
    }
}
```

### 4.3 Template de Teste de ViewModel

Arquivo: `Templates/Tests/ViewModelTest.template.cs`

```csharp
using Xunit;
using FluentAssertions;
using Moq;
using ReactiveUI.Testing;
using Microsoft.Extensions.Logging;
using MediatR;
using AutoInstaller.UI.ViewModels;
using AutoInstaller.Application.Commands;
using AutoInstaller.Application.Queries;
using AutoInstaller.Application.DTOs;

namespace AutoInstaller.UI.Tests.ViewModels;

public class {{ViewModelName}}Tests
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly Mock<ILogger<{{ViewModelName}}>> _loggerMock;
    private readonly {{ViewModelName}} _sut;
    
    public {{ViewModelName}}Tests()
    {
        _mediatorMock = new Mock<IMediator>();
        _loggerMock = new Mock<ILogger<{{ViewModelName}}>>();
        _sut = new {{ViewModelName}}(_mediatorMock.Object, _loggerMock.Object);
    }
    
    [Fact]
    public void Constructor_ShouldInitializeProperties()
    {
        // Assert
        _sut.Should().NotBeNull();
        _sut.Title.Should().NotBeNullOrEmpty();
        _sut.IsLoading.Should().BeFalse();
        _sut.ErrorMessage.Should().BeNull();
    }
    
    [Fact]
    public void LoadCommand_ShouldBeExecutable()
    {
        // Assert
        _sut.LoadCommand.Should().NotBeNull();
        _sut.LoadCommand.CanExecute(null).Should().BeTrue();
    }
    
    [Fact]
    public void LoadCommand_WhenExecuted_ShouldSetLoadingState()
    {
        // Arrange
        var testData = CreateTestData();
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<{{QueryName}}>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(testData);
        
        // Act & Assert
        new TestScheduler().With(scheduler =>
        {
            _sut.LoadCommand.Execute().Subscribe();
            
            // Should set loading to true initially
            _sut.IsLoading.Should().BeTrue();
            
            scheduler.AdvanceToEnd();
            
            // Should set loading to false after completion
            _sut.IsLoading.Should().BeFalse();
            _sut.ErrorMessage.Should().BeNull();
        });
    }
    
    [Fact]
    public void LoadCommand_WhenSuccessful_ShouldUpdateData()
    {
        // Arrange
        var testData = CreateTestData();
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<{{QueryName}}>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(testData);
        
        // Act
        new TestScheduler().With(scheduler =>
        {
            _sut.LoadCommand.Execute().Subscribe();
            scheduler.AdvanceToEnd();
            
            // Assert
            _sut.{{DataProperty}}.Should().NotBeNull();
            _sut.{{DataProperty}}.Should().BeEquivalentTo(testData);
            _sut.ErrorMessage.Should().BeNull();
        });
    }
    
    [Fact]
    public void LoadCommand_WhenFails_ShouldSetErrorMessage()
    {
        // Arrange
        var expectedException = new Exception("Test error");
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<{{QueryName}}>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(expectedException);
        
        // Act
        new TestScheduler().With(scheduler =>
        {
            _sut.LoadCommand.Execute().Subscribe();
            scheduler.AdvanceToEnd();
            
            // Assert
            _sut.IsLoading.Should().BeFalse();
            _sut.ErrorMessage.Should().Be(expectedException.Message);
        });
    }
    
    [Fact]
    public void {{ActionCommand}}_WithValidInput_ShouldExecuteSuccessfully()
    {
        // Arrange
        var input = CreateValidInput();
        var expectedResult = CreateExpectedResult();
        
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<{{CommandName}}>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);
        
        // Act
        new TestScheduler().With(scheduler =>
        {
            _sut.{{ActionCommand}}.Execute(input).Subscribe();
            scheduler.AdvanceToEnd();
            
            // Assert
            _mediatorMock.Verify(
                m => m.Send(It.Is<{{CommandName}}>(c => 
                    c.{{Property}} == input.{{Property}}), 
                    It.IsAny<CancellationToken>()), 
                Times.Once);
        });
    }
    
    [Fact]
    public void PropertyChanged_ShouldRaiseNotification()
    {
        // Arrange
        var propertyChangedRaised = false;
        _sut.PropertyChanged += (sender, args) =>
        {
            if (args.PropertyName == nameof(_sut.{{PropertyName}}))
                propertyChangedRaised = true;
        };
        
        // Act
        _sut.{{PropertyName}} = "New Value";
        
        // Assert
        propertyChangedRaised.Should().BeTrue();
    }
    
    private {{DataType}} CreateTestData()
    {
        return new {{DataType}}
        {
            // TODO: Initialize with test data
        };
    }
    
    private {{InputType}} CreateValidInput()
    {
        return new {{InputType}}
        {
            // TODO: Initialize with valid input data
        };
    }
    
    private {{ResultType}} CreateExpectedResult()
    {
        return new {{ResultType}}
        {
            // TODO: Initialize with expected result data
        };
    }
}
```

---

## 5. Templates de Configuração

### 5.1 Template de appsettings

Arquivo: `Templates/Configuration/appsettings.template.json`

```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information",
      "AutoInstaller": "Debug"
    },
    "Console": {
      "IncludeScopes": true,
      "TimestampFormat": "yyyy-MM-dd HH:mm:ss "
    },
    "File": {
      "Path": "logs/autoinstaller-{Date}.log",
      "MinLevel": "Information",
      "RollingInterval": "Day",
      "RetainedFileCountLimit": 30
    }
  },
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=autoinstaller.db"
  },
  "Docker": {
    "Endpoint": "npipe://./pipe/docker_engine",
    "Timeout": "00:05:00",
    "EnableTLS": false,
    "CertificatePath": "",
    "DefaultRegistry": "docker.io"
  },
  "Podman": {
    "Endpoint": "npipe://./pipe/podman-machine-default",
    "Timeout": "00:05:00",
    "MachineProfile": "default",
    "UseRootless": true,
    "DefaultRegistry": "docker.io"
  },
  "Application": {
    "Name": "{{ApplicationName}}",
    "Version": "1.0.0",
    "Environment": "{{Environment}}",
    "DataDirectory": "{{DataDirectory}}",
    "TempDirectory": "{{TempDirectory}}",
    "BackupDirectory": "{{BackupDirectory}}",
    "MaxConcurrentOperations": 5,
    "DefaultTimeout": "00:02:00"
  },
  "UI": {
    "Theme": "Dark",
    "Language": "pt-BR",
    "AutoRefreshInterval": "00:00:30",
    "ShowNotifications": true,
    "MinimizeToTray": false,
    "StartMinimized": false,
    "WindowState": {
      "Width": 1200,
      "Height": 800,
      "IsMaximized": false
    }
  },
  "Security": {
    "EncryptionKey": "{{EncryptionKey}}",
    "AllowedRegistries": [
      "docker.io",
      "ghcr.io",
      "quay.io"
    ],
    "RequireImageSignatureVerification": false,
    "MaxImageSizeGB": 10
  },
  "Performance": {
    "EnableCaching": true,
    "CacheExpirationMinutes": 30,
    "MaxMemoryUsageMB": 512,
    "EnableMetrics": true,
    "MetricsInterval": "00:01:00"
  }
}
```

### 5.2 Template de launchSettings

Arquivo: `Templates/Configuration/launchSettings.template.json`

```json
{
  "profiles": {
    "{{ProjectName}}": {
      "commandName": "Project",
      "environmentVariables": {
        "DOTNET_ENVIRONMENT": "Development",
        "ASPNETCORE_ENVIRONMENT": "Development"
      },
      "workingDirectory": ".",
      "applicationUrl": ""
    },
    "{{ProjectName}} (Production)": {
      "commandName": "Project",
      "environmentVariables": {
        "DOTNET_ENVIRONMENT": "Production",
        "ASPNETCORE_ENVIRONMENT": "Production"
      },
      "workingDirectory": ".",
      "applicationUrl": ""
    },
    "{{ProjectName}} (Staging)": {
      "commandName": "Project",
      "environmentVariables": {
        "DOTNET_ENVIRONMENT": "Staging",
        "ASPNETCORE_ENVIRONMENT": "Staging"
      },
      "workingDirectory": ".",
      "applicationUrl": ""
    }
  }
}
```

---

## 6. Scripts de Automação

### 6.1 Script de Criação de Componente UI

Arquivo: `Scripts/New-UIComponent.ps1`

```powershell
param(
    [Parameter(Mandatory=$true)]
    [string]$ComponentName,
    
    [Parameter(Mandatory=$false)]
    [ValidateSet("UserControl", "Window", "Modal", "Card")]
    [string]$ComponentType = "UserControl",
    
    [Parameter(Mandatory=$false)]
    [string]$Namespace = "AutoInstaller.UI.Controls"
)

$ErrorActionPreference = "Stop"

Write-Host "Creating new UI component: $ComponentName" -ForegroundColor Green

# Definir caminhos
$templatesPath = "Documentos-Auto-Instalador/Templates/Components"
$uiPath = "src/AutoInstaller.UI"

# Determinar pasta de destino baseada no tipo
$destinationFolder = switch ($ComponentType) {
    "Modal" { "Modals" }
    "Card" { "Controls" }
    "Window" { "Views" }
    default { "Controls" }
}

$destinationPath = "$uiPath/$destinationFolder"

# Criar pasta se não existir
if (!(Test-Path $destinationPath)) {
    New-Item -ItemType Directory -Path $destinationPath -Force | Out-Null
}

# Definir substituições
$replacements = @{
    "ComponentName" = $ComponentName
    "ComponentType" = $ComponentType
    "Namespace" = $Namespace
    "FullNamespace" = "$Namespace.$destinationFolder"
}

# Função para processar template
function Process-Template {
    param(
        [string]$TemplatePath,
        [string]$OutputPath,
        [hashtable]$Replacements
    )
    
    if (!(Test-Path $TemplatePath)) {
        Write-Warning "Template not found: $TemplatePath"
        return
    }
    
    $content = Get-Content $TemplatePath -Raw
    
    foreach ($key in $Replacements.Keys) {
        $content = $content -replace "\{\{$key\}\}", $Replacements[$key]
    }
    
    Set-Content -Path $OutputPath -Value $content -Encoding UTF8
    Write-Host "Generated: $OutputPath" -ForegroundColor Cyan
}

# Gerar arquivos
$templateFile = "$templatesPath/$ComponentType.template.axaml"
$outputFile = "$destinationPath/$ComponentName.axaml"

Process-Template -TemplatePath $templateFile -OutputPath $outputFile -Replacements $replacements

# Gerar ViewModel se necessário
if ($ComponentType -ne "UserControl") {
    $viewModelTemplate = "$templatesPath/ComponentViewModel.template.cs"
    $viewModelOutput = "$uiPath/ViewModels/$($ComponentName)ViewModel.cs"
    
    if (!(Test-Path "$uiPath/ViewModels")) {
        New-Item -ItemType Directory -Path "$uiPath/ViewModels" -Force | Out-Null
    }
    
    Process-Template -TemplatePath $viewModelTemplate -OutputPath $viewModelOutput -Replacements $replacements
}

Write-Host "Component $ComponentName created successfully!" -ForegroundColor Green
Write-Host "Files created:" -ForegroundColor Yellow
Write-Host "- $outputFile" -ForegroundColor White
if ($ComponentType -ne "UserControl") {
    Write-Host "- $viewModelOutput" -ForegroundColor White
}
```

### 6.2 Script de Criação de Teste

Arquivo: `Scripts/New-Test.ps1`

```powershell
param(
    [Parameter(Mandatory=$true)]
    [string]$ClassName,
    
    [Parameter(Mandatory=$true)]
    [ValidateSet("Unit", "Integration", "ViewModel")]
    [string]$TestType,
    
    [Parameter(Mandatory=$true)]
    [ValidateSet("Core", "Application", "Infrastructure", "UI")]
    [string]$Layer,
    
    [Parameter(Mandatory=$false)]
    [string]$Module = ""
)

$ErrorActionPreference = "Stop"

Write-Host "Creating new $TestType test for $ClassName" -ForegroundColor Green

# Definir caminhos
$templatesPath = "Documentos-Auto-Instalador/Templates/Tests"
$testsPath = "tests/AutoInstaller.$Layer.Tests"

if ($Module) {
    $testsPath += "/$Module"
}

# Criar pasta se não existir
if (!(Test-Path $testsPath)) {
    New-Item -ItemType Directory -Path $testsPath -Force | Out-Null
}

# Definir substituições
$replacements = @{
    "ClassName" = $ClassName
    "Layer" = $Layer
    "Module" = $Module
    "TestType" = $TestType
    "Namespace" = "AutoInstaller.$Layer.Tests"
}

if ($Module) {
    $replacements["Namespace"] += ".$Module"
}

# Função para processar template
function Process-Template {
    param(
        [string]$TemplatePath,
        [string]$OutputPath,
        [hashtable]$Replacements
    )
    
    if (!(Test-Path $TemplatePath)) {
        Write-Warning "Template not found: $TemplatePath"
        return
    }
    
    $content = Get-Content $TemplatePath -Raw
    
    foreach ($key in $Replacements.Keys) {
        $content = $content -replace "\{\{$key\}\}", $Replacements[$key]
    }
    
    Set-Content -Path $OutputPath -Value $content -Encoding UTF8
    Write-Host "Generated: $OutputPath" -ForegroundColor Cyan
}

# Gerar teste
$templateFile = "$templatesPath/$($TestType)Test.template.cs"
$outputFile = "$testsPath/$($ClassName)Tests.cs"

Process-Template -TemplatePath $templateFile -OutputPath $outputFile -Replacements $replacements

Write-Host "Test $($ClassName)Tests created successfully!" -ForegroundColor Green
Write-Host "File created: $outputFile" -ForegroundColor White
```

---

## 7. Documentação de Uso

### 7.1 Como Usar os Templates

1. **Criação de Módulo**:
   ```powershell
   .\Scripts\New-Module.ps1 -ModuleName "Backup" -EntityName "BackupJob"
   ```

2. **Criação de Componente UI**:
   ```powershell
   .\Scripts\New-UIComponent.ps1 -ComponentName "ContainerCard" -ComponentType "Card"
   ```

3. **Criação de Teste**:
   ```powershell
   .\Scripts\New-Test.ps1 -ClassName "ContainerService" -TestType "Unit" -Layer "Application"
   ```

### 7.2 Customização de Templates

1. **Modificar Templates Existentes**: Edite os arquivos `.template.*` na pasta `Templates/`
2. **Adicionar Novos Placeholders**: Use a sintaxe `{{PlaceholderName}}`
3. **Criar Novos Templates**: Siga a estrutura de pastas existente
4. **Atualizar Scripts**: Modifique os scripts PowerShell para suportar novos templates

### 7.3 Melhores Práticas

1. **Sempre revisar código gerado** antes de usar em produção
2. **Personalizar templates** conforme necessidades específicas do projeto
3. **Manter templates atualizados** com mudanças na arquitetura
4. **Documentar customizações** para facilitar manutenção
5. **Testar templates** regularmente para garantir funcionamento

---

*Documento gerado em: Janeiro 2025*
*Versão: 1.0*
*Autor: Arquiteto de Software - Auto-Instalador*