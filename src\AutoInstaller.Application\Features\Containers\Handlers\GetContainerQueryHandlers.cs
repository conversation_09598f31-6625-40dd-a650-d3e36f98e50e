using AutoInstaller.Application.Common;
using AutoInstaller.Application.DTOs;
using AutoInstaller.Application.Features.Containers.Queries;
using AutoInstaller.Core.Entities;
using AutoInstaller.Core.Interfaces;
using AutoInstaller.Core.ValueObjects;
using AutoMapper;
using Microsoft.Extensions.Logging;

namespace AutoInstaller.Application.Features.Containers.Handlers;

/// <summary>
/// Handler para GetContainerByIdQuery
/// </summary>
public class GetContainerByIdQueryHandler : IQueryHandler<GetContainerByIdQuery, ContainerDto?>
{
    private readonly IContainerRepository _containerRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetContainerByIdQueryHandler> _logger;

    /// <summary>
    /// Construtor
    /// </summary>
    /// <param name="containerRepository">Repositório de containers</param>
    /// <param name="mapper">Mapper</param>
    /// <param name="logger">Logger</param>
    public GetContainerByIdQueryHandler(
        IContainerRepository containerRepository,
        IMapper mapper,
        ILogger<GetContainerByIdQueryHandler> logger)
    {
        _containerRepository = containerRepository ?? throw new ArgumentNullException(nameof(containerRepository));
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Executa a query
    /// </summary>
    /// <param name="request">Query</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>DTO do container ou null</returns>
    public async Task<ContainerDto?> Handle(GetContainerByIdQuery request, CancellationToken cancellationToken)
    {
        _logger.LogDebug("Buscando container por ID {ContainerId}", request.Id);

        try
        {
            var container = await _containerRepository.GetByIdAsync(request.Id, cancellationToken);
            
            if (container == null)
            {
                _logger.LogDebug("Container {ContainerId} não encontrado", request.Id);
                return null;
            }

            var containerDto = _mapper.Map<ContainerDto>(container);
            
            _logger.LogDebug("Container {ContainerId} encontrado com sucesso", request.Id);
            return containerDto;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar container por ID {ContainerId}", request.Id);
            throw;
        }
    }
}

/// <summary>
/// Handler para GetContainerByNameQuery
/// </summary>
public class GetContainerByNameQueryHandler : IQueryHandler<GetContainerByNameQuery, ContainerDto?>
{
    private readonly IContainerRepository _containerRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetContainerByNameQueryHandler> _logger;

    /// <summary>
    /// Construtor
    /// </summary>
    /// <param name="containerRepository">Repositório de containers</param>
    /// <param name="mapper">Mapper</param>
    /// <param name="logger">Logger</param>
    public GetContainerByNameQueryHandler(
        IContainerRepository containerRepository,
        IMapper mapper,
        ILogger<GetContainerByNameQueryHandler> logger)
    {
        _containerRepository = containerRepository ?? throw new ArgumentNullException(nameof(containerRepository));
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Executa a query
    /// </summary>
    /// <param name="request">Query</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>DTO do container ou null</returns>
    public async Task<ContainerDto?> Handle(GetContainerByNameQuery request, CancellationToken cancellationToken)
    {
        _logger.LogDebug("Buscando container por nome {ContainerName}", request.Name);

        try
        {
            if (string.IsNullOrWhiteSpace(request.Name))
            {
                _logger.LogDebug("Nome do container não fornecido");
                return null;
            }

            var containerName = ContainerName.Create(request.Name);
            var container = await _containerRepository.GetByNameAsync(containerName, cancellationToken);
            
            if (container == null)
            {
                _logger.LogDebug("Container {ContainerName} não encontrado", request.Name);
                return null;
            }

            var containerDto = _mapper.Map<ContainerDto>(container);
            
            _logger.LogDebug("Container {ContainerName} encontrado com sucesso", request.Name);
            return containerDto;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar container por nome {ContainerName}", request.Name);
            throw;
        }
    }
}

/// <summary>
/// Handler para GetContainersByStatusQuery
/// </summary>
public class GetContainersByStatusQueryHandler : IQueryHandler<GetContainersByStatusQuery, List<ContainerSummaryDto>>
{
    private readonly IContainerRepository _containerRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetContainersByStatusQueryHandler> _logger;

    /// <summary>
    /// Construtor
    /// </summary>
    /// <param name="containerRepository">Repositório de containers</param>
    /// <param name="mapper">Mapper</param>
    /// <param name="logger">Logger</param>
    public GetContainersByStatusQueryHandler(
        IContainerRepository containerRepository,
        IMapper mapper,
        ILogger<GetContainersByStatusQueryHandler> logger)
    {
        _containerRepository = containerRepository ?? throw new ArgumentNullException(nameof(containerRepository));
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Executa a query
    /// </summary>
    /// <param name="request">Query</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de containers</returns>
    public async Task<List<ContainerSummaryDto>> Handle(GetContainersByStatusQuery request, CancellationToken cancellationToken)
    {
        _logger.LogDebug("Buscando containers por status {Status}", request.Status);

        try
        {
            var containers = await _containerRepository.GetByStatusAsync(request.Status, cancellationToken);
            var containerDtos = _mapper.Map<List<ContainerSummaryDto>>(containers);
            
            _logger.LogDebug("Encontrados {Count} containers com status {Status}", containerDtos.Count, request.Status);
            return containerDtos;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar containers por status {Status}", request.Status);
            throw;
        }
    }
}

/// <summary>
/// Handler para GetContainersByRuntimeQuery
/// </summary>
public class GetContainersByRuntimeQueryHandler : IQueryHandler<GetContainersByRuntimeQuery, List<ContainerSummaryDto>>
{
    private readonly IContainerRepository _containerRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetContainersByRuntimeQueryHandler> _logger;

    /// <summary>
    /// Construtor
    /// </summary>
    /// <param name="containerRepository">Repositório de containers</param>
    /// <param name="mapper">Mapper</param>
    /// <param name="logger">Logger</param>
    public GetContainersByRuntimeQueryHandler(
        IContainerRepository containerRepository,
        IMapper mapper,
        ILogger<GetContainersByRuntimeQueryHandler> logger)
    {
        _containerRepository = containerRepository ?? throw new ArgumentNullException(nameof(containerRepository));
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Executa a query
    /// </summary>
    /// <param name="request">Query</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de containers</returns>
    public async Task<List<ContainerSummaryDto>> Handle(GetContainersByRuntimeQuery request, CancellationToken cancellationToken)
    {
        _logger.LogDebug("Buscando containers por runtime {Runtime}", request.Runtime);

        try
        {
            var containers = await _containerRepository.GetByRuntimeAsync(request.Runtime, cancellationToken);
            var containerDtos = _mapper.Map<List<ContainerSummaryDto>>(containers);
            
            _logger.LogDebug("Encontrados {Count} containers com runtime {Runtime}", containerDtos.Count, request.Runtime);
            return containerDtos;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar containers por runtime {Runtime}", request.Runtime);
            throw;
        }
    }
}

/// <summary>
/// Handler para GetContainersPagedQuery
/// </summary>
public class GetContainersPagedQueryHandler : IQueryHandler<GetContainersPagedQuery, PagedContainerResultDto>
{
    private readonly IContainerRepository _containerRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetContainersPagedQueryHandler> _logger;

    /// <summary>
    /// Construtor
    /// </summary>
    /// <param name="containerRepository">Repositório de containers</param>
    /// <param name="mapper">Mapper</param>
    /// <param name="logger">Logger</param>
    public GetContainersPagedQueryHandler(
        IContainerRepository containerRepository,
        IMapper mapper,
        ILogger<GetContainersPagedQueryHandler> logger)
    {
        _containerRepository = containerRepository ?? throw new ArgumentNullException(nameof(containerRepository));
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Executa a query
    /// </summary>
    /// <param name="request">Query</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Resultado paginado</returns>
    public async Task<PagedContainerResultDto> Handle(GetContainersPagedQuery request, CancellationToken cancellationToken)
    {
        _logger.LogDebug("Buscando containers paginados - Página {PageNumber}, Tamanho {PageSize}", 
            request.PageNumber, request.PageSize);

        try
        {
            var pagedResult = await _containerRepository.GetPagedAsync(
                request.PageNumber, 
                request.PageSize, 
                cancellationToken);

            var resultDto = new PagedContainerResultDto
            {
                Items = _mapper.Map<List<ContainerSummaryDto>>(pagedResult.Items),
                PageNumber = pagedResult.PageNumber,
                PageSize = pagedResult.PageSize,
                TotalCount = pagedResult.TotalCount,
                TotalPages = pagedResult.TotalPages,
                HasPreviousPage = pagedResult.HasPreviousPage,
                HasNextPage = pagedResult.HasNextPage
            };

            _logger.LogDebug("Encontrados {TotalCount} containers, retornando página {PageNumber} com {ItemCount} itens", 
                resultDto.TotalCount, resultDto.PageNumber, resultDto.Items.Count);

            return resultDto;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar containers paginados");
            throw;
        }
    }
}

/// <summary>
/// Handler para SearchContainersQuery
/// </summary>
public class SearchContainersQueryHandler : IQueryHandler<SearchContainersQuery, PagedContainerResultDto>
{
    private readonly IContainerRepository _containerRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<SearchContainersQueryHandler> _logger;

    /// <summary>
    /// Construtor
    /// </summary>
    /// <param name="containerRepository">Repositório de containers</param>
    /// <param name="mapper">Mapper</param>
    /// <param name="logger">Logger</param>
    public SearchContainersQueryHandler(
        IContainerRepository containerRepository,
        IMapper mapper,
        ILogger<SearchContainersQueryHandler> logger)
    {
        _containerRepository = containerRepository ?? throw new ArgumentNullException(nameof(containerRepository));
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Executa a query
    /// </summary>
    /// <param name="request">Query</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Resultado paginado da busca</returns>
    public async Task<PagedContainerResultDto> Handle(SearchContainersQuery request, CancellationToken cancellationToken)
    {
        _logger.LogDebug("Buscando containers com texto '{SearchText}' - Página {PageNumber}, Tamanho {PageSize}", 
            request.SearchText, request.PageNumber, request.PageSize);

        try
        {
            var pagedResult = await _containerRepository.SearchPagedAsync(
                request.SearchText,
                request.PageNumber, 
                request.PageSize, 
                cancellationToken);

            var resultDto = new PagedContainerResultDto
            {
                Items = _mapper.Map<List<ContainerSummaryDto>>(pagedResult.Items),
                PageNumber = pagedResult.PageNumber,
                PageSize = pagedResult.PageSize,
                TotalCount = pagedResult.TotalCount,
                TotalPages = pagedResult.TotalPages,
                HasPreviousPage = pagedResult.HasPreviousPage,
                HasNextPage = pagedResult.HasNextPage
            };

            _logger.LogDebug("Busca por '{SearchText}' retornou {TotalCount} containers, página {PageNumber} com {ItemCount} itens", 
                request.SearchText, resultDto.TotalCount, resultDto.PageNumber, resultDto.Items.Count);

            return resultDto;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar containers com texto '{SearchText}'", request.SearchText);
            throw;
        }
    }
}

/// <summary>
/// Handler para GetContainerStatisticsQuery
/// </summary>
public class GetContainerStatisticsQueryHandler : IQueryHandler<GetContainerStatisticsQuery, ContainerStatisticsDto>
{
    private readonly IContainerRepository _containerRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetContainerStatisticsQueryHandler> _logger;

    /// <summary>
    /// Construtor
    /// </summary>
    /// <param name="containerRepository">Repositório de containers</param>
    /// <param name="mapper">Mapper</param>
    /// <param name="logger">Logger</param>
    public GetContainerStatisticsQueryHandler(
        IContainerRepository containerRepository,
        IMapper mapper,
        ILogger<GetContainerStatisticsQueryHandler> logger)
    {
        _containerRepository = containerRepository ?? throw new ArgumentNullException(nameof(containerRepository));
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Executa a query
    /// </summary>
    /// <param name="request">Query</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Estatísticas dos containers</returns>
    public async Task<ContainerStatisticsDto> Handle(GetContainerStatisticsQuery request, CancellationToken cancellationToken)
    {
        _logger.LogDebug("Obtendo estatísticas de containers");

        try
        {
            var statistics = await _containerRepository.GetStatisticsAsync(cancellationToken);
            var statisticsDto = _mapper.Map<ContainerStatisticsDto>(statistics);
            
            _logger.LogDebug("Estatísticas obtidas: {TotalContainers} containers totais, {RunningContainers} em execução", 
                statisticsDto.TotalContainers, statisticsDto.RunningContainers);

            return statisticsDto;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter estatísticas de containers");
            throw;
        }
    }
}

/// <summary>
/// Handler para GetRunningContainersQuery
/// </summary>
public class GetRunningContainersQueryHandler : IQueryHandler<GetRunningContainersQuery, List<ContainerSummaryDto>>
{
    private readonly IContainerRepository _containerRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetRunningContainersQueryHandler> _logger;

    /// <summary>
    /// Construtor
    /// </summary>
    /// <param name="containerRepository">Repositório de containers</param>
    /// <param name="mapper">Mapper</param>
    /// <param name="logger">Logger</param>
    public GetRunningContainersQueryHandler(
        IContainerRepository containerRepository,
        IMapper mapper,
        ILogger<GetRunningContainersQueryHandler> logger)
    {
        _containerRepository = containerRepository ?? throw new ArgumentNullException(nameof(containerRepository));
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Executa a query
    /// </summary>
    /// <param name="request">Query</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de containers em execução</returns>
    public async Task<List<ContainerSummaryDto>> Handle(GetRunningContainersQuery request, CancellationToken cancellationToken)
    {
        _logger.LogDebug("Buscando containers em execução");

        try
        {
            var containers = await _containerRepository.GetRunningContainersAsync(cancellationToken);
            var containerDtos = _mapper.Map<List<ContainerSummaryDto>>(containers);
            
            _logger.LogDebug("Encontrados {Count} containers em execução", containerDtos.Count);
            return containerDtos;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar containers em execução");
            throw;
        }
    }
}

/// <summary>
/// Handler para GetStoppedContainersQuery
/// </summary>
public class GetStoppedContainersQueryHandler : IQueryHandler<GetStoppedContainersQuery, List<ContainerSummaryDto>>
{
    private readonly IContainerRepository _containerRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetStoppedContainersQueryHandler> _logger;

    /// <summary>
    /// Construtor
    /// </summary>
    /// <param name="containerRepository">Repositório de containers</param>
    /// <param name="mapper">Mapper</param>
    /// <param name="logger">Logger</param>
    public GetStoppedContainersQueryHandler(
        IContainerRepository containerRepository,
        IMapper mapper,
        ILogger<GetStoppedContainersQueryHandler> logger)
    {
        _containerRepository = containerRepository ?? throw new ArgumentNullException(nameof(containerRepository));
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Executa a query
    /// </summary>
    /// <param name="request">Query</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de containers parados</returns>
    public async Task<List<ContainerSummaryDto>> Handle(GetStoppedContainersQuery request, CancellationToken cancellationToken)
    {
        _logger.LogDebug("Buscando containers parados");

        try
        {
            var containers = await _containerRepository.GetStoppedContainersAsync(cancellationToken);
            var containerDtos = _mapper.Map<List<ContainerSummaryDto>>(containers);
            
            _logger.LogDebug("Encontrados {Count} containers parados", containerDtos.Count);
            return containerDtos;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar containers parados");
            throw;
        }
    }
}
