using {{RootNamespace}}.Core.Common;
using {{RootNamespace}}.Core.Exceptions;

namespace {{RootNamespace}}.Core.Modules.{{ModuleName}}.ValueObjects;

/// <summary>
/// Value Object para {{ValueObjectName}}
/// </summary>
public sealed class {{ValueObjectName}} : ValueObject
{
    public string Value { get; private set; }

    private {{ValueObjectName}}(string value)
    {
        Value = value;
    }

    /// <summary>
    /// Cria uma nova instância de {{ValueObjectName}}
    /// </summary>
    /// <param name="value">Valor do {{ValueObjectName}}</param>
    /// <returns>Nova instância de {{ValueObjectName}}</returns>
    /// <exception cref="DomainException">Quando o valor é inválido</exception>
    public static {{ValueObjectName}} Create(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            throw new DomainException("{{ValueObjectName}} não pode ser vazio");

        if (value.Length > 100)
            throw new DomainException("{{ValueObjectName}} não pode ter mais de 100 caracteres");

        // Adicione validações específicas aqui
        if (!IsValid(value))
            throw new DomainException($"{{ValueObjectName}} '{value}' é inválido");

        return new {{ValueObjectName}}(value);
    }

    /// <summary>
    /// Valida se o valor é válido para {{ValueObjectName}}
    /// </summary>
    /// <param name="value">Valor a ser validado</param>
    /// <returns>True se válido, false caso contrário</returns>
    private static bool IsValid(string value)
    {
        // Implemente a lógica de validação específica aqui
        // Exemplo: regex, regras de negócio, etc.
        return true;
    }

    /// <summary>
    /// Retorna os componentes para comparação de igualdade
    /// </summary>
    /// <returns>Componentes do Value Object</returns>
    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Value;
    }

    /// <summary>
    /// Conversão implícita para string
    /// </summary>
    /// <param name="{{LowerValueObjectName}}">Instância de {{ValueObjectName}}</param>
    public static implicit operator string({{ValueObjectName}} {{LowerValueObjectName}})
    {
        return {{LowerValueObjectName}}.Value;
    }

    /// <summary>
    /// Representação em string do Value Object
    /// </summary>
    /// <returns>Valor como string</returns>
    public override string ToString()
    {
        return Value;
    }
}