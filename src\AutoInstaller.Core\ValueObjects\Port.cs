using AutoInstaller.Core.Common;
using AutoInstaller.Core.Exceptions;

namespace AutoInstaller.Core.ValueObjects;

/// <summary>
/// Protocolo da porta
/// </summary>
public enum PortProtocol
{
    TCP,
    UDP
}

/// <summary>
/// Value Object para porta de rede
/// </summary>
public sealed class Port : ValueObject
{
    private const int MinPortNumber = 1;
    private const int MaxPortNumber = 65535;
    private const int MinPrivilegedPort = 1;
    private const int MaxPrivilegedPort = 1023;
    private const int MinEphemeralPort = 32768;
    private const int MaxEphemeralPort = 65535;

    /// <summary>
    /// Número da porta
    /// </summary>
    public int Number { get; private set; }

    /// <summary>
    /// Protocolo da porta
    /// </summary>
    public PortProtocol Protocol { get; private set; }

    /// <summary>
    /// Indica se é uma porta privilegiada (1-1023)
    /// </summary>
    public bool IsPrivileged => Number >= MinPrivilegedPort && Number <= MaxPrivilegedPort;

    /// <summary>
    /// Indica se é uma porta efêmera (32768-65535)
    /// </summary>
    public bool IsEphemeral => Number >= MinEphemeralPort && Number <= MaxEphemeralPort;

    /// <summary>
    /// Indica se é uma porta registrada (1024-49151)
    /// </summary>
    public bool IsRegistered => Number >= 1024 && Number <= 49151;

    /// <summary>
    /// Indica se é uma porta dinâmica/privada (49152-65535)
    /// </summary>
    public bool IsDynamic => Number >= 49152 && Number <= MaxPortNumber;

    /// <summary>
    /// Construtor privado
    /// </summary>
    /// <param name="number">Número da porta</param>
    /// <param name="protocol">Protocolo da porta</param>
    private Port(int number, PortProtocol protocol)
    {
        Number = number;
        Protocol = protocol;
    }

    /// <summary>
    /// Cria uma nova porta TCP
    /// </summary>
    /// <param name="number">Número da porta</param>
    /// <returns>Port TCP válida</returns>
    public static Port CreateTcp(int number)
    {
        return Create(number, PortProtocol.TCP);
    }

    /// <summary>
    /// Cria uma nova porta UDP
    /// </summary>
    /// <param name="number">Número da porta</param>
    /// <returns>Port UDP válida</returns>
    public static Port CreateUdp(int number)
    {
        return Create(number, PortProtocol.UDP);
    }

    /// <summary>
    /// Cria uma nova porta
    /// </summary>
    /// <param name="number">Número da porta</param>
    /// <param name="protocol">Protocolo da porta</param>
    /// <returns>Port válida</returns>
    /// <exception cref="ValueObjectValidationException">Quando a porta é inválida</exception>
    public static Port Create(int number, PortProtocol protocol)
    {
        if (number < MinPortNumber || number > MaxPortNumber)
            throw new ValueObjectValidationException(nameof(Port), number.ToString(), 
                $"Número da porta deve estar entre {MinPortNumber} e {MaxPortNumber}");

        return new Port(number, protocol);
    }

    /// <summary>
    /// Cria uma porta a partir de string no formato "número/protocolo"
    /// </summary>
    /// <param name="portString">String no formato "80/tcp" ou "53/udp"</param>
    /// <returns>Port válida</returns>
    /// <exception cref="ValueObjectValidationException">Quando o formato é inválido</exception>
    public static Port Parse(string portString)
    {
        if (string.IsNullOrWhiteSpace(portString))
            throw new ValueObjectValidationException(nameof(Port), portString ?? "null", 
                "String da porta não pode ser vazia");

        var parts = portString.Trim().Split('/');
        if (parts.Length != 2)
            throw new ValueObjectValidationException(nameof(Port), portString, 
                "Formato deve ser 'número/protocolo' (ex: 80/tcp)");

        if (!int.TryParse(parts[0], out var number))
            throw new ValueObjectValidationException(nameof(Port), portString, 
                "Número da porta deve ser um inteiro válido");

        if (!Enum.TryParse<PortProtocol>(parts[1], true, out var protocol))
            throw new ValueObjectValidationException(nameof(Port), portString, 
                "Protocolo deve ser 'tcp' ou 'udp'");

        return Create(number, protocol);
    }

    /// <summary>
    /// Tenta criar uma porta
    /// </summary>
    /// <param name="number">Número da porta</param>
    /// <param name="protocol">Protocolo da porta</param>
    /// <param name="port">Port criada</param>
    /// <returns>True se válida</returns>
    public static bool TryCreate(int number, PortProtocol protocol, out Port? port)
    {
        try
        {
            port = Create(number, protocol);
            return true;
        }
        catch (ValueObjectValidationException)
        {
            port = null;
            return false;
        }
    }

    /// <summary>
    /// Tenta fazer parse de uma string
    /// </summary>
    /// <param name="portString">String da porta</param>
    /// <param name="port">Port criada</param>
    /// <returns>True se válida</returns>
    public static bool TryParse(string portString, out Port? port)
    {
        try
        {
            port = Parse(portString);
            return true;
        }
        catch (ValueObjectValidationException)
        {
            port = null;
            return false;
        }
    }

    /// <summary>
    /// Verifica se uma porta é válida
    /// </summary>
    /// <param name="number">Número da porta</param>
    /// <returns>True se válida</returns>
    public static bool IsValid(int number)
    {
        return number >= MinPortNumber && number <= MaxPortNumber;
    }

    /// <summary>
    /// Portas comuns pré-definidas
    /// </summary>
    public static class Common
    {
        public static readonly Port Http = CreateTcp(80);
        public static readonly Port Https = CreateTcp(443);
        public static readonly Port Ssh = CreateTcp(22);
        public static readonly Port Ftp = CreateTcp(21);
        public static readonly Port Smtp = CreateTcp(25);
        public static readonly Port Pop3 = CreateTcp(110);
        public static readonly Port Imap = CreateTcp(143);
        public static readonly Port Dns = CreateUdp(53);
        public static readonly Port Dhcp = CreateUdp(67);
        public static readonly Port Ntp = CreateUdp(123);
        public static readonly Port Mysql = CreateTcp(3306);
        public static readonly Port PostgreSql = CreateTcp(5432);
        public static readonly Port Redis = CreateTcp(6379);
        public static readonly Port MongoDB = CreateTcp(27017);
    }

    /// <summary>
    /// Retorna os componentes para comparação
    /// </summary>
    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Number;
        yield return Protocol;
    }

    /// <summary>
    /// Representação string no formato "número/protocolo"
    /// </summary>
    public override string ToString()
    {
        return $"{Number}/{Protocol.ToString().ToLowerInvariant()}";
    }

    /// <summary>
    /// Representação string apenas com o número
    /// </summary>
    public string ToNumberString()
    {
        return Number.ToString();
    }
}
