using AutoInstaller.Application.Common;
using AutoInstaller.Application.DTOs;
using AutoInstaller.Core.Entities;

namespace AutoInstaller.Application.Features.Containers.Commands;

/// <summary>
/// Command para criar um novo container
/// </summary>
public class CreateContainerCommand : BaseCommand<ContainerDto>
{
    /// <summary>
    /// Nome do container
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Nome da imagem
    /// </summary>
    public string ImageName { get; set; } = string.Empty;

    /// <summary>
    /// Tag da imagem
    /// </summary>
    public string ImageTag { get; set; } = "latest";

    /// <summary>
    /// Runtime a ser utilizado
    /// </summary>
    public ContainerRuntime Runtime { get; set; } = ContainerRuntime.Docker;

    /// <summary>
    /// Comando a ser executado
    /// </summary>
    public string? Command { get; set; }

    /// <summary>
    /// Argumentos do comando
    /// </summary>
    public string[]? Arguments { get; set; }

    /// <summary>
    /// Diretório de trabalho
    /// </summary>
    public string? WorkingDirectory { get; set; }

    /// <summary>
    /// Usuário de execução
    /// </summary>
    public string? User { get; set; }

    /// <summary>
    /// Auto restart
    /// </summary>
    public bool AutoRestart { get; set; }

    /// <summary>
    /// Política de reinicialização
    /// </summary>
    public string RestartPolicy { get; set; } = "no";

    /// <summary>
    /// Limite de memória em bytes
    /// </summary>
    public long? MemoryLimit { get; set; }

    /// <summary>
    /// Limite de CPU
    /// </summary>
    public double? CpuLimit { get; set; }

    /// <summary>
    /// Portas a serem expostas
    /// </summary>
    public List<CreatePortDto> ExposedPorts { get; set; } = new();

    /// <summary>
    /// Variáveis de ambiente
    /// </summary>
    public Dictionary<string, string> EnvironmentVariables { get; set; } = new();

    /// <summary>
    /// Labels
    /// </summary>
    public Dictionary<string, string> Labels { get; set; } = new();

    /// <summary>
    /// Volumes
    /// </summary>
    public List<string> Volumes { get; set; } = new();

    /// <summary>
    /// Construtor
    /// </summary>
    public CreateContainerCommand()
    {
    }

    /// <summary>
    /// Construtor com usuário executor
    /// </summary>
    /// <param name="executedBy">Usuário que executa o comando</param>
    public CreateContainerCommand(string executedBy) : base(executedBy)
    {
    }

    /// <summary>
    /// Cria o command a partir de um DTO
    /// </summary>
    /// <param name="dto">DTO de criação</param>
    /// <param name="executedBy">Usuário executor</param>
    /// <returns>Command configurado</returns>
    public static CreateContainerCommand FromDto(CreateContainerDto dto, string executedBy)
    {
        if (dto == null)
            throw new ArgumentNullException(nameof(dto));

        var command = new CreateContainerCommand(executedBy)
        {
            Name = dto.Name,
            ImageName = dto.ImageName,
            ImageTag = dto.ImageTag,
            Command = dto.Command,
            Arguments = dto.Arguments,
            WorkingDirectory = dto.WorkingDirectory,
            User = dto.User,
            AutoRestart = dto.AutoRestart,
            RestartPolicy = dto.RestartPolicy,
            MemoryLimit = dto.MemoryLimit,
            CpuLimit = dto.CpuLimit,
            ExposedPorts = dto.ExposedPorts,
            EnvironmentVariables = dto.EnvironmentVariables,
            Labels = dto.Labels,
            Volumes = dto.Volumes
        };

        // Parse do runtime
        if (Enum.TryParse<ContainerRuntime>(dto.Runtime, true, out var runtime))
        {
            command.Runtime = runtime;
        }

        return command;
    }
}

/// <summary>
/// Command para atualizar um container
/// </summary>
public class UpdateContainerCommand : BaseCommand<ContainerDto>
{
    /// <summary>
    /// ID do container
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Comando a ser executado
    /// </summary>
    public string? Command { get; set; }

    /// <summary>
    /// Argumentos do comando
    /// </summary>
    public string[]? Arguments { get; set; }

    /// <summary>
    /// Diretório de trabalho
    /// </summary>
    public string? WorkingDirectory { get; set; }

    /// <summary>
    /// Usuário de execução
    /// </summary>
    public string? User { get; set; }

    /// <summary>
    /// Auto restart
    /// </summary>
    public bool? AutoRestart { get; set; }

    /// <summary>
    /// Política de reinicialização
    /// </summary>
    public string? RestartPolicy { get; set; }

    /// <summary>
    /// Limite de memória em bytes
    /// </summary>
    public long? MemoryLimit { get; set; }

    /// <summary>
    /// Limite de CPU
    /// </summary>
    public double? CpuLimit { get; set; }

    /// <summary>
    /// Construtor
    /// </summary>
    public UpdateContainerCommand()
    {
    }

    /// <summary>
    /// Construtor com ID e usuário executor
    /// </summary>
    /// <param name="id">ID do container</param>
    /// <param name="executedBy">Usuário que executa o comando</param>
    public UpdateContainerCommand(Guid id, string executedBy) : base(executedBy)
    {
        Id = id;
    }
}

/// <summary>
/// Command para deletar um container
/// </summary>
public class DeleteContainerCommand : BaseCommand
{
    /// <summary>
    /// ID do container
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Forçar remoção mesmo se em execução
    /// </summary>
    public bool Force { get; set; }

    /// <summary>
    /// Remover volumes associados
    /// </summary>
    public bool RemoveVolumes { get; set; }

    /// <summary>
    /// Construtor
    /// </summary>
    public DeleteContainerCommand()
    {
    }

    /// <summary>
    /// Construtor com ID e usuário executor
    /// </summary>
    /// <param name="id">ID do container</param>
    /// <param name="executedBy">Usuário que executa o comando</param>
    public DeleteContainerCommand(Guid id, string executedBy) : base(executedBy)
    {
        Id = id;
    }
}

/// <summary>
/// Command para iniciar um container
/// </summary>
public class StartContainerCommand : BaseCommand
{
    /// <summary>
    /// ID do container
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Construtor
    /// </summary>
    public StartContainerCommand()
    {
    }

    /// <summary>
    /// Construtor com ID e usuário executor
    /// </summary>
    /// <param name="id">ID do container</param>
    /// <param name="executedBy">Usuário que executa o comando</param>
    public StartContainerCommand(Guid id, string executedBy) : base(executedBy)
    {
        Id = id;
    }
}

/// <summary>
/// Command para parar um container
/// </summary>
public class StopContainerCommand : BaseCommand
{
    /// <summary>
    /// ID do container
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Timeout em segundos para parada graceful
    /// </summary>
    public int TimeoutSeconds { get; set; } = 10;

    /// <summary>
    /// Forçar parada se timeout for atingido
    /// </summary>
    public bool Force { get; set; } = true;

    /// <summary>
    /// Construtor
    /// </summary>
    public StopContainerCommand()
    {
    }

    /// <summary>
    /// Construtor com ID e usuário executor
    /// </summary>
    /// <param name="id">ID do container</param>
    /// <param name="executedBy">Usuário que executa o comando</param>
    public StopContainerCommand(Guid id, string executedBy) : base(executedBy)
    {
        Id = id;
    }
}

/// <summary>
/// Command para pausar um container
/// </summary>
public class PauseContainerCommand : BaseCommand
{
    /// <summary>
    /// ID do container
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Construtor
    /// </summary>
    public PauseContainerCommand()
    {
    }

    /// <summary>
    /// Construtor com ID e usuário executor
    /// </summary>
    /// <param name="id">ID do container</param>
    /// <param name="executedBy">Usuário que executa o comando</param>
    public PauseContainerCommand(Guid id, string executedBy) : base(executedBy)
    {
        Id = id;
    }
}

/// <summary>
/// Command para despausar um container
/// </summary>
public class UnpauseContainerCommand : BaseCommand
{
    /// <summary>
    /// ID do container
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Construtor
    /// </summary>
    public UnpauseContainerCommand()
    {
    }

    /// <summary>
    /// Construtor com ID e usuário executor
    /// </summary>
    /// <param name="id">ID do container</param>
    /// <param name="executedBy">Usuário que executa o comando</param>
    public UnpauseContainerCommand(Guid id, string executedBy) : base(executedBy)
    {
        Id = id;
    }
}

/// <summary>
/// Command para reiniciar um container
/// </summary>
public class RestartContainerCommand : BaseCommand
{
    /// <summary>
    /// ID do container
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Timeout em segundos para parada antes do restart
    /// </summary>
    public int TimeoutSeconds { get; set; } = 10;

    /// <summary>
    /// Construtor
    /// </summary>
    public RestartContainerCommand()
    {
    }

    /// <summary>
    /// Construtor com ID e usuário executor
    /// </summary>
    /// <param name="id">ID do container</param>
    /// <param name="executedBy">Usuário que executa o comando</param>
    public RestartContainerCommand(Guid id, string executedBy) : base(executedBy)
    {
        Id = id;
    }
}
