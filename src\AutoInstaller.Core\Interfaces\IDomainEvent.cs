using MediatR;

namespace AutoInstaller.Core.Interfaces;

/// <summary>
/// Interface base para eventos de domínio
/// </summary>
public interface IDomainEvent : INotification
{
    /// <summary>
    /// Identificador único do evento
    /// </summary>
    Guid EventId { get; }

    /// <summary>
    /// Data e hora em que o evento ocorreu
    /// </summary>
    DateTime OccurredOn { get; }

    /// <summary>
    /// Tipo do evento
    /// </summary>
    string EventType { get; }

    /// <summary>
    /// Versão do evento (para versionamento de eventos)
    /// </summary>
    int Version { get; }
}

/// <summary>
/// Classe base abstrata para eventos de domínio
/// </summary>
public abstract class DomainEvent : IDomainEvent
{
    /// <summary>
    /// Identificador único do evento
    /// </summary>
    public Guid EventId { get; private set; }

    /// <summary>
    /// Data e hora em que o evento ocorreu
    /// </summary>
    public DateTime OccurredOn { get; private set; }

    /// <summary>
    /// Tipo do evento
    /// </summary>
    public string EventType { get; private set; }

    /// <summary>
    /// Versão do evento
    /// </summary>
    public int Version { get; private set; }

    /// <summary>
    /// Construtor protegido
    /// </summary>
    protected DomainEvent()
    {
        EventId = Guid.NewGuid();
        OccurredOn = DateTime.UtcNow;
        EventType = GetType().Name;
        Version = 1;
    }

    /// <summary>
    /// Construtor com versão específica
    /// </summary>
    /// <param name="version">Versão do evento</param>
    protected DomainEvent(int version) : this()
    {
        Version = version;
    }
}
