using AutoInstaller.Core.Common;
using AutoInstaller.Core.Modules.{{ModuleName}}.Entities;
using System.Linq.Expressions;

namespace AutoInstaller.Core.Modules.{{ModuleName}}.Interfaces;

/// <summary>
/// Interface do repositório para {{EntityName}}
/// Define operações de persistência para a entidade {{EntityName}}
/// </summary>
public interface I{{EntityName}}Repository : IRepository<{{EntityName}}>
{
    // Métodos de consulta específicos
    Task<{{EntityName}}?> GetByNameAsync(string name, CancellationToken cancellationToken = default);
    Task<IEnumerable<{{EntityName}}>> GetByStatusAsync({{EntityName}}Status status, CancellationToken cancellationToken = default);
    Task<IEnumerable<{{EntityName}}>> GetActiveAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<{{EntityName}}>> GetCreatedByUserAsync(string createdBy, CancellationToken cancellationToken = default);
    Task<IEnumerable<{{EntityName}}>> GetCreatedInPeriodAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);
    
    // Métodos de consulta com paginação
    Task<PagedResult<{{EntityName}}>> GetPagedAsync(
        int pageNumber, 
        int pageSize, 
        string? searchTerm = null,
        {{EntityName}}Status? status = null,
        string? sortBy = null,
        bool sortDescending = false,
        CancellationToken cancellationToken = default);
    
    Task<PagedResult<{{EntityName}}>> GetPagedByUserAsync(
        string createdBy,
        int pageNumber, 
        int pageSize, 
        string? searchTerm = null,
        {{EntityName}}Status? status = null,
        CancellationToken cancellationToken = default);
    
    // Métodos de verificação
    Task<bool> ExistsByNameAsync(string name, CancellationToken cancellationToken = default);
    Task<bool> ExistsByNameAsync(string name, Guid excludeId, CancellationToken cancellationToken = default);
    Task<bool> HasActiveAsync(CancellationToken cancellationToken = default);
    
    // Métodos de contagem
    Task<int> CountByStatusAsync({{EntityName}}Status status, CancellationToken cancellationToken = default);
    Task<int> CountByUserAsync(string createdBy, CancellationToken cancellationToken = default);
    Task<int> CountCreatedInPeriodAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);
    
    // Métodos de busca avançada
    Task<IEnumerable<{{EntityName}}>> SearchAsync(
        string searchTerm,
        {{EntityName}}Status? status = null,
        int? limit = null,
        CancellationToken cancellationToken = default);
    
    Task<IEnumerable<{{EntityName}}>> FindAsync(
        Expression<Func<{{EntityName}}, bool>> predicate,
        int? limit = null,
        string? orderBy = null,
        bool orderDescending = false,
        CancellationToken cancellationToken = default);
    
    // Métodos de operações em lote
    Task<IEnumerable<{{EntityName}}>> GetByIdsAsync(IEnumerable<Guid> ids, CancellationToken cancellationToken = default);
    Task<int> BulkUpdateStatusAsync(IEnumerable<Guid> ids, {{EntityName}}Status newStatus, string updatedBy, CancellationToken cancellationToken = default);
    Task<int> BulkDeleteAsync(IEnumerable<Guid> ids, string deletedBy, CancellationToken cancellationToken = default);
    
    // Métodos de estatísticas
    Task<{{EntityName}}Statistics> GetStatisticsAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<{{EntityName}}StatusCount>> GetStatusCountsAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<{{EntityName}}CreationTrend>> GetCreationTrendAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);
    
    // Métodos de limpeza e manutenção
    Task<int> CleanupDeletedAsync(DateTime olderThan, CancellationToken cancellationToken = default);
    Task<int> ArchiveInactiveAsync(DateTime olderThan, CancellationToken cancellationToken = default);
}

/// <summary>
/// Classe para estatísticas de {{EntityName}}
/// </summary>
public class {{EntityName}}Statistics
{
    public int Total { get; set; }
    public int Active { get; set; }
    public int Inactive { get; set; }
    public int Deleted { get; set; }
    public int CreatedToday { get; set; }
    public int CreatedThisWeek { get; set; }
    public int CreatedThisMonth { get; set; }
    public DateTime? LastCreated { get; set; }
    public DateTime? LastUpdated { get; set; }
    public string? MostActiveUser { get; set; }
    public int MostActiveUserCount { get; set; }
}

/// <summary>
/// Classe para contagem por status
/// </summary>
public class {{EntityName}}StatusCount
{
    public {{EntityName}}Status Status { get; set; }
    public int Count { get; set; }
    public double Percentage { get; set; }
}

/// <summary>
/// Classe para tendência de criação
/// </summary>
public class {{EntityName}}CreationTrend
{
    public DateTime Date { get; set; }
    public int Count { get; set; }
    public int CumulativeCount { get; set; }
}

/// <summary>
/// Classe para resultado paginado
/// </summary>
public class PagedResult<T>
{
    public IEnumerable<T> Items { get; set; } = Enumerable.Empty<T>();
    public int TotalCount { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    public bool HasPreviousPage => PageNumber > 1;
    public bool HasNextPage => PageNumber < TotalPages;
    public int StartIndex => (PageNumber - 1) * PageSize + 1;
    public int EndIndex => Math.Min(StartIndex + PageSize - 1, TotalCount);
}