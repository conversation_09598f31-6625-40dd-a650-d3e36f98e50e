using FluentValidation;
using {{RootNamespace}}.Application.Modules.{{ModuleName}}.Commands;
using {{RootNamespace}}.Core.Modules.{{ModuleName}}.Interfaces;
using Microsoft.Extensions.Localization;

namespace {{RootNamespace}}.Application.Modules.{{ModuleName}}.Validators;

/// <summary>
/// Validador para Create{{EntityName}}Command
/// </summary>
public class Create{{EntityName}}CommandValidator : AbstractValidator<Create{{EntityName}}Command>
{
    private readonly I{{EntityName}}Repository _{{LowerEntityName}}Repository;
    private readonly IStringLocalizer<Create{{EntityName}}CommandValidator> _localizer;

    public Create{{EntityName}}CommandValidator(
        I{{EntityName}}Repository {{LowerEntityName}}Repository,
        IStringLocalizer<Create{{EntityName}}CommandValidator> localizer)
    {
        _{{LowerEntityName}}Repository = {{LowerEntityName}}Repository ?? throw new ArgumentNullException(nameof({{LowerEntityName}}Repository));
        _localizer = localizer ?? throw new ArgumentNullException(nameof(localizer));

        ConfigureValidationRules();
    }

    private void ConfigureValidationRules()
    {
        // Validação de campos obrigatórios
        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage(_localizer["{{EntityName}}.Name.Required"])
            .MaximumLength(100)
            .WithMessage(_localizer["{{EntityName}}.Name.MaxLength"]);

        RuleFor(x => x.Description)
            .MaximumLength(500)
            .WithMessage(_localizer["{{EntityName}}.Description.MaxLength"])
            .When(x => !string.IsNullOrEmpty(x.Description));

        // Validações customizadas
        RuleFor(x => x)
            .MustAsync(BeUniqueAsync)
            .WithMessage(_localizer["{{EntityName}}.MustBeUnique"])
            .WithName("{{EntityName}}");

        // Adicione mais validações conforme necessário
        RuleFor(x => x.Email)
            .EmailAddress()
            .WithMessage(_localizer["{{EntityName}}.Email.Invalid"])
            .When(x => !string.IsNullOrEmpty(x.Email));

        RuleFor(x => x.CreatedBy)
            .NotEmpty()
            .WithMessage(_localizer["{{EntityName}}.CreatedBy.Required"]);
    }

    /// <summary>
    /// Valida se o {{EntityName}} é único
    /// </summary>
    /// <param name="command">Comando de criação</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se único, false caso contrário</returns>
    private async Task<bool> BeUniqueAsync(Create{{EntityName}}Command command, CancellationToken cancellationToken)
    {
        // Implementar lógica de verificação de unicidade
        // Exemplo: verificar por nome, email, etc.
        var existing = await _{{LowerEntityName}}Repository.GetByNameAsync(command.Name, cancellationToken);
        return existing == null;
    }
}

/// <summary>
/// Validador para Update{{EntityName}}Command
/// </summary>
public class Update{{EntityName}}CommandValidator : AbstractValidator<Update{{EntityName}}Command>
{
    private readonly I{{EntityName}}Repository _{{LowerEntityName}}Repository;
    private readonly IStringLocalizer<Update{{EntityName}}CommandValidator> _localizer;

    public Update{{EntityName}}CommandValidator(
        I{{EntityName}}Repository {{LowerEntityName}}Repository,
        IStringLocalizer<Update{{EntityName}}CommandValidator> localizer)
    {
        _{{LowerEntityName}}Repository = {{LowerEntityName}}Repository ?? throw new ArgumentNullException(nameof({{LowerEntityName}}Repository));
        _localizer = localizer ?? throw new ArgumentNullException(nameof(localizer));

        ConfigureValidationRules();
    }

    private void ConfigureValidationRules()
    {
        RuleFor(x => x.Id)
            .NotEmpty()
            .WithMessage(_localizer["{{EntityName}}.Id.Required"]);

        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage(_localizer["{{EntityName}}.Name.Required"])
            .MaximumLength(100)
            .WithMessage(_localizer["{{EntityName}}.Name.MaxLength"]);

        RuleFor(x => x.Description)
            .MaximumLength(500)
            .WithMessage(_localizer["{{EntityName}}.Description.MaxLength"])
            .When(x => !string.IsNullOrEmpty(x.Description));

        // Validação de existência
        RuleFor(x => x.Id)
            .MustAsync(ExistAsync)
            .WithMessage(_localizer["{{EntityName}}.NotFound"]);

        // Validação de unicidade (excluindo o próprio registro)
        RuleFor(x => x)
            .MustAsync(BeUniqueForUpdateAsync)
            .WithMessage(_localizer["{{EntityName}}.MustBeUnique"])
            .WithName("{{EntityName}}");

        RuleFor(x => x.ModifiedBy)
            .NotEmpty()
            .WithMessage(_localizer["{{EntityName}}.ModifiedBy.Required"]);
    }

    /// <summary>
    /// Valida se o {{EntityName}} existe
    /// </summary>
    /// <param name="id">ID do {{EntityName}}</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se existe, false caso contrário</returns>
    private async Task<bool> ExistAsync(Guid id, CancellationToken cancellationToken)
    {
        var entity = await _{{LowerEntityName}}Repository.GetByIdAsync(id, cancellationToken);
        return entity != null;
    }

    /// <summary>
    /// Valida se o {{EntityName}} é único para atualização
    /// </summary>
    /// <param name="command">Comando de atualização</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se único, false caso contrário</returns>
    private async Task<bool> BeUniqueForUpdateAsync(Update{{EntityName}}Command command, CancellationToken cancellationToken)
    {
        var existing = await _{{LowerEntityName}}Repository.GetByNameAsync(command.Name, cancellationToken);
        return existing == null || existing.Id == command.Id;
    }
}

/// <summary>
/// Validador para Delete{{EntityName}}Command
/// </summary>
public class Delete{{EntityName}}CommandValidator : AbstractValidator<Delete{{EntityName}}Command>
{
    private readonly I{{EntityName}}Repository _{{LowerEntityName}}Repository;
    private readonly IStringLocalizer<Delete{{EntityName}}CommandValidator> _localizer;

    public Delete{{EntityName}}CommandValidator(
        I{{EntityName}}Repository {{LowerEntityName}}Repository,
        IStringLocalizer<Delete{{EntityName}}CommandValidator> localizer)
    {
        _{{LowerEntityName}}Repository = {{LowerEntityName}}Repository ?? throw new ArgumentNullException(nameof({{LowerEntityName}}Repository));
        _localizer = localizer ?? throw new ArgumentNullException(nameof(localizer));

        ConfigureValidationRules();
    }

    private void ConfigureValidationRules()
    {
        RuleFor(x => x.Id)
            .NotEmpty()
            .WithMessage(_localizer["{{EntityName}}.Id.Required"])
            .MustAsync(ExistAsync)
            .WithMessage(_localizer["{{EntityName}}.NotFound"])
            .MustAsync(CanBeDeletedAsync)
            .WithMessage(_localizer["{{EntityName}}.CannotBeDeleted"]);

        RuleFor(x => x.DeletedBy)
            .NotEmpty()
            .WithMessage(_localizer["{{EntityName}}.DeletedBy.Required"]);
    }

    /// <summary>
    /// Valida se o {{EntityName}} existe
    /// </summary>
    /// <param name="id">ID do {{EntityName}}</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se existe, false caso contrário</returns>
    private async Task<bool> ExistAsync(Guid id, CancellationToken cancellationToken)
    {
        var entity = await _{{LowerEntityName}}Repository.GetByIdAsync(id, cancellationToken);
        return entity != null;
    }

    /// <summary>
    /// Valida se o {{EntityName}} pode ser removido
    /// </summary>
    /// <param name="id">ID do {{EntityName}}</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se pode ser removido, false caso contrário</returns>
    private async Task<bool> CanBeDeletedAsync(Guid id, CancellationToken cancellationToken)
    {
        // Implementar lógica para verificar se pode ser removido
        // Exemplo: verificar dependências, relacionamentos, etc.
        
        // Placeholder - implementar conforme regras de negócio
        return true;
    }
}