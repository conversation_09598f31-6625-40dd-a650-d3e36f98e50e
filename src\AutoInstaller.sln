Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1

# Core Projects (Domain Layer)
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AutoInstaller.Core", "AutoInstaller.Core\AutoInstaller.Core.csproj", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}"
EndProject

# Application Layer
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AutoInstaller.Application", "AutoInstaller.Application\AutoInstaller.Application.csproj", "{B2C3D4E5-F6G7-8901-BCDE-F23456789012}"
EndProject

# Infrastructure Layer
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AutoInstaller.Infrastructure", "AutoInstaller.Infrastructure\AutoInstaller.Infrastructure.csproj", "{C3D4E5F6-G7H8-9012-CDEF-345678901234}"
EndProject

# Presentation Layer
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AutoInstaller.UI", "AutoInstaller.UI\AutoInstaller.UI.csproj", "{D4E5F6G7-H8I9-0123-DEF0-456789012345}"
EndProject

# Shared Layer
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AutoInstaller.Shared", "AutoInstaller.Shared\AutoInstaller.Shared.csproj", "{E5F6G7H8-I9J0-1234-EF01-567890123456}"
EndProject

# Test Projects
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AutoInstaller.Core.Tests", "..\tests\AutoInstaller.Core.Tests\AutoInstaller.Core.Tests.csproj", "{F6G7H8I9-J0K1-2345-F012-************}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AutoInstaller.Application.Tests", "..\tests\AutoInstaller.Application.Tests\AutoInstaller.Application.Tests.csproj", "{G7H8I9J0-K1L2-3456-0123-************}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AutoInstaller.Infrastructure.Tests", "..\tests\AutoInstaller.Infrastructure.Tests\AutoInstaller.Infrastructure.Tests.csproj", "{H8I9J0K1-L2M3-4567-1234-************}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AutoInstaller.UI.Tests", "..\tests\AutoInstaller.UI.Tests\AutoInstaller.UI.Tests.csproj", "{I9J0K1L2-M3N4-5678-2345-************}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AutoInstaller.Integration.Tests", "..\tests\AutoInstaller.Integration.Tests\AutoInstaller.Integration.Tests.csproj", "{J0K1L2M3-N4O5-6789-3456-012345678901}"
EndProject

# Solution Folders
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{11111111-2222-3333-4444-************}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{*************-4444-5555-************}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "docs", "docs", "{33333333-4444-5555-6666-777777777777}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "scripts", "scripts", "{44444444-5555-6666-7777-888888888888}"
EndProject

Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.Build.0 = Release|Any CPU
		{B2C3D4E5-F6G7-8901-BCDE-F23456789012}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B2C3D4E5-F6G7-8901-BCDE-F23456789012}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B2C3D4E5-F6G7-8901-BCDE-F23456789012}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B2C3D4E5-F6G7-8901-BCDE-F23456789012}.Release|Any CPU.Build.0 = Release|Any CPU
		{C3D4E5F6-G7H8-9012-CDEF-345678901234}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C3D4E5F6-G7H8-9012-CDEF-345678901234}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C3D4E5F6-G7H8-9012-CDEF-345678901234}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C3D4E5F6-G7H8-9012-CDEF-345678901234}.Release|Any CPU.Build.0 = Release|Any CPU
		{D4E5F6G7-H8I9-0123-DEF0-456789012345}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D4E5F6G7-H8I9-0123-DEF0-456789012345}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D4E5F6G7-H8I9-0123-DEF0-456789012345}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D4E5F6G7-H8I9-0123-DEF0-456789012345}.Release|Any CPU.Build.0 = Release|Any CPU
		{E5F6G7H8-I9J0-1234-EF01-567890123456}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E5F6G7H8-I9J0-1234-EF01-567890123456}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E5F6G7H8-I9J0-1234-EF01-567890123456}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E5F6G7H8-I9J0-1234-EF01-567890123456}.Release|Any CPU.Build.0 = Release|Any CPU
		{F6G7H8I9-J0K1-2345-F012-************}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F6G7H8I9-J0K1-2345-F012-************}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F6G7H8I9-J0K1-2345-F012-************}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F6G7H8I9-J0K1-2345-F012-************}.Release|Any CPU.Build.0 = Release|Any CPU
		{G7H8I9J0-K1L2-3456-0123-************}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{G7H8I9J0-K1L2-3456-0123-************}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{G7H8I9J0-K1L2-3456-0123-************}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{G7H8I9J0-K1L2-3456-0123-************}.Release|Any CPU.Build.0 = Release|Any CPU
		{H8I9J0K1-L2M3-4567-1234-************}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{H8I9J0K1-L2M3-4567-1234-************}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{H8I9J0K1-L2M3-4567-1234-************}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{H8I9J0K1-L2M3-4567-1234-************}.Release|Any CPU.Build.0 = Release|Any CPU
		{I9J0K1L2-M3N4-5678-2345-************}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{I9J0K1L2-M3N4-5678-2345-************}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{I9J0K1L2-M3N4-5678-2345-************}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{I9J0K1L2-M3N4-5678-2345-************}.Release|Any CPU.Build.0 = Release|Any CPU
		{J0K1L2M3-N4O5-6789-3456-012345678901}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{J0K1L2M3-N4O5-6789-3456-012345678901}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{J0K1L2M3-N4O5-6789-3456-012345678901}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{J0K1L2M3-N4O5-6789-3456-012345678901}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890} = {11111111-2222-3333-4444-************}
		{B2C3D4E5-F6G7-8901-BCDE-F23456789012} = {11111111-2222-3333-4444-************}
		{C3D4E5F6-G7H8-9012-CDEF-345678901234} = {11111111-2222-3333-4444-************}
		{D4E5F6G7-H8I9-0123-DEF0-456789012345} = {11111111-2222-3333-4444-************}
		{E5F6G7H8-I9J0-1234-EF01-567890123456} = {11111111-2222-3333-4444-************}
		{F6G7H8I9-J0K1-2345-F012-************} = {*************-4444-5555-************}
		{G7H8I9J0-K1L2-3456-0123-************} = {*************-4444-5555-************}
		{H8I9J0K1-L2M3-4567-1234-************} = {*************-4444-5555-************}
		{I9J0K1L2-M3N4-5678-2345-************} = {*************-4444-5555-************}
		{J0K1L2M3-N4O5-6789-3456-012345678901} = {*************-4444-5555-************}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {12345678-1234-1234-1234-123456789012}
	EndGlobalSection
EndGlobal
