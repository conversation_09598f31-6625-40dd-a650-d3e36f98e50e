using Avalonia;
using Avalonia.Controls;
using Avalonia.Media;

namespace AutoInstaller.UI.Controls;

/// <summary>
/// Controle de loading spinner animado
/// </summary>
public partial class LoadingSpinner : UserControl
{
    /// <summary>
    /// Propriedade para cor do spinner
    /// </summary>
    public static readonly StyledProperty<IBrush?> SpinnerBrushProperty =
        AvaloniaProperty.Register<LoadingSpinner, IBrush?>(nameof(SpinnerBrush));

    /// <summary>
    /// Propriedade para velocidade da animação
    /// </summary>
    public static readonly StyledProperty<double> AnimationSpeedProperty =
        AvaloniaProperty.Register<LoadingSpinner, double>(nameof(AnimationSpeed), 1.0);

    /// <summary>
    /// Propriedade para tamanho do ponto
    /// </summary>
    public static readonly StyledProperty<double> DotSizeProperty =
        AvaloniaProperty.Register<LoadingSpinner, double>(nameof(DotSize), 3.0);

    /// <summary>
    /// Cor do spinner
    /// </summary>
    public IBrush? SpinnerBrush
    {
        get => GetValue(SpinnerBrushProperty);
        set => SetValue(SpinnerBrushProperty, value);
    }

    /// <summary>
    /// Velocidade da animação (1.0 = normal, 2.0 = 2x mais rápido, 0.5 = 2x mais lento)
    /// </summary>
    public double AnimationSpeed
    {
        get => GetValue(AnimationSpeedProperty);
        set => SetValue(AnimationSpeedProperty, value);
    }

    /// <summary>
    /// Tamanho dos pontos do spinner
    /// </summary>
    public double DotSize
    {
        get => GetValue(DotSizeProperty);
        set => SetValue(DotSizeProperty, value);
    }

    /// <summary>
    /// Construtor
    /// </summary>
    public LoadingSpinner()
    {
        InitializeComponent();
        
        // Observar mudanças nas propriedades
        SpinnerBrushProperty.Changed.AddClassHandler<LoadingSpinner>(OnSpinnerBrushChanged);
        DotSizeProperty.Changed.AddClassHandler<LoadingSpinner>(OnDotSizeChanged);
    }

    /// <summary>
    /// Chamado quando a cor do spinner muda
    /// </summary>
    /// <param name="sender">Sender</param>
    /// <param name="e">Event args</param>
    private static void OnSpinnerBrushChanged(LoadingSpinner sender, AvaloniaPropertyChangedEventArgs e)
    {
        sender.UpdateSpinnerBrush();
    }

    /// <summary>
    /// Chamado quando o tamanho do ponto muda
    /// </summary>
    /// <param name="sender">Sender</param>
    /// <param name="e">Event args</param>
    private static void OnDotSizeChanged(LoadingSpinner sender, AvaloniaPropertyChangedEventArgs e)
    {
        sender.UpdateDotSize();
    }

    /// <summary>
    /// Atualiza a cor do spinner
    /// </summary>
    private void UpdateSpinnerBrush()
    {
        if (SpinnerBrush == null)
            return;

        // Encontrar todos os pontos e atualizar a cor
        var canvas = this.FindControl<Canvas>("SpinnerCanvas");
        if (canvas == null)
            return;

        foreach (var child in canvas.Children)
        {
            if (child is Ellipse ellipse)
            {
                ellipse.Fill = SpinnerBrush;
            }
        }
    }

    /// <summary>
    /// Atualiza o tamanho dos pontos
    /// </summary>
    private void UpdateDotSize()
    {
        var canvas = this.FindControl<Canvas>("SpinnerCanvas");
        if (canvas == null)
            return;

        foreach (var child in canvas.Children)
        {
            if (child is Ellipse ellipse)
            {
                ellipse.Width = DotSize;
                ellipse.Height = DotSize;
                
                // Reposicionar o ponto para manter o centro
                var left = Canvas.GetLeft(ellipse);
                var top = Canvas.GetTop(ellipse);
                
                // Ajustar posição baseado no novo tamanho
                Canvas.SetLeft(ellipse, left - (DotSize - 3.0) / 2);
                Canvas.SetTop(ellipse, top - (DotSize - 3.0) / 2);
            }
        }
    }

    /// <summary>
    /// Inicia a animação do spinner
    /// </summary>
    public void Start()
    {
        IsVisible = true;
        // As animações são definidas em CSS e iniciam automaticamente
    }

    /// <summary>
    /// Para a animação do spinner
    /// </summary>
    public void Stop()
    {
        IsVisible = false;
    }

    /// <summary>
    /// Alterna o estado do spinner
    /// </summary>
    public void Toggle()
    {
        if (IsVisible)
            Stop();
        else
            Start();
    }
}
