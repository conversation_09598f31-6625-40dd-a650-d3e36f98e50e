using AutoInstaller.Core.Common;
using AutoInstaller.Core.Exceptions;
using AutoInstaller.Core.Interfaces;
using AutoInstaller.Core.ValueObjects;
using AutoInstaller.Core.Events;

namespace AutoInstaller.Core.Entities;

/// <summary>
/// Status do container
/// </summary>
public enum ContainerStatus
{
    Created,
    Running,
    Paused,
    Restarting,
    Removing,
    Exited,
    Dead
}

/// <summary>
/// Tipo de runtime do container
/// </summary>
public enum ContainerRuntime
{
    Docker
}

/// <summary>
/// Entidade Container - Aggregate Root
/// </summary>
public class Container : BaseEntity, IAggregateRoot
{
    private readonly List<Port> _exposedPorts = new();
    private readonly Dictionary<string, string> _environmentVariables = new();
    private readonly Dictionary<string, string> _labels = new();
    private readonly List<string> _volumes = new();

    /// <summary>
    /// Nome do container
    /// </summary>
    public ContainerName Name { get; private set; }

    /// <summary>
    /// Nome da imagem
    /// </summary>
    public string ImageName { get; private set; }

    /// <summary>
    /// Tag da imagem
    /// </summary>
    public ImageTag ImageTag { get; private set; }

    /// <summary>
    /// Status atual do container
    /// </summary>
    public ContainerStatus Status { get; private set; }

    /// <summary>
    /// Runtime utilizado (Docker)
    /// </summary>
    public ContainerRuntime Runtime { get; private set; }

    /// <summary>
    /// ID do container no runtime
    /// </summary>
    public string? RuntimeId { get; private set; }

    /// <summary>
    /// Comando executado no container
    /// </summary>
    public string? Command { get; private set; }

    /// <summary>
    /// Argumentos do comando
    /// </summary>
    public string[]? Arguments { get; private set; }

    /// <summary>
    /// Diretório de trabalho
    /// </summary>
    public string? WorkingDirectory { get; private set; }

    /// <summary>
    /// Usuário que executa o container
    /// </summary>
    public string? User { get; private set; }

    /// <summary>
    /// Indica se o container deve reiniciar automaticamente
    /// </summary>
    public bool AutoRestart { get; private set; }

    /// <summary>
    /// Política de reinicialização
    /// </summary>
    public string RestartPolicy { get; private set; }

    /// <summary>
    /// Limite de memória em bytes
    /// </summary>
    public long? MemoryLimit { get; private set; }

    /// <summary>
    /// Limite de CPU (número de cores)
    /// </summary>
    public double? CpuLimit { get; private set; }

    /// <summary>
    /// Data de início do container
    /// </summary>
    public DateTime? StartedAt { get; private set; }

    /// <summary>
    /// Data de parada do container
    /// </summary>
    public DateTime? StoppedAt { get; private set; }

    /// <summary>
    /// Portas expostas pelo container
    /// </summary>
    public IReadOnlyList<Port> ExposedPorts => _exposedPorts.AsReadOnly();

    /// <summary>
    /// Variáveis de ambiente
    /// </summary>
    public IReadOnlyDictionary<string, string> EnvironmentVariables => _environmentVariables.AsReadOnly();

    /// <summary>
    /// Labels do container
    /// </summary>
    public IReadOnlyDictionary<string, string> Labels => _labels.AsReadOnly();

    /// <summary>
    /// Volumes montados
    /// </summary>
    public IReadOnlyList<string> Volumes => _volumes.AsReadOnly();

    /// <summary>
    /// Construtor protegido para EF Core
    /// </summary>
    protected Container() : base()
    {
        Name = ContainerName.Create("default");
        ImageName = string.Empty;
        ImageTag = ImageTag.CreateLatest();
        Status = ContainerStatus.Created;
        Runtime = ContainerRuntime.Docker;
        RestartPolicy = "no";
    }

    /// <summary>
    /// Construtor principal
    /// </summary>
    /// <param name="name">Nome do container</param>
    /// <param name="imageName">Nome da imagem</param>
    /// <param name="imageTag">Tag da imagem</param>
    /// <param name="runtime">Runtime do container</param>
    /// <param name="createdBy">Usuário que criou</param>
    public Container(ContainerName name, string imageName, ImageTag imageTag, 
        ContainerRuntime runtime, string createdBy) : base(createdBy)
    {
        if (string.IsNullOrWhiteSpace(imageName))
            throw new BusinessRuleException("Nome da imagem não pode ser vazio");

        Name = name ?? throw new ArgumentNullException(nameof(name));
        ImageName = imageName.Trim();
        ImageTag = imageTag ?? ImageTag.CreateLatest();
        Runtime = runtime;
        Status = ContainerStatus.Created;
        RestartPolicy = "no";

        AddDomainEvent(new ContainerCreatedEvent(Id, Name, ImageName, ImageTag, Runtime));
    }

    /// <summary>
    /// Atualiza o status do container
    /// </summary>
    /// <param name="newStatus">Novo status</param>
    /// <param name="updatedBy">Usuário que atualizou</param>
    public void UpdateStatus(ContainerStatus newStatus, string updatedBy)
    {
        if (Status == newStatus)
            return;

        var previousStatus = Status;
        Status = newStatus;
        MarkAsUpdated(updatedBy);

        // Atualiza timestamps baseado no status
        switch (newStatus)
        {
            case ContainerStatus.Running:
                StartedAt = DateTime.UtcNow;
                StoppedAt = null;
                break;
            case ContainerStatus.Exited:
            case ContainerStatus.Dead:
                StoppedAt = DateTime.UtcNow;
                break;
        }

        AddDomainEvent(new ContainerStatusChangedEvent(Id, previousStatus, newStatus));
    }

    /// <summary>
    /// Define o ID do runtime
    /// </summary>
    /// <param name="runtimeId">ID do container no runtime</param>
    /// <param name="updatedBy">Usuário que atualizou</param>
    public void SetRuntimeId(string runtimeId, string updatedBy)
    {
        if (string.IsNullOrWhiteSpace(runtimeId))
            throw new BusinessRuleException("Runtime ID não pode ser vazio");

        RuntimeId = runtimeId.Trim();
        MarkAsUpdated(updatedBy);
    }

    /// <summary>
    /// Configura o comando do container
    /// </summary>
    /// <param name="command">Comando principal</param>
    /// <param name="arguments">Argumentos do comando</param>
    /// <param name="updatedBy">Usuário que atualizou</param>
    public void SetCommand(string command, string[]? arguments, string updatedBy)
    {
        if (string.IsNullOrWhiteSpace(command))
            throw new BusinessRuleException("Comando não pode ser vazio");

        Command = command.Trim();
        Arguments = arguments?.Where(arg => !string.IsNullOrWhiteSpace(arg))
                              .Select(arg => arg.Trim())
                              .ToArray();
        MarkAsUpdated(updatedBy);
    }

    /// <summary>
    /// Define configurações de execução
    /// </summary>
    /// <param name="workingDirectory">Diretório de trabalho</param>
    /// <param name="user">Usuário de execução</param>
    /// <param name="updatedBy">Usuário que atualizou</param>
    public void SetExecutionSettings(string? workingDirectory, string? user, string updatedBy)
    {
        WorkingDirectory = string.IsNullOrWhiteSpace(workingDirectory) ? null : workingDirectory.Trim();
        User = string.IsNullOrWhiteSpace(user) ? null : user.Trim();
        MarkAsUpdated(updatedBy);
    }

    /// <summary>
    /// Configura política de reinicialização
    /// </summary>
    /// <param name="autoRestart">Se deve reiniciar automaticamente</param>
    /// <param name="restartPolicy">Política de reinicialização</param>
    /// <param name="updatedBy">Usuário que atualizou</param>
    public void SetRestartPolicy(bool autoRestart, string restartPolicy, string updatedBy)
    {
        if (string.IsNullOrWhiteSpace(restartPolicy))
            throw new BusinessRuleException("Política de reinicialização não pode ser vazia");

        var validPolicies = new[] { "no", "always", "unless-stopped", "on-failure" };
        if (!validPolicies.Contains(restartPolicy.ToLowerInvariant()))
            throw new BusinessRuleException($"Política de reinicialização deve ser uma das: {string.Join(", ", validPolicies)}");

        AutoRestart = autoRestart;
        RestartPolicy = restartPolicy.ToLowerInvariant();
        MarkAsUpdated(updatedBy);
    }

    /// <summary>
    /// Define limites de recursos
    /// </summary>
    /// <param name="memoryLimit">Limite de memória em bytes</param>
    /// <param name="cpuLimit">Limite de CPU</param>
    /// <param name="updatedBy">Usuário que atualizou</param>
    public void SetResourceLimits(long? memoryLimit, double? cpuLimit, string updatedBy)
    {
        if (memoryLimit.HasValue && memoryLimit.Value <= 0)
            throw new BusinessRuleException("Limite de memória deve ser positivo");

        if (cpuLimit.HasValue && cpuLimit.Value <= 0)
            throw new BusinessRuleException("Limite de CPU deve ser positivo");

        MemoryLimit = memoryLimit;
        CpuLimit = cpuLimit;
        MarkAsUpdated(updatedBy);
    }

    /// <summary>
    /// Adiciona uma porta exposta
    /// </summary>
    /// <param name="port">Porta a ser exposta</param>
    /// <param name="updatedBy">Usuário que atualizou</param>
    public void AddExposedPort(Port port, string updatedBy)
    {
        if (_exposedPorts.Contains(port))
            throw new BusinessRuleException($"Porta {port} já está exposta");

        _exposedPorts.Add(port);
        MarkAsUpdated(updatedBy);
    }

    /// <summary>
    /// Remove uma porta exposta
    /// </summary>
    /// <param name="port">Porta a ser removida</param>
    /// <param name="updatedBy">Usuário que atualizou</param>
    public void RemoveExposedPort(Port port, string updatedBy)
    {
        if (_exposedPorts.Remove(port))
        {
            MarkAsUpdated(updatedBy);
        }
    }

    /// <summary>
    /// Adiciona uma variável de ambiente
    /// </summary>
    /// <param name="key">Chave da variável</param>
    /// <param name="value">Valor da variável</param>
    /// <param name="updatedBy">Usuário que atualizou</param>
    public void AddEnvironmentVariable(string key, string value, string updatedBy)
    {
        if (string.IsNullOrWhiteSpace(key))
            throw new BusinessRuleException("Chave da variável de ambiente não pode ser vazia");

        _environmentVariables[key.Trim()] = value ?? string.Empty;
        MarkAsUpdated(updatedBy);
    }

    /// <summary>
    /// Remove uma variável de ambiente
    /// </summary>
    /// <param name="key">Chave da variável</param>
    /// <param name="updatedBy">Usuário que atualizou</param>
    public void RemoveEnvironmentVariable(string key, string updatedBy)
    {
        if (_environmentVariables.Remove(key))
        {
            MarkAsUpdated(updatedBy);
        }
    }

    /// <summary>
    /// Adiciona um label
    /// </summary>
    /// <param name="key">Chave do label</param>
    /// <param name="value">Valor do label</param>
    /// <param name="updatedBy">Usuário que atualizou</param>
    public void AddLabel(string key, string value, string updatedBy)
    {
        if (string.IsNullOrWhiteSpace(key))
            throw new BusinessRuleException("Chave do label não pode ser vazia");

        _labels[key.Trim()] = value ?? string.Empty;
        MarkAsUpdated(updatedBy);
    }

    /// <summary>
    /// Remove um label
    /// </summary>
    /// <param name="key">Chave do label</param>
    /// <param name="updatedBy">Usuário que atualizou</param>
    public void RemoveLabel(string key, string updatedBy)
    {
        if (_labels.Remove(key))
        {
            MarkAsUpdated(updatedBy);
        }
    }

    /// <summary>
    /// Adiciona um volume
    /// </summary>
    /// <param name="volume">Definição do volume</param>
    /// <param name="updatedBy">Usuário que atualizou</param>
    public void AddVolume(string volume, string updatedBy)
    {
        if (string.IsNullOrWhiteSpace(volume))
            throw new BusinessRuleException("Definição do volume não pode ser vazia");

        var volumeDefinition = volume.Trim();
        if (!_volumes.Contains(volumeDefinition))
        {
            _volumes.Add(volumeDefinition);
            MarkAsUpdated(updatedBy);
        }
    }

    /// <summary>
    /// Remove um volume
    /// </summary>
    /// <param name="volume">Definição do volume</param>
    /// <param name="updatedBy">Usuário que atualizou</param>
    public void RemoveVolume(string volume, string updatedBy)
    {
        if (_volumes.Remove(volume))
        {
            MarkAsUpdated(updatedBy);
        }
    }

    /// <summary>
    /// Verifica se o container pode ser iniciado
    /// </summary>
    /// <returns>True se pode ser iniciado</returns>
    public bool CanStart()
    {
        return Status == ContainerStatus.Created || Status == ContainerStatus.Exited;
    }

    /// <summary>
    /// Verifica se o container pode ser parado
    /// </summary>
    /// <returns>True se pode ser parado</returns>
    public bool CanStop()
    {
        return Status == ContainerStatus.Running || Status == ContainerStatus.Paused;
    }

    /// <summary>
    /// Verifica se o container pode ser pausado
    /// </summary>
    /// <returns>True se pode ser pausado</returns>
    public bool CanPause()
    {
        return Status == ContainerStatus.Running;
    }

    /// <summary>
    /// Verifica se o container pode ser despausado
    /// </summary>
    /// <returns>True se pode ser despausado</returns>
    public bool CanUnpause()
    {
        return Status == ContainerStatus.Paused;
    }

    /// <summary>
    /// Verifica se o container pode ser removido
    /// </summary>
    /// <returns>True se pode ser removido</returns>
    public bool CanRemove()
    {
        return Status == ContainerStatus.Created || Status == ContainerStatus.Exited || Status == ContainerStatus.Dead;
    }
}
