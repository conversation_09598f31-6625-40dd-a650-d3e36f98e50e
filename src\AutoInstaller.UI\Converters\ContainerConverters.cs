using Avalonia.Data.Converters;
using Avalonia.Media;
using Material.Icons;
using System.Globalization;

namespace AutoInstaller.UI.Converters;

/// <summary>
/// Converter para status do container para cor
/// </summary>
public class StatusToBrushConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is not string status)
            return Brushes.Gray;

        return status.ToLowerInvariant() switch
        {
            "running" => Brushes.Green,
            "created" => Brushes.Blue,
            "paused" => Brushes.Orange,
            "restarting" => Brushes.Yellow,
            "removing" => Brushes.Purple,
            "exited" => Brushes.Red,
            "dead" => Brushes.DarkRed,
            _ => Brushes.Gray
        };
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// Converter para runtime do container para ícone
/// </summary>
public class RuntimeToIconConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is not string runtime)
            return MaterialIconKind.HelpCircle;

        return runtime.ToLowerInvariant() switch
        {
            "docker" => MaterialIconKind.Docker,
            _ => MaterialIconKind.HelpCircle
        };
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// Converter para DateTime para string formatada
/// </summary>
public class DateTimeToStringConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is not DateTime dateTime)
            return string.Empty;

        var now = DateTime.Now;
        var diff = now - dateTime;

        if (diff.TotalMinutes < 1)
            return "Agora";
        
        if (diff.TotalMinutes < 60)
            return $"{(int)diff.TotalMinutes}m atrás";
        
        if (diff.TotalHours < 24)
            return $"{(int)diff.TotalHours}h atrás";
        
        if (diff.TotalDays < 7)
            return $"{(int)diff.TotalDays}d atrás";
        
        return dateTime.ToString("dd/MM/yyyy");
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// Converter para verificar se container pode ser iniciado
/// </summary>
public class CanStartConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is not string status)
            return false;

        return status.ToLowerInvariant() is "created" or "exited";
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// Converter para verificar se container pode ser parado
/// </summary>
public class CanStopConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is not string status)
            return false;

        return status.ToLowerInvariant() is "running" or "paused";
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// Converter para verificar se container pode ser pausado
/// </summary>
public class CanPauseConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is not string status)
            return false;

        return status.ToLowerInvariant() == "running";
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// Converter para verificar se container pode ser despausado
/// </summary>
public class CanUnpauseConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is not string status)
            return false;

        return status.ToLowerInvariant() == "paused";
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// Converter para formatar bytes em formato legível
/// </summary>
public class BytesToStringConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is not long bytes)
            return "0 B";

        string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
        int counter = 0;
        decimal number = bytes;

        while (Math.Round(number / 1024) >= 1)
        {
            number /= 1024;
            counter++;
        }

        return $"{number:n1} {suffixes[counter]}";
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// Converter para formatar CPU em formato legível
/// </summary>
public class CpuToStringConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is not double cpu)
            return "0 CPU";

        return $"{cpu:F2} CPU";
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// Converter para verificar se valor é nulo
/// </summary>
public class IsNullConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        return value == null;
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// Converter para verificar se valor não é nulo
/// </summary>
public class IsNotNullConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        return value != null;
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// Converter para inverter valor booleano
/// </summary>
public class InverseBooleanConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is bool boolValue)
            return !boolValue;
        
        return true;
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is bool boolValue)
            return !boolValue;
        
        return false;
    }
}

/// <summary>
/// Converter para pluralização de texto
/// </summary>
public class PluralConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is not int count)
            return string.Empty;

        if (parameter is not string text)
            return string.Empty;

        var parts = text.Split('|');
        if (parts.Length != 2)
            return text;

        return count == 1 ? parts[0] : parts[1];
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// Converter para status do container para texto amigável
/// </summary>
public class StatusToFriendlyTextConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is not string status)
            return "Desconhecido";

        return status.ToLowerInvariant() switch
        {
            "created" => "Criado",
            "running" => "Executando",
            "paused" => "Pausado",
            "restarting" => "Reiniciando",
            "removing" => "Removendo",
            "exited" => "Parado",
            "dead" => "Morto",
            _ => status
        };
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// Converter para runtime do container para texto amigável
/// </summary>
public class RuntimeToFriendlyTextConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is not string runtime)
            return "Desconhecido";

        return runtime.ToLowerInvariant() switch
        {
            "docker" => "Docker",
            _ => runtime
        };
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// Converter para formatar lista de portas
/// </summary>
public class PortsToStringConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is not int count)
            return "Nenhuma";

        return count switch
        {
            0 => "Nenhuma",
            1 => "1 porta",
            _ => $"{count} portas"
        };
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
