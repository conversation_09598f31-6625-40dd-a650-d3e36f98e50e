<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:material="using:Material.Icons.Avalonia"
             xmlns:vm="using:{{RootNamespace}}.UI.Modules.{{ModuleName}}.ViewModels"
             mc:Ignorable="d" d:DesignWidth="400" d:DesignHeight="300"
             x:Class="{{RootNamespace}}.UI.Modules.{{ModuleName}}.Views.{{EntityName}}Card"
             x:DataType="vm:{{EntityName}}CardViewModel">

  <Design.DataContext>
    <vm:{{EntityName}}CardViewModel />
  </Design.DataContext>

  <UserControl.Styles>
    <Style Selector="Border.card">
      <Setter Property="Background" Value="{DynamicResource SurfaceVariantBrush}" />
      <Setter Property="CornerRadius" Value="12" />
      <Setter Property="BorderThickness" Value="1" />
      <Setter Property="BorderBrush" Value="{DynamicResource OutlineVariantBrush}" />
      <Setter Property="Padding" Value="16" />
      <Setter Property="Margin" Value="8" />
      <Setter Property="BoxShadow" Value="0 2 8 0 #1A000000" />
      <Setter Property="Transitions">
        <Transitions>
          <BoxShadowsTransition Property="BoxShadow" Duration="0:0:0.2" />
          <TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.2" />
        </Transitions>
      </Setter>
    </Style>

    <Style Selector="Border.card:pointerover">
      <Setter Property="BoxShadow" Value="0 4 12 0 #2A000000" />
      <Setter Property="RenderTransform" Value="translateY(-2px)" />
    </Style>

    <Style Selector="Border.card:pressed">
      <Setter Property="BoxShadow" Value="0 1 4 0 #1A000000" />
      <Setter Property="RenderTransform" Value="translateY(1px)" />
    </Style>

    <Style Selector="TextBlock.title">
      <Setter Property="FontSize" Value="18" />
      <Setter Property="FontWeight" Value="SemiBold" />
      <Setter Property="Foreground" Value="{DynamicResource OnSurfaceBrush}" />
      <Setter Property="Margin" Value="0,0,0,8" />
    </Style>

    <Style Selector="TextBlock.subtitle">
      <Setter Property="FontSize" Value="14" />
      <Setter Property="Foreground" Value="{DynamicResource OnSurfaceVariantBrush}" />
      <Setter Property="Margin" Value="0,0,0,12" />
    </Style>

    <Style Selector="TextBlock.content">
      <Setter Property="FontSize" Value="14" />
      <Setter Property="Foreground" Value="{DynamicResource OnSurfaceBrush}" />
      <Setter Property="TextWrapping" Value="Wrap" />
      <Setter Property="LineHeight" Value="20" />
    </Style>

    <Style Selector="Button.card-action">
      <Setter Property="Background" Value="Transparent" />
      <Setter Property="BorderThickness" Value="0" />
      <Setter Property="Padding" Value="12,8" />
      <Setter Property="Margin" Value="4,0" />
      <Setter Property="CornerRadius" Value="8" />
      <Setter Property="FontWeight" Value="Medium" />
      <Setter Property="Foreground" Value="{DynamicResource PrimaryBrush}" />
    </Style>

    <Style Selector="Button.card-action:pointerover">
      <Setter Property="Background" Value="{DynamicResource PrimaryBrush, Opacity=0.08}" />
    </Style>

    <Style Selector="Button.card-action:pressed">
      <Setter Property="Background" Value="{DynamicResource PrimaryBrush, Opacity=0.12}" />
    </Style>

    <Style Selector="material|MaterialIcon.card-icon">
      <Setter Property="Width" Value="24" />
      <Setter Property="Height" Value="24" />
      <Setter Property="Foreground" Value="{DynamicResource PrimaryBrush}" />
      <Setter Property="Margin" Value="0,0,12,0" />
    </Style>

    <Style Selector="ProgressBar.card-progress">
      <Setter Property="Height" Value="4" />
      <Setter Property="CornerRadius" Value="2" />
      <Setter Property="Background" Value="{DynamicResource OutlineVariantBrush}" />
      <Setter Property="Foreground" Value="{DynamicResource PrimaryBrush}" />
      <Setter Property="Margin" Value="0,12,0,0" />
    </Style>
  </UserControl.Styles>

  <Border Classes="card"
          IsVisible="{Binding IsVisible}"
          IsEnabled="{Binding IsEnabled}">
    
    <Grid RowDefinitions="Auto,*,Auto,Auto">
      
      <!-- Header com ícone e título -->
      <Grid Grid.Row="0" ColumnDefinitions="Auto,*,Auto">
        
        <!-- Ícone -->
        <material:MaterialIcon Grid.Column="0"
                              Classes="card-icon"
                              Kind="{Binding IconKind}"
                              IsVisible="{Binding ShowIcon}" />
        
        <!-- Título e subtítulo -->
        <StackPanel Grid.Column="1" VerticalAlignment="Center">
          <TextBlock Classes="title"
                    Text="{Binding Title}"
                    IsVisible="{Binding !#Title.Text, Converter={x:Static StringConverters.IsNotNullOrEmpty}}" />
          
          <TextBlock Classes="subtitle"
                    Text="{Binding Subtitle}"
                    IsVisible="{Binding !#Subtitle.Text, Converter={x:Static StringConverters.IsNotNullOrEmpty}}" />
        </StackPanel>
        
        <!-- Menu de ações -->
        <Button Grid.Column="2"
               Classes="card-action"
               Command="{Binding ShowMenuCommand}"
               IsVisible="{Binding HasMenuActions}">
          <material:MaterialIcon Kind="DotsVertical" Width="20" Height="20" />
        </Button>
        
      </Grid>
      
      <!-- Conteúdo principal -->
      <ScrollViewer Grid.Row="1"
                   Margin="0,8"
                   MaxHeight="{Binding MaxContentHeight}"
                   HorizontalScrollBarVisibility="Disabled"
                   VerticalScrollBarVisibility="Auto">
        
        <StackPanel Spacing="12">
          
          <!-- Texto de conteúdo -->
          <TextBlock Classes="content"
                    Text="{Binding Content}"
                    IsVisible="{Binding !#Content.Text, Converter={x:Static StringConverters.IsNotNullOrEmpty}}" />
          
          <!-- Conteúdo customizado -->
          <ContentPresenter Content="{Binding CustomContent}"
                           IsVisible="{Binding HasCustomContent}" />
          
          <!-- Lista de itens -->
          <ItemsControl ItemsSource="{Binding Items}"
                       IsVisible="{Binding HasItems}">
            <ItemsControl.ItemTemplate>
              <DataTemplate>
                <Border Background="{DynamicResource SurfaceBrush}"
                       CornerRadius="8"
                       Padding="12,8"
                       Margin="0,4">
                  <Grid ColumnDefinitions="Auto,*,Auto">
                    
                    <material:MaterialIcon Grid.Column="0"
                                          Kind="{Binding IconKind}"
                                          Width="16" Height="16"
                                          Foreground="{DynamicResource OnSurfaceVariantBrush}"
                                          Margin="0,0,8,0"
                                          IsVisible="{Binding HasIcon}" />
                    
                    <TextBlock Grid.Column="1"
                              Text="{Binding Text}"
                              FontSize="13"
                              Foreground="{DynamicResource OnSurfaceBrush}"
                              VerticalAlignment="Center" />
                    
                    <TextBlock Grid.Column="2"
                              Text="{Binding Value}"
                              FontSize="13"
                              FontWeight="Medium"
                              Foreground="{DynamicResource OnSurfaceVariantBrush}"
                              VerticalAlignment="Center"
                              IsVisible="{Binding HasValue}" />
                    
                  </Grid>
                </Border>
              </DataTemplate>
            </ItemsControl.ItemTemplate>
          </ItemsControl>
          
        </StackPanel>
        
      </ScrollViewer>
      
      <!-- Barra de progresso -->
      <ProgressBar Grid.Row="2"
                  Classes="card-progress"
                  Value="{Binding ProgressValue}"
                  Maximum="{Binding ProgressMaximum}"
                  IsVisible="{Binding ShowProgress}"
                  IsIndeterminate="{Binding IsProgressIndeterminate}" />
      
      <!-- Ações do rodapé -->
      <StackPanel Grid.Row="3"
                 Orientation="Horizontal"
                 HorizontalAlignment="Right"
                 Margin="0,12,0,0"
                 Spacing="8"
                 IsVisible="{Binding HasActions}">
        
        <Button Classes="card-action"
               Content="{Binding SecondaryActionText}"
               Command="{Binding SecondaryActionCommand}"
               IsVisible="{Binding HasSecondaryAction}" />
        
        <Button Classes="card-action"
               Content="{Binding PrimaryActionText}"
               Command="{Binding PrimaryActionCommand}"
               IsVisible="{Binding HasPrimaryAction}"
               FontWeight="SemiBold" />
        
      </StackPanel>
      
    </Grid>
    
  </Border>
  
</UserControl>