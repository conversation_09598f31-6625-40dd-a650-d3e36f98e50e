using AutoInstaller.Application.DTOs;
using AutoInstaller.Application.Features.Containers.Commands;
using AutoInstaller.Application.Features.Containers.Queries;
using AutoInstaller.Core.Entities;
using MediatR;
using Microsoft.Extensions.Logging;
using ReactiveUI;
using System.Collections.ObjectModel;
using System.Reactive;
using System.Reactive.Linq;

namespace AutoInstaller.UI.ViewModels;

/// <summary>
/// ViewModel para gerenciamento de containers
/// </summary>
public class ContainerViewModel : BaseListViewModel<ContainerSummaryDto>
{
    private readonly IMediator _mediator;
    private ContainerStatisticsDto? _statistics;
    private string _selectedStatus = "Todos";
    private string _selectedRuntime = "Todos";

    /// <summary>
    /// Estatísticas dos containers
    /// </summary>
    public ContainerStatisticsDto? Statistics
    {
        get => _statistics;
        set => this.RaiseAndSetIfChanged(ref _statistics, value);
    }

    /// <summary>
    /// Status selecionado para filtro
    /// </summary>
    public string SelectedStatus
    {
        get => _selectedStatus;
        set => this.RaiseAndSetIfChanged(ref _selectedStatus, value);
    }

    /// <summary>
    /// Runtime selecionado para filtro
    /// </summary>
    public string SelectedRuntime
    {
        get => _selectedRuntime;
        set => this.RaiseAndSetIfChanged(ref _selectedRuntime, value);
    }

    /// <summary>
    /// Lista de status disponíveis para filtro
    /// </summary>
    public ObservableCollection<string> StatusOptions { get; } = new()
    {
        "Todos",
        "Created",
        "Running", 
        "Paused",
        "Restarting",
        "Removing",
        "Exited",
        "Dead"
    };

    /// <summary>
    /// Lista de runtimes disponíveis para filtro
    /// </summary>
    public ObservableCollection<string> RuntimeOptions { get; } = new()
    {
        "Todos",
        "Docker"
    };

    /// <summary>
    /// Comando para criar novo container
    /// </summary>
    public ReactiveCommand<Unit, Unit> CreateContainerCommand { get; }

    /// <summary>
    /// Comando para editar container selecionado
    /// </summary>
    public ReactiveCommand<Unit, Unit> EditContainerCommand { get; }

    /// <summary>
    /// Comando para deletar container selecionado
    /// </summary>
    public ReactiveCommand<Unit, Unit> DeleteContainerCommand { get; }

    /// <summary>
    /// Comando para iniciar container selecionado
    /// </summary>
    public ReactiveCommand<Unit, Unit> StartContainerCommand { get; }

    /// <summary>
    /// Comando para parar container selecionado
    /// </summary>
    public ReactiveCommand<Unit, Unit> StopContainerCommand { get; }

    /// <summary>
    /// Comando para pausar container selecionado
    /// </summary>
    public ReactiveCommand<Unit, Unit> PauseContainerCommand { get; }

    /// <summary>
    /// Comando para despausar container selecionado
    /// </summary>
    public ReactiveCommand<Unit, Unit> UnpauseContainerCommand { get; }

    /// <summary>
    /// Comando para reiniciar container selecionado
    /// </summary>
    public ReactiveCommand<Unit, Unit> RestartContainerCommand { get; }

    /// <summary>
    /// Comando para carregar estatísticas
    /// </summary>
    public ReactiveCommand<Unit, Unit> LoadStatisticsCommand { get; }

    /// <summary>
    /// Construtor
    /// </summary>
    /// <param name="mediator">Mediator para CQRS</param>
    /// <param name="logger">Logger</param>
    public ContainerViewModel(IMediator mediator, ILogger<ContainerViewModel> logger) : base(logger)
    {
        _mediator = mediator ?? throw new ArgumentNullException(nameof(mediator));
        Title = "Gerenciamento de Containers";

        // Observables para habilitar/desabilitar comandos baseado na seleção
        var hasSelection = this.WhenAnyValue(x => x.SelectedItem).Select(item => item != null);
        var canStart = this.WhenAnyValue(x => x.SelectedItem)
            .Select(item => item != null && (item.Status == "Created" || item.Status == "Exited"));
        var canStop = this.WhenAnyValue(x => x.SelectedItem)
            .Select(item => item != null && (item.Status == "Running" || item.Status == "Paused"));
        var canPause = this.WhenAnyValue(x => x.SelectedItem)
            .Select(item => item != null && item.Status == "Running");
        var canUnpause = this.WhenAnyValue(x => x.SelectedItem)
            .Select(item => item != null && item.Status == "Paused");

        // Comandos de gerenciamento
        CreateContainerCommand = CreateCommand(CreateContainerAsync, errorMessage: "Erro ao criar container");
        EditContainerCommand = CreateCommand(EditContainerAsync, hasSelection, "Erro ao editar container");
        DeleteContainerCommand = CreateCommand(DeleteContainerAsync, hasSelection, "Erro ao deletar container");

        // Comandos de controle
        StartContainerCommand = CreateCommand(StartContainerAsync, canStart, "Erro ao iniciar container");
        StopContainerCommand = CreateCommand(StopContainerAsync, canStop, "Erro ao parar container");
        PauseContainerCommand = CreateCommand(PauseContainerAsync, canPause, "Erro ao pausar container");
        UnpauseContainerCommand = CreateCommand(UnpauseContainerAsync, canUnpause, "Erro ao despausar container");
        RestartContainerCommand = CreateCommand(RestartContainerAsync, canStop, "Erro ao reiniciar container");

        // Comando para estatísticas
        LoadStatisticsCommand = CreateCommand(LoadStatisticsAsync, errorMessage: "Erro ao carregar estatísticas");

        // Observar mudanças nos filtros para recarregar automaticamente
        this.WhenAnyValue(x => x.SelectedStatus, x => x.SelectedRuntime)
            .Throttle(TimeSpan.FromMilliseconds(300))
            .ObserveOn(RxApp.MainThreadScheduler)
            .Subscribe(_ => RefreshCommand.Execute().Subscribe());
    }

    /// <summary>
    /// Carrega os containers
    /// </summary>
    /// <returns>Task</returns>
    protected override async Task LoadItemsAsync()
    {
        try
        {
            var query = new GetContainersPagedQuery("System", CurrentPage, PageSize);

            // Aplicar filtros se necessário
            if (SelectedStatus != "Todos" && Enum.TryParse<ContainerStatus>(SelectedStatus, out var status))
            {
                query.StatusFilter = status;
            }

            if (SelectedRuntime != "Todos" && Enum.TryParse<ContainerRuntime>(SelectedRuntime, out var runtime))
            {
                query.RuntimeFilter = runtime;
            }

            PagedContainerResultDto result;

            if (!string.IsNullOrWhiteSpace(SearchText))
            {
                var searchQuery = new SearchContainersQuery("System", SearchText, CurrentPage, PageSize);
                if (query.StatusFilter.HasValue) searchQuery.StatusFilter = query.StatusFilter;
                if (query.RuntimeFilter.HasValue) searchQuery.RuntimeFilter = query.RuntimeFilter;
                
                result = await _mediator.Send(searchQuery);
            }
            else
            {
                result = await _mediator.Send(query);
            }

            Items.Clear();
            foreach (var item in result.Items)
            {
                Items.Add(item);
            }

            UpdatePagination(result.TotalCount, result.PageNumber, result.PageSize);

            _logger.LogDebug("Carregados {Count} containers da página {Page}", result.Items.Count, result.PageNumber);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao carregar containers");
            SetError(ex, "Erro ao carregar lista de containers");
        }
    }

    /// <summary>
    /// Carrega estatísticas dos containers
    /// </summary>
    /// <returns>Task</returns>
    private async Task LoadStatisticsAsync()
    {
        try
        {
            var query = new GetContainerStatisticsQuery("System");
            Statistics = await _mediator.Send(query);
            
            _logger.LogDebug("Estatísticas carregadas: {Total} containers totais", Statistics?.TotalContainers ?? 0);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao carregar estatísticas");
            SetError(ex, "Erro ao carregar estatísticas dos containers");
        }
    }

    /// <summary>
    /// Cria um novo container
    /// </summary>
    /// <returns>Task</returns>
    private async Task CreateContainerAsync()
    {
        // TODO: Implementar dialog/view para criação de container
        _logger.LogInformation("Solicitação para criar novo container");
        
        // Por enquanto, apenas um exemplo
        var command = new CreateContainerCommand("System")
        {
            Name = $"test-container-{DateTime.Now:yyyyMMdd-HHmmss}",
            ImageName = "nginx",
            ImageTag = "latest",
            Runtime = ContainerRuntime.Docker
        };

        try
        {
            var result = await _mediator.Send(command);
            _logger.LogInformation("Container criado com sucesso: {ContainerId}", result.Id);
            
            // Recarregar lista
            await LoadItemsAsync();
            await LoadStatisticsAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao criar container");
            throw;
        }
    }

    /// <summary>
    /// Edita o container selecionado
    /// </summary>
    /// <returns>Task</returns>
    private async Task EditContainerAsync()
    {
        if (SelectedItem == null) return;

        // TODO: Implementar dialog/view para edição de container
        _logger.LogInformation("Solicitação para editar container {ContainerId}", SelectedItem.Id);
    }

    /// <summary>
    /// Deleta o container selecionado
    /// </summary>
    /// <returns>Task</returns>
    private async Task DeleteContainerAsync()
    {
        if (SelectedItem == null) return;

        // TODO: Implementar confirmação
        var command = new DeleteContainerCommand(SelectedItem.Id, "System");

        try
        {
            await _mediator.Send(command);
            _logger.LogInformation("Container deletado com sucesso: {ContainerId}", SelectedItem.Id);
            
            // Recarregar lista
            await LoadItemsAsync();
            await LoadStatisticsAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao deletar container {ContainerId}", SelectedItem.Id);
            throw;
        }
    }

    /// <summary>
    /// Inicia o container selecionado
    /// </summary>
    /// <returns>Task</returns>
    private async Task StartContainerAsync()
    {
        if (SelectedItem == null) return;

        var command = new StartContainerCommand(SelectedItem.Id, "System");

        try
        {
            await _mediator.Send(command);
            _logger.LogInformation("Container iniciado com sucesso: {ContainerId}", SelectedItem.Id);
            
            // Recarregar lista
            await LoadItemsAsync();
            await LoadStatisticsAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao iniciar container {ContainerId}", SelectedItem.Id);
            throw;
        }
    }

    /// <summary>
    /// Para o container selecionado
    /// </summary>
    /// <returns>Task</returns>
    private async Task StopContainerAsync()
    {
        if (SelectedItem == null) return;

        var command = new StopContainerCommand(SelectedItem.Id, "System");

        try
        {
            await _mediator.Send(command);
            _logger.LogInformation("Container parado com sucesso: {ContainerId}", SelectedItem.Id);
            
            // Recarregar lista
            await LoadItemsAsync();
            await LoadStatisticsAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao parar container {ContainerId}", SelectedItem.Id);
            throw;
        }
    }

    /// <summary>
    /// Pausa o container selecionado
    /// </summary>
    /// <returns>Task</returns>
    private async Task PauseContainerAsync()
    {
        if (SelectedItem == null) return;

        var command = new PauseContainerCommand(SelectedItem.Id, "System");

        try
        {
            await _mediator.Send(command);
            _logger.LogInformation("Container pausado com sucesso: {ContainerId}", SelectedItem.Id);
            
            // Recarregar lista
            await LoadItemsAsync();
            await LoadStatisticsAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao pausar container {ContainerId}", SelectedItem.Id);
            throw;
        }
    }

    /// <summary>
    /// Despausa o container selecionado
    /// </summary>
    /// <returns>Task</returns>
    private async Task UnpauseContainerAsync()
    {
        if (SelectedItem == null) return;

        var command = new UnpauseContainerCommand(SelectedItem.Id, "System");

        try
        {
            await _mediator.Send(command);
            _logger.LogInformation("Container despausado com sucesso: {ContainerId}", SelectedItem.Id);
            
            // Recarregar lista
            await LoadItemsAsync();
            await LoadStatisticsAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao despausar container {ContainerId}", SelectedItem.Id);
            throw;
        }
    }

    /// <summary>
    /// Reinicia o container selecionado
    /// </summary>
    /// <returns>Task</returns>
    private async Task RestartContainerAsync()
    {
        if (SelectedItem == null) return;

        var command = new RestartContainerCommand(SelectedItem.Id, "System");

        try
        {
            await _mediator.Send(command);
            _logger.LogInformation("Container reiniciado com sucesso: {ContainerId}", SelectedItem.Id);
            
            // Recarregar lista
            await LoadItemsAsync();
            await LoadStatisticsAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao reiniciar container {ContainerId}", SelectedItem.Id);
            throw;
        }
    }

    /// <summary>
    /// Inicialização do ViewModel
    /// </summary>
    /// <returns>Task</returns>
    public override async Task InitializeAsync()
    {
        await base.InitializeAsync();
        
        // Carregar dados iniciais
        await LoadItemsAsync();
        await LoadStatisticsAsync();
    }

    /// <summary>
    /// Limpeza de recursos
    /// </summary>
    public override void Cleanup()
    {
        base.Cleanup();
        Items.Clear();
        Statistics = null;
    }
}
