using {{RootNamespace}}.Core.Modules.{{ModuleName}}.Interfaces;
using {{RootNamespace}}.Core.Common;
using {{RootNamespace}}.Infrastructure.Common;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text.Json;
using System.Net.Http;

namespace {{RootNamespace}}.Infrastructure.Modules.{{ModuleName}}.Services;

/// <summary>
/// Serviço externo para {{EntityName}}
/// Implementa comunicação com APIs externas ou serviços de terceiros
/// </summary>
public class {{EntityName}}ExternalService : I{{EntityName}}ExternalService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<{{EntityName}}ExternalService> _logger;
    private readonly {{EntityName}}ExternalServiceOptions _options;
    private readonly JsonSerializerOptions _jsonOptions;

    public {{EntityName}}ExternalService(
        HttpClient httpClient,
        ILogger<{{EntityName}}ExternalService> logger,
        IOptions<{{EntityName}}ExternalServiceOptions> options)
    {
        _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        };

        ConfigureHttpClient();
    }

    /// <summary>
    /// Configura o HttpClient com headers e configurações padrão
    /// </summary>
    private void ConfigureHttpClient()
    {
        _httpClient.BaseAddress = new Uri(_options.BaseUrl);
        _httpClient.DefaultRequestHeaders.Add("User-Agent", "AutoInstaller/1.0");
        
        if (!string.IsNullOrEmpty(_options.ApiKey))
        {
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_options.ApiKey}");
        }

        _httpClient.Timeout = TimeSpan.FromSeconds(_options.TimeoutSeconds);
    }

    /// <summary>
    /// Busca dados externos para {{EntityName}}
    /// </summary>
    /// <param name="identifier">Identificador para busca</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Dados externos ou null se não encontrado</returns>
    public async Task<{{EntityName}}ExternalData?> GetExternalDataAsync(string identifier, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Buscando dados externos para {{EntityName}} com identificador: {Identifier}", identifier);

            var endpoint = $"{_options.DataEndpoint}/{identifier}";
            var response = await _httpClient.GetAsync(endpoint, cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                var data = JsonSerializer.Deserialize<{{EntityName}}ExternalData>(content, _jsonOptions);
                
                _logger.LogInformation("Dados externos obtidos com sucesso para {{EntityName}}: {Identifier}", identifier);
                return data;
            }
            else if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                _logger.LogWarning("Dados externos não encontrados para {{EntityName}}: {Identifier}", identifier);
                return null;
            }
            else
            {
                _logger.LogError("Erro ao buscar dados externos para {{EntityName}}: {Identifier}. Status: {StatusCode}", 
                    identifier, response.StatusCode);
                throw new ExternalServiceException($"Erro ao buscar dados externos: {response.StatusCode}");
            }
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "Erro de rede ao buscar dados externos para {{EntityName}}: {Identifier}", identifier);
            throw new ExternalServiceException("Erro de conectividade com serviço externo", ex);
        }
        catch (TaskCanceledException ex)
        {
            _logger.LogError(ex, "Timeout ao buscar dados externos para {{EntityName}}: {Identifier}", identifier);
            throw new ExternalServiceException("Timeout na comunicação com serviço externo", ex);
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "Erro ao deserializar dados externos para {{EntityName}}: {Identifier}", identifier);
            throw new ExternalServiceException("Erro no formato dos dados externos", ex);
        }
    }

    /// <summary>
    /// Envia dados para serviço externo
    /// </summary>
    /// <param name="data">Dados a serem enviados</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Resultado da operação</returns>
    public async Task<{{EntityName}}ExternalResult> SendDataAsync({{EntityName}}ExternalData data, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Enviando dados para serviço externo: {{EntityName}}");

            var json = JsonSerializer.Serialize(data, _jsonOptions);
            var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync(_options.SubmitEndpoint, content, cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
                var result = JsonSerializer.Deserialize<{{EntityName}}ExternalResult>(responseContent, _jsonOptions);
                
                _logger.LogInformation("Dados enviados com sucesso para serviço externo: {{EntityName}}");
                return result;
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogError("Erro ao enviar dados para serviço externo: {{EntityName}}. Status: {StatusCode}, Error: {Error}", 
                    response.StatusCode, errorContent);
                throw new ExternalServiceException($"Erro ao enviar dados: {response.StatusCode} - {errorContent}");
            }
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "Erro de rede ao enviar dados para serviço externo: {{EntityName}}");
            throw new ExternalServiceException("Erro de conectividade com serviço externo", ex);
        }
        catch (TaskCanceledException ex)
        {
            _logger.LogError(ex, "Timeout ao enviar dados para serviço externo: {{EntityName}}");
            throw new ExternalServiceException("Timeout na comunicação com serviço externo", ex);
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "Erro ao serializar/deserializar dados para serviço externo: {{EntityName}}");
            throw new ExternalServiceException("Erro no formato dos dados", ex);
        }
    }

    /// <summary>
    /// Valida dados com serviço externo
    /// </summary>
    /// <param name="identifier">Identificador para validação</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Resultado da validação</returns>
    public async Task<bool> ValidateAsync(string identifier, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Validando {{EntityName}} com serviço externo: {Identifier}", identifier);

            var endpoint = $"{_options.ValidationEndpoint}/{identifier}";
            var response = await _httpClient.GetAsync(endpoint, cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                var validationResult = JsonSerializer.Deserialize<ValidationResult>(content, _jsonOptions);
                
                _logger.LogInformation("Validação concluída para {{EntityName}}: {Identifier}, Válido: {IsValid}", 
                    identifier, validationResult.IsValid);
                return validationResult.IsValid;
            }
            else
            {
                _logger.LogWarning("Erro na validação de {{EntityName}}: {Identifier}. Status: {StatusCode}", 
                    identifier, response.StatusCode);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao validar {{EntityName}} com serviço externo: {Identifier}", identifier);
            return false;
        }
    }

    /// <summary>
    /// Verifica o status de saúde do serviço externo
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se o serviço está saudável</returns>
    public async Task<bool> HealthCheckAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Verificando saúde do serviço externo para {{EntityName}}");

            var response = await _httpClient.GetAsync(_options.HealthEndpoint, cancellationToken);
            var isHealthy = response.IsSuccessStatusCode;
            
            _logger.LogDebug("Status de saúde do serviço externo para {{EntityName}}: {IsHealthy}", isHealthy);
            return isHealthy;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao verificar saúde do serviço externo para {{EntityName}}");
            return false;
        }
    }
}

/// <summary>
/// Configurações para o serviço externo de {{EntityName}}
/// </summary>
public class {{EntityName}}ExternalServiceOptions
{
    public const string SectionName = "{{EntityName}}ExternalService";

    public string BaseUrl { get; set; } = string.Empty;
    public string ApiKey { get; set; } = string.Empty;
    public string DataEndpoint { get; set; } = "/api/{{LowerEntityName}}/data";
    public string SubmitEndpoint { get; set; } = "/api/{{LowerEntityName}}/submit";
    public string ValidationEndpoint { get; set; } = "/api/{{LowerEntityName}}/validate";
    public string HealthEndpoint { get; set; } = "/health";
    public int TimeoutSeconds { get; set; } = 30;
    public int RetryAttempts { get; set; } = 3;
    public int RetryDelaySeconds { get; set; } = 2;
}

/// <summary>
/// Dados externos para {{EntityName}}
/// </summary>
public class {{EntityName}}ExternalData
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Dictionary<string, object> Properties { get; set; } = new();
    public DateTime LastUpdated { get; set; }
}

/// <summary>
/// Resultado de operação externa para {{EntityName}}
/// </summary>
public class {{EntityName}}ExternalResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string TransactionId { get; set; } = string.Empty;
    public Dictionary<string, object> Data { get; set; } = new();
}

/// <summary>
/// Resultado de validação
/// </summary>
public class ValidationResult
{
    public bool IsValid { get; set; }
    public string Message { get; set; } = string.Empty;
    public List<string> Errors { get; set; } = new();
}