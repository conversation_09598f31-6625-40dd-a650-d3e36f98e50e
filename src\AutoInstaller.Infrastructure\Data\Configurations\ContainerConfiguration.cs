using AutoInstaller.Core.Entities;
using AutoInstaller.Core.ValueObjects;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System.Text.Json;

namespace AutoInstaller.Infrastructure.Data.Configurations;

/// <summary>
/// Configuração da entidade Container para Entity Framework
/// </summary>
public class ContainerConfiguration : IEntityTypeConfiguration<Container>
{
    /// <summary>
    /// Configura a entidade Container
    /// </summary>
    /// <param name="builder">Entity type builder</param>
    public void Configure(EntityTypeBuilder<Container> builder)
    {
        // Configuração da tabela
        builder.ToTable("Containers");

        // Chave primária
        builder.HasKey(c => c.Id);

        // Configuração de propriedades básicas
        ConfigureBasicProperties(builder);

        // Configuração de Value Objects
        ConfigureValueObjects(builder);

        // Configuração de enums
        ConfigureEnums(builder);

        // Configuração de coleções
        ConfigureCollections(builder);

        // Configuração de propriedades de auditoria
        ConfigureAuditProperties(builder);

        // Configuração de índices
        ConfigureIndexes(builder);
    }

    /// <summary>
    /// Configura propriedades básicas
    /// </summary>
    /// <param name="builder">Entity type builder</param>
    private static void ConfigureBasicProperties(EntityTypeBuilder<Container> builder)
    {
        builder.Property(c => c.Id)
            .IsRequired()
            .ValueGeneratedNever(); // Guid gerado pela aplicação

        builder.Property(c => c.ImageName)
            .IsRequired()
            .HasMaxLength(255)
            .HasComment("Nome da imagem Docker/Podman");

        builder.Property(c => c.RuntimeId)
            .HasMaxLength(100)
            .HasComment("ID do container no runtime (Docker/Podman)");

        builder.Property(c => c.Command)
            .HasMaxLength(500)
            .HasComment("Comando executado no container");

        builder.Property(c => c.WorkingDirectory)
            .HasMaxLength(500)
            .HasComment("Diretório de trabalho do container");

        builder.Property(c => c.User)
            .HasMaxLength(100)
            .HasComment("Usuário que executa o container");

        builder.Property(c => c.RestartPolicy)
            .IsRequired()
            .HasMaxLength(50)
            .HasDefaultValue("no")
            .HasComment("Política de reinicialização do container");

        builder.Property(c => c.AutoRestart)
            .IsRequired()
            .HasDefaultValue(false)
            .HasComment("Indica se o container deve reiniciar automaticamente");

        builder.Property(c => c.MemoryLimit)
            .HasComment("Limite de memória em bytes");

        builder.Property(c => c.CpuLimit)
            .HasColumnType("decimal(5,2)")
            .HasComment("Limite de CPU (número de cores)");

        builder.Property(c => c.StartedAt)
            .HasComment("Data e hora de início do container");

        builder.Property(c => c.StoppedAt)
            .HasComment("Data e hora de parada do container");
    }

    /// <summary>
    /// Configura Value Objects
    /// </summary>
    /// <param name="builder">Entity type builder</param>
    private static void ConfigureValueObjects(EntityTypeBuilder<Container> builder)
    {
        // ContainerName
        builder.Property(c => c.Name)
            .HasConversion(
                name => name.Value,
                value => ContainerName.Create(value))
            .IsRequired()
            .HasMaxLength(253)
            .HasComment("Nome do container");

        // ImageTag
        builder.Property(c => c.ImageTag)
            .HasConversion(
                tag => tag.Value,
                value => ImageTag.Create(value))
            .IsRequired()
            .HasMaxLength(128)
            .HasDefaultValue("latest")
            .HasComment("Tag da imagem");
    }

    /// <summary>
    /// Configura enums
    /// </summary>
    /// <param name="builder">Entity type builder</param>
    private static void ConfigureEnums(EntityTypeBuilder<Container> builder)
    {
        // ContainerStatus
        builder.Property(c => c.Status)
            .HasConversion<string>()
            .IsRequired()
            .HasMaxLength(20)
            .HasDefaultValue(ContainerStatus.Created)
            .HasComment("Status atual do container");

        // ContainerRuntime
        builder.Property(c => c.Runtime)
            .HasConversion<string>()
            .IsRequired()
            .HasMaxLength(20)
            .HasDefaultValue(ContainerRuntime.Docker)
            .HasComment("Runtime utilizado (Docker)");
    }

    /// <summary>
    /// Configura coleções
    /// </summary>
    /// <param name="builder">Entity type builder</param>
    private static void ConfigureCollections(EntityTypeBuilder<Container> builder)
    {
        // Arguments - array de strings como JSON
        builder.Property(c => c.Arguments)
            .HasConversion(
                args => args != null ? JsonSerializer.Serialize(args, (JsonSerializerOptions?)null) : null,
                json => string.IsNullOrEmpty(json) ? null : JsonSerializer.Deserialize<string[]>(json, (JsonSerializerOptions?)null))
            .HasColumnType("TEXT")
            .HasComment("Argumentos do comando em formato JSON");

        // ExposedPorts - lista de portas como JSON
        builder.Property("_exposedPortsJson")
            .HasColumnName("ExposedPorts")
            .HasConversion(
                ports => JsonSerializer.Serialize(
                    ports.Select(p => new { Number = p.Number, Protocol = p.Protocol.ToString() }), 
                    (JsonSerializerOptions?)null),
                json => JsonSerializer.Deserialize<List<dynamic>>(json, (JsonSerializerOptions?)null)
                    .Select(p => Port.Create(
                        Convert.ToInt32(p.GetProperty("Number").GetInt32()), 
                        Enum.Parse<PortProtocol>(p.GetProperty("Protocol").GetString())))
                    .ToList())
            .HasColumnType("TEXT")
            .HasComment("Portas expostas em formato JSON");

        // EnvironmentVariables - dicionário como JSON
        builder.Property("_environmentVariablesJson")
            .HasColumnName("EnvironmentVariables")
            .HasConversion(
                envVars => JsonSerializer.Serialize(envVars, (JsonSerializerOptions?)null),
                json => JsonSerializer.Deserialize<Dictionary<string, string>>(json, (JsonSerializerOptions?)null) ?? new Dictionary<string, string>())
            .HasColumnType("TEXT")
            .HasComment("Variáveis de ambiente em formato JSON");

        // Labels - dicionário como JSON
        builder.Property("_labelsJson")
            .HasColumnName("Labels")
            .HasConversion(
                labels => JsonSerializer.Serialize(labels, (JsonSerializerOptions?)null),
                json => JsonSerializer.Deserialize<Dictionary<string, string>>(json, (JsonSerializerOptions?)null) ?? new Dictionary<string, string>())
            .HasColumnType("TEXT")
            .HasComment("Labels do container em formato JSON");

        // Volumes - lista de strings como JSON
        builder.Property("_volumesJson")
            .HasColumnName("Volumes")
            .HasConversion(
                volumes => JsonSerializer.Serialize(volumes, (JsonSerializerOptions?)null),
                json => JsonSerializer.Deserialize<List<string>>(json, (JsonSerializerOptions?)null) ?? new List<string>())
            .HasColumnType("TEXT")
            .HasComment("Volumes montados em formato JSON");

        // Ignorar propriedades de navegação das coleções (são propriedades calculadas)
        builder.Ignore(c => c.ExposedPorts);
        builder.Ignore(c => c.EnvironmentVariables);
        builder.Ignore(c => c.Labels);
        builder.Ignore(c => c.Volumes);
        builder.Ignore(c => c.DomainEvents);
    }

    /// <summary>
    /// Configura propriedades de auditoria
    /// </summary>
    /// <param name="builder">Entity type builder</param>
    private static void ConfigureAuditProperties(EntityTypeBuilder<Container> builder)
    {
        builder.Property(c => c.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("datetime('now')")
            .HasComment("Data de criação do registro");

        builder.Property(c => c.UpdatedAt)
            .HasComment("Data da última atualização do registro");

        builder.Property(c => c.CreatedBy)
            .IsRequired()
            .HasMaxLength(100)
            .HasComment("Usuário que criou o registro");

        builder.Property(c => c.UpdatedBy)
            .HasMaxLength(100)
            .HasComment("Usuário que fez a última atualização");

        builder.Property(c => c.IsDeleted)
            .IsRequired()
            .HasDefaultValue(false)
            .HasComment("Indica se o registro foi excluído (soft delete)");
    }

    /// <summary>
    /// Configura índices
    /// </summary>
    /// <param name="builder">Entity type builder</param>
    private static void ConfigureIndexes(EntityTypeBuilder<Container> builder)
    {
        // Índice único para nome do container (apenas não excluídos)
        builder.HasIndex(c => c.Name)
            .IsUnique()
            .HasFilter("IsDeleted = 0")
            .HasDatabaseName("IX_Containers_Name_Unique");

        // Índice para RuntimeId
        builder.HasIndex(c => c.RuntimeId)
            .HasDatabaseName("IX_Containers_RuntimeId");

        // Índice para Status
        builder.HasIndex(c => c.Status)
            .HasDatabaseName("IX_Containers_Status");

        // Índice para Runtime
        builder.HasIndex(c => c.Runtime)
            .HasDatabaseName("IX_Containers_Runtime");

        // Índice para ImageName
        builder.HasIndex(c => c.ImageName)
            .HasDatabaseName("IX_Containers_ImageName");

        // Índice composto para ImageName + ImageTag
        builder.HasIndex(c => new { c.ImageName, c.ImageTag })
            .HasDatabaseName("IX_Containers_Image");

        // Índice para CreatedBy
        builder.HasIndex(c => c.CreatedBy)
            .HasDatabaseName("IX_Containers_CreatedBy");

        // Índice para CreatedAt
        builder.HasIndex(c => c.CreatedAt)
            .HasDatabaseName("IX_Containers_CreatedAt");

        // Índice para IsDeleted (para soft delete)
        builder.HasIndex(c => c.IsDeleted)
            .HasDatabaseName("IX_Containers_IsDeleted");

        // Índice composto para consultas comuns
        builder.HasIndex(c => new { c.Status, c.Runtime, c.IsDeleted })
            .HasDatabaseName("IX_Containers_Status_Runtime_IsDeleted");
    }
}

/// <summary>
/// Configuração base para entidades
/// </summary>
public abstract class BaseEntityConfiguration<T> : IEntityTypeConfiguration<T> where T : BaseEntity
{
    /// <summary>
    /// Configura a entidade
    /// </summary>
    /// <param name="builder">Entity type builder</param>
    public virtual void Configure(EntityTypeBuilder<T> builder)
    {
        // Configuração padrão para todas as entidades
        builder.HasKey(e => e.Id);

        builder.Property(e => e.Id)
            .IsRequired()
            .ValueGeneratedNever();

        builder.Property(e => e.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("datetime('now')");

        builder.Property(e => e.UpdatedAt);

        builder.Property(e => e.CreatedBy)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(e => e.UpdatedBy)
            .HasMaxLength(100);

        builder.Property(e => e.IsDeleted)
            .IsRequired()
            .HasDefaultValue(false);

        // Índices padrão
        builder.HasIndex(e => e.CreatedAt);
        builder.HasIndex(e => e.IsDeleted);
        builder.HasIndex(e => e.CreatedBy);

        // Ignorar eventos de domínio
        builder.Ignore(e => e.DomainEvents);
    }
}
