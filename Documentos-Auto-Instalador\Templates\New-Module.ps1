<#
.SYNOPSIS
    Script para geração automática de módulos usando templates

.DESCRIPTION
    Este script gera automaticamente a estrutura de um módulo completo
    baseado nos templates definidos, substituindo variáveis e criando
    todos os arquivos necessários para um módulo funcional.

.PARAMETER ModuleName
    Nome do módulo a ser criado (ex: "UserManagement")

.PARAMETER EntityName
    Nome da entidade principal do módulo (ex: "User")

.PARAMETER RootNamespace
    Namespace raiz do projeto (ex: "AutoInstaller")

.PARAMETER OutputPath
    Caminho onde o módulo será criado

.PARAMETER TemplatesPath
    Caminho para a pasta de templates

.PARAMETER IncludeLayers
    Camadas a serem incluídas (Application, Domain, Infrastructure, UI)

.EXAMPLE
    .\New-Module.ps1 -ModuleName "UserManagement" -EntityName "User" -RootNamespace "AutoInstaller" -OutputPath "C:\Project\Modules"

.EXAMPLE
    .\New-Module.ps1 -ModuleName "ContainerManagement" -EntityName "Container" -RootNamespace "AutoInstaller" -OutputPath "C:\Project\Modules" -IncludeLayers @("Application", "Domain", "Infrastructure")
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$ModuleName,
    
    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$EntityName,
    
    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$RootNamespace,
    
    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$OutputPath,
    
    [Parameter(Mandatory = $false)]
    [string]$TemplatesPath = "./Templates",
    
    [Parameter(Mandatory = $false)]
    [ValidateSet("Application", "Domain", "Infrastructure", "UI")]
    [string[]]$IncludeLayers = @("Application", "Domain", "Infrastructure", "UI"),
    
    [Parameter(Mandatory = $false)]
    [switch]$Force,
    
    [Parameter(Mandatory = $false)]
    [switch]$Verbose
)

# Configurações
$ErrorActionPreference = "Stop"
$VerbosePreference = if ($Verbose) { "Continue" } else { "SilentlyContinue" }

# Variáveis globais
$script:TemplateVariables = @{}
$script:CreatedFiles = @()
$script:CreatedDirectories = @()

#region Funções Auxiliares

function Write-Log {
    param(
        [string]$Message,
        [ValidateSet("Info", "Warning", "Error", "Success")]
        [string]$Level = "Info"
    )
    
    $timestamp = Get-Date -Format "HH:mm:ss"
    $color = switch ($Level) {
        "Info" { "White" }
        "Warning" { "Yellow" }
        "Error" { "Red" }
        "Success" { "Green" }
    }
    
    Write-Host "[$timestamp] " -NoNewline -ForegroundColor Gray
    Write-Host "[$Level] " -NoNewline -ForegroundColor $color
    Write-Host $Message
}

function Initialize-TemplateVariables {
    Write-Verbose "Inicializando variáveis de template..."
    
    $script:TemplateVariables = @{
        "{{RootNamespace}}" = $RootNamespace
        "{{ModuleName}}" = $ModuleName
        "{{EntityName}}" = $EntityName
        "{{LowerEntityName}}" = $EntityName.ToLower()
        "{{UpperEntityName}}" = $EntityName.ToUpper()
        "{{PluralEntityName}}" = Get-PluralForm $EntityName
        "{{LowerPluralEntityName}}" = (Get-PluralForm $EntityName).ToLower()
        "{{CamelCaseEntityName}}" = $EntityName.Substring(0,1).ToLower() + $EntityName.Substring(1)
        "{{KebabCaseEntityName}}" = Convert-ToKebabCase $EntityName
        "{{SnakeCaseEntityName}}" = Convert-ToSnakeCase $EntityName
        "{{CurrentYear}}" = (Get-Date).Year
        "{{CurrentDate}}" = Get-Date -Format "yyyy-MM-dd"
        "{{CurrentDateTime}}" = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        "{{GeneratedBy}}" = "New-Module.ps1"
        "{{Version}}" = "1.0.0"
    }
    
    Write-Verbose "Variáveis de template inicializadas: $($script:TemplateVariables.Count) variáveis"
}

function Get-PluralForm {
    param([string]$Word)
    
    # Regras simples de pluralização em inglês
    if ($Word.EndsWith("y")) {
        return $Word.Substring(0, $Word.Length - 1) + "ies"
    }
    elseif ($Word.EndsWith("s") -or $Word.EndsWith("sh") -or $Word.EndsWith("ch") -or $Word.EndsWith("x") -or $Word.EndsWith("z")) {
        return $Word + "es"
    }
    else {
        return $Word + "s"
    }
}

function Convert-ToKebabCase {
    param([string]$Text)
    return ($Text -creplace '([A-Z])', '-$1').ToLower().TrimStart('-')
}

function Convert-ToSnakeCase {
    param([string]$Text)
    return ($Text -creplace '([A-Z])', '_$1').ToLower().TrimStart('_')
}

function Test-Prerequisites {
    Write-Log "Verificando pré-requisitos..." "Info"
    
    # Verificar se o caminho de templates existe
    if (-not (Test-Path $TemplatesPath)) {
        throw "Caminho de templates não encontrado: $TemplatesPath"
    }
    
    # Verificar se o caminho de saída existe
    if (-not (Test-Path $OutputPath)) {
        Write-Log "Criando diretório de saída: $OutputPath" "Info"
        New-Item -Path $OutputPath -ItemType Directory -Force | Out-Null
    }
    
    # Verificar se o módulo já existe
    $moduleOutputPath = Join-Path $OutputPath $ModuleName
    if ((Test-Path $moduleOutputPath) -and -not $Force) {
        throw "Módulo '$ModuleName' já existe em '$moduleOutputPath'. Use -Force para sobrescrever."
    }
    
    Write-Log "Pré-requisitos verificados com sucesso" "Success"
}

function Get-TemplateFiles {
    Write-Log "Descobrindo arquivos de template..." "Info"
    
    $templateFiles = @()
    
    foreach ($layer in $IncludeLayers) {
        $layerPath = Join-Path $TemplatesPath "Modules\$layer"
        
        if (Test-Path $layerPath) {
            $files = Get-ChildItem -Path $layerPath -File -Recurse
            foreach ($file in $files) {
                $templateFiles += @{
                    SourcePath = $file.FullName
                    RelativePath = $file.FullName.Substring($layerPath.Length + 1)
                    Layer = $layer
                    FileName = $file.Name
                }
            }
        }
        else {
            Write-Log "Camada '$layer' não encontrada em '$layerPath'" "Warning"
        }
    }
    
    Write-Log "Encontrados $($templateFiles.Count) arquivos de template" "Info"
    return $templateFiles
}

function New-ModuleStructure {
    Write-Log "Criando estrutura do módulo '$ModuleName'..." "Info"
    
    $moduleBasePath = Join-Path $OutputPath $ModuleName
    
    # Criar diretório base do módulo
    if (Test-Path $moduleBasePath) {
        if ($Force) {
            Write-Log "Removendo módulo existente..." "Warning"
            Remove-Item -Path $moduleBasePath -Recurse -Force
        }
    }
    
    New-Item -Path $moduleBasePath -ItemType Directory -Force | Out-Null
    $script:CreatedDirectories += $moduleBasePath
    
    # Criar estrutura de diretórios para cada camada
    foreach ($layer in $IncludeLayers) {
        $layerPath = Join-Path $moduleBasePath $layer
        New-Item -Path $layerPath -ItemType Directory -Force | Out-Null
        $script:CreatedDirectories += $layerPath
        
        # Criar subdiretórios específicos da camada
        switch ($layer) {
            "Application" {
                @("Commands", "Queries", "Handlers", "Validators", "Mappers", "DTOs") | ForEach-Object {
                    $subPath = Join-Path $layerPath $_
                    New-Item -Path $subPath -ItemType Directory -Force | Out-Null
                    $script:CreatedDirectories += $subPath
                }
            }
            "Domain" {
                @("Entities", "ValueObjects", "Services", "Interfaces", "Events") | ForEach-Object {
                    $subPath = Join-Path $layerPath $_
                    New-Item -Path $subPath -ItemType Directory -Force | Out-Null
                    $script:CreatedDirectories += $subPath
                }
            }
            "Infrastructure" {
                @("Repositories", "Services", "Configuration", "Data") | ForEach-Object {
                    $subPath = Join-Path $layerPath $_
                    New-Item -Path $subPath -ItemType Directory -Force | Out-Null
                    $script:CreatedDirectories += $subPath
                }
            }
            "UI" {
                @("Views", "ViewModels", "Controls", "Converters", "Styles") | ForEach-Object {
                    $subPath = Join-Path $layerPath $_
                    New-Item -Path $subPath -ItemType Directory -Force | Out-Null
                    $script:CreatedDirectories += $subPath
                }
            }
        }
    }
    
    Write-Log "Estrutura do módulo criada: $($script:CreatedDirectories.Count) diretórios" "Success"
}

function Invoke-TemplateProcessing {
    param(
        [array]$TemplateFiles
    )
    
    Write-Log "Processando templates..." "Info"
    
    foreach ($template in $TemplateFiles) {
        try {
            Write-Verbose "Processando template: $($template.FileName)"
            
            # Ler conteúdo do template
            $content = Get-Content -Path $template.SourcePath -Raw -Encoding UTF8
            
            # Substituir variáveis
            foreach ($variable in $script:TemplateVariables.GetEnumerator()) {
                $content = $content -replace [regex]::Escape($variable.Key), $variable.Value
            }
            
            # Determinar caminho de saída
            $outputFileName = $template.FileName
            foreach ($variable in $script:TemplateVariables.GetEnumerator()) {
                $outputFileName = $outputFileName -replace [regex]::Escape($variable.Key), $variable.Value
            }
            
            # Determinar subdiretório baseado no tipo de arquivo
            $subDirectory = Get-OutputSubDirectory -Layer $template.Layer -FileName $outputFileName
            $outputPath = Join-Path (Join-Path (Join-Path $OutputPath $ModuleName) $template.Layer) $subDirectory
            $outputFilePath = Join-Path $outputPath $outputFileName
            
            # Criar diretório se não existir
            if (-not (Test-Path $outputPath)) {
                New-Item -Path $outputPath -ItemType Directory -Force | Out-Null
                $script:CreatedDirectories += $outputPath
            }
            
            # Escrever arquivo processado
            Set-Content -Path $outputFilePath -Value $content -Encoding UTF8
            $script:CreatedFiles += $outputFilePath
            
            Write-Verbose "Arquivo criado: $outputFilePath"
        }
        catch {
            Write-Log "Erro ao processar template '$($template.FileName)': $($_.Exception.Message)" "Error"
            throw
        }
    }
    
    Write-Log "Templates processados: $($script:CreatedFiles.Count) arquivos criados" "Success"
}

function Get-OutputSubDirectory {
    param(
        [string]$Layer,
        [string]$FileName
    )
    
    switch ($Layer) {
        "Application" {
            if ($FileName -like "*Command*") { return "Commands" }
            if ($FileName -like "*Query*") { return "Queries" }
            if ($FileName -like "*Handler*") { return "Handlers" }
            if ($FileName -like "*Validator*") { return "Validators" }
            if ($FileName -like "*Mapper*") { return "Mappers" }
            if ($FileName -like "*Dto*" -or $FileName -like "*DTO*") { return "DTOs" }
            return ""
        }
        "Domain" {
            if ($FileName -like "*Entity*" -and $FileName -notlike "*Service*") { return "Entities" }
            if ($FileName -like "*ValueObject*") { return "ValueObjects" }
            if ($FileName -like "*Service*") { return "Services" }
            if ($FileName -like "*Interface*" -or $FileName -like "I*.cs") { return "Interfaces" }
            if ($FileName -like "*Event*") { return "Events" }
            return ""
        }
        "Infrastructure" {
            if ($FileName -like "*Repository*") { return "Repositories" }
            if ($FileName -like "*Service*") { return "Services" }
            if ($FileName -like "*Configuration*") { return "Configuration" }
            if ($FileName -like "*Context*" -or $FileName -like "*Data*") { return "Data" }
            return ""
        }
        "UI" {
            if ($FileName -like "*.axaml" -and $FileName -notlike "*ViewModel*") { return "Views" }
            if ($FileName -like "*ViewModel*") { return "ViewModels" }
            if ($FileName -like "*Control*") { return "Controls" }
            if ($FileName -like "*Converter*") { return "Converters" }
            if ($FileName -like "*.xaml" -and $FileName -notlike "*.axaml") { return "Styles" }
            return ""
        }
        default { return "" }
    }
}

function New-ModuleProjectFile {
    Write-Log "Criando arquivo de projeto do módulo..." "Info"
    
    $moduleBasePath = Join-Path $OutputPath $ModuleName
    $projectFilePath = Join-Path $moduleBasePath "$ModuleName.csproj"
    
    $projectContent = @"
<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <RootNamespace>$RootNamespace.Modules.$ModuleName</RootNamespace>
    <AssemblyName>$RootNamespace.Modules.$ModuleName</AssemblyName>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <WarningsNotAsErrors>CS1591</WarningsNotAsErrors>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MediatR" Version="13.0.0" />
<PackageReference Include="FluentValidation" Version="12.0.0" />
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="9.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.0" />
  </ItemGroup>

</Project>
"@
    
    Set-Content -Path $projectFilePath -Value $projectContent -Encoding UTF8
    $script:CreatedFiles += $projectFilePath
    
    Write-Log "Arquivo de projeto criado: $projectFilePath" "Success"
}

function New-ModuleReadme {
    Write-Log "Criando README do módulo..." "Info"
    
    $moduleBasePath = Join-Path $OutputPath $ModuleName
    $readmePath = Join-Path $moduleBasePath "README.md"
    
    $readmeContent = @"
# Módulo $ModuleName

Módulo gerado automaticamente para gerenciamento de $EntityName.

## Estrutura

### Camadas Incluídas
$(($IncludeLayers | ForEach-Object { "- $_" }) -join "`n")

### Arquivos Gerados
$(($script:CreatedFiles | Where-Object { $_ -like "*$ModuleName*" } | ForEach-Object { "- " + (Split-Path $_ -Leaf) }) -join "`n")

## Configuração

1. Adicione referência ao projeto principal
2. Configure injeção de dependência
3. Execute migrações se necessário
4. Configure roteamento (se aplicável)

## Uso

```csharp
// Exemplo de uso do módulo $ModuleName
// TODO: Adicionar exemplos específicos
```

## Gerado em

- **Data**: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
- **Ferramenta**: New-Module.ps1
- **Versão**: 1.0.0

"@
    
    Set-Content -Path $readmePath -Value $readmeContent -Encoding UTF8
    $script:CreatedFiles += $readmePath
    
    Write-Log "README criado: $readmePath" "Success"
}

function Show-Summary {
    Write-Log "" "Info"
    Write-Log "=== RESUMO DA GERAÇÃO DO MÓDULO ===" "Success"
    Write-Log "Módulo: $ModuleName" "Info"
    Write-Log "Entidade: $EntityName" "Info"
    Write-Log "Namespace: $RootNamespace" "Info"
    Write-Log "Camadas: $($IncludeLayers -join ', ')" "Info"
    Write-Log "" "Info"
    Write-Log "Diretórios criados: $($script:CreatedDirectories.Count)" "Info"
    Write-Log "Arquivos criados: $($script:CreatedFiles.Count)" "Info"
    Write-Log "" "Info"
    Write-Log "Localização: $(Join-Path $OutputPath $ModuleName)" "Success"
    Write-Log "" "Info"
    
    if ($Verbose) {
        Write-Log "Arquivos criados:" "Info"
        $script:CreatedFiles | ForEach-Object {
            Write-Log "  - $(Split-Path $_ -Leaf)" "Info"
        }
    }
}

#endregion

#region Execução Principal

try {
    Write-Log "Iniciando geração do módulo '$ModuleName'..." "Info"
    Write-Log "" "Info"
    
    # 1. Verificar pré-requisitos
    Test-Prerequisites
    
    # 2. Inicializar variáveis de template
    Initialize-TemplateVariables
    
    # 3. Descobrir arquivos de template
    $templateFiles = Get-TemplateFiles
    
    if ($templateFiles.Count -eq 0) {
        throw "Nenhum arquivo de template encontrado para as camadas especificadas: $($IncludeLayers -join ', ')"
    }
    
    # 4. Criar estrutura do módulo
    New-ModuleStructure
    
    # 5. Processar templates
    Invoke-TemplateProcessing -TemplateFiles $templateFiles
    
    # 6. Criar arquivo de projeto
    New-ModuleProjectFile
    
    # 7. Criar README
    New-ModuleReadme
    
    # 8. Mostrar resumo
    Show-Summary
    
    Write-Log "" "Info"
    Write-Log "Módulo '$ModuleName' gerado com sucesso!" "Success"
}
catch {
    Write-Log "" "Info"
    Write-Log "Erro durante a geração do módulo: $($_.Exception.Message)" "Error"
    
    # Cleanup em caso de erro
    if ($script:CreatedDirectories.Count -gt 0 -or $script:CreatedFiles.Count -gt 0) {
        Write-Log "Executando limpeza..." "Warning"
        
        $script:CreatedFiles | ForEach-Object {
            if (Test-Path $_) {
                Remove-Item $_ -Force -ErrorAction SilentlyContinue
            }
        }
        
        $script:CreatedDirectories | Sort-Object { $_.Length } -Descending | ForEach-Object {
            if (Test-Path $_) {
                Remove-Item $_ -Force -Recurse -ErrorAction SilentlyContinue
            }
        }
    }
    
    exit 1
}

#endregion