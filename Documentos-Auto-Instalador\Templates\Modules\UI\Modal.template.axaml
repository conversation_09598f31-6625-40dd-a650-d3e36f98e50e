<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:material="using:Material.Icons.Avalonia"
             xmlns:vm="using:{{RootNamespace}}.UI.Modules.{{ModuleName}}.ViewModels"
             mc:Ignorable="d" d:DesignWidth="600" d:DesignHeight="400"
             x:Class="{{RootNamespace}}.UI.Modules.{{ModuleName}}.Views.{{EntityName}}Modal"
             x:DataType="vm:{{EntityName}}ModalViewModel">

  <Design.DataContext>
    <vm:{{EntityName}}ModalViewModel />
  </Design.DataContext>

  <UserControl.Styles>
    <Style Selector="Border.modal-overlay">
      <Setter Property="Background" Value="#80000000" />
      <Setter Property="IsVisible" Value="False" />
      <Setter Property="Opacity" Value="0" />
      <Setter Property="Transitions">
        <Transitions>
          <DoubleTransition Property="Opacity" Duration="0:0:0.3" Easing="CubicEaseOut" />
        </Transitions>
      </Setter>
    </Style>

    <Style Selector="Border.modal-overlay.show">
      <Setter Property="IsVisible" Value="True" />
      <Setter Property="Opacity" Value="1" />
    </Style>

    <Style Selector="Border.modal-container">
      <Setter Property="Background" Value="{DynamicResource SurfaceBrush}" />
      <Setter Property="CornerRadius" Value="16" />
      <Setter Property="BorderThickness" Value="1" />
      <Setter Property="BorderBrush" Value="{DynamicResource OutlineVariantBrush}" />
      <Setter Property="BoxShadow" Value="0 8 32 0 #40000000" />
      <Setter Property="MaxWidth" Value="600" />
      <Setter Property="MaxHeight" Value="80vh" />
      <Setter Property="MinWidth" Value="320" />
      <Setter Property="MinHeight" Value="200" />
      <Setter Property="RenderTransform" Value="scale(0.9) translateY(20px)" />
      <Setter Property="Opacity" Value="0" />
      <Setter Property="Transitions">
        <Transitions>
          <TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.3" Easing="CubicEaseOut" />
          <DoubleTransition Property="Opacity" Duration="0:0:0.3" Easing="CubicEaseOut" />
        </Transitions>
      </Setter>
    </Style>

    <Style Selector="Border.modal-container.show">
      <Setter Property="RenderTransform" Value="scale(1) translateY(0px)" />
      <Setter Property="Opacity" Value="1" />
    </Style>

    <Style Selector="Border.modal-header">
      <Setter Property="Background" Value="{DynamicResource SurfaceVariantBrush}" />
      <Setter Property="CornerRadius" Value="16,16,0,0" />
      <Setter Property="Padding" Value="24,20" />
      <Setter Property="BorderThickness" Value="0,0,0,1" />
      <Setter Property="BorderBrush" Value="{DynamicResource OutlineVariantBrush}" />
    </Style>

    <Style Selector="TextBlock.modal-title">
      <Setter Property="FontSize" Value="20" />
      <Setter Property="FontWeight" Value="SemiBold" />
      <Setter Property="Foreground" Value="{DynamicResource OnSurfaceBrush}" />
    </Style>

    <Style Selector="TextBlock.modal-subtitle">
      <Setter Property="FontSize" Value="14" />
      <Setter Property="Foreground" Value="{DynamicResource OnSurfaceVariantBrush}" />
      <Setter Property="Margin" Value="0,4,0,0" />
    </Style>

    <Style Selector="Button.modal-close">
      <Setter Property="Background" Value="Transparent" />
      <Setter Property="BorderThickness" Value="0" />
      <Setter Property="Padding" Value="8" />
      <Setter Property="CornerRadius" Value="8" />
      <Setter Property="Width" Value="40" />
      <Setter Property="Height" Value="40" />
      <Setter Property="Foreground" Value="{DynamicResource OnSurfaceVariantBrush}" />
    </Style>

    <Style Selector="Button.modal-close:pointerover">
      <Setter Property="Background" Value="{DynamicResource OnSurfaceBrush, Opacity=0.08}" />
    </Style>

    <Style Selector="Button.modal-close:pressed">
      <Setter Property="Background" Value="{DynamicResource OnSurfaceBrush, Opacity=0.12}" />
    </Style>

    <Style Selector="ScrollViewer.modal-content">
      <Setter Property="Padding" Value="24" />
      <Setter Property="HorizontalScrollBarVisibility" Value="Disabled" />
      <Setter Property="VerticalScrollBarVisibility" Value="Auto" />
    </Style>

    <Style Selector="Border.modal-footer">
      <Setter Property="Background" Value="{DynamicResource SurfaceVariantBrush}" />
      <Setter Property="CornerRadius" Value="0,0,16,16" />
      <Setter Property="Padding" Value="24,16" />
      <Setter Property="BorderThickness" Value="0,1,0,0" />
      <Setter Property="BorderBrush" Value="{DynamicResource OutlineVariantBrush}" />
    </Style>

    <Style Selector="Button.modal-action">
      <Setter Property="Padding" Value="16,12" />
      <Setter Property="CornerRadius" Value="8" />
      <Setter Property="FontWeight" Value="Medium" />
      <Setter Property="MinWidth" Value="80" />
    </Style>

    <Style Selector="Button.modal-action.primary">
      <Setter Property="Background" Value="{DynamicResource PrimaryBrush}" />
      <Setter Property="Foreground" Value="{DynamicResource OnPrimaryBrush}" />
      <Setter Property="BorderThickness" Value="0" />
    </Style>

    <Style Selector="Button.modal-action.primary:pointerover">
      <Setter Property="Background" Value="{DynamicResource PrimaryBrush, Opacity=0.9}" />
    </Style>

    <Style Selector="Button.modal-action.secondary">
      <Setter Property="Background" Value="Transparent" />
      <Setter Property="Foreground" Value="{DynamicResource PrimaryBrush}" />
      <Setter Property="BorderThickness" Value="1" />
      <Setter Property="BorderBrush" Value="{DynamicResource PrimaryBrush}" />
    </Style>

    <Style Selector="Button.modal-action.secondary:pointerover">
      <Setter Property="Background" Value="{DynamicResource PrimaryBrush, Opacity=0.08}" />
    </Style>

    <Style Selector="Button.modal-action.tertiary">
      <Setter Property="Background" Value="Transparent" />
      <Setter Property="Foreground" Value="{DynamicResource OnSurfaceVariantBrush}" />
      <Setter Property="BorderThickness" Value="0" />
    </Style>

    <Style Selector="Button.modal-action.tertiary:pointerover">
      <Setter Property="Background" Value="{DynamicResource OnSurfaceBrush, Opacity=0.08}" />
    </Style>

    <Style Selector="ProgressBar.modal-progress">
      <Setter Property="Height" Value="4" />
      <Setter Property="CornerRadius" Value="2" />
      <Setter Property="Background" Value="{DynamicResource OutlineVariantBrush}" />
      <Setter Property="Foreground" Value="{DynamicResource PrimaryBrush}" />
      <Setter Property="Margin" Value="0,0,0,16" />
    </Style>

    <Style Selector="material|MaterialIcon.modal-icon">
      <Setter Property="Width" Value="24" />
      <Setter Property="Height" Value="24" />
      <Setter Property="Margin" Value="0,0,12,0" />
    </Style>
  </UserControl.Styles>

  <!-- Overlay do modal -->
  <Border Classes="modal-overlay"
          Classes.show="{Binding IsVisible}"
          Tapped="OnOverlayTapped">
    
    <Grid HorizontalAlignment="Center"
          VerticalAlignment="Center"
          Margin="20">
      
      <!-- Container do modal -->
      <Border Classes="modal-container"
              Classes.show="{Binding IsVisible}"
              Width="{Binding ModalWidth}"
              Height="{Binding ModalHeight}">
        
        <Grid RowDefinitions="Auto,*,Auto">
          
          <!-- Header -->
          <Border Grid.Row="0"
                 Classes="modal-header"
                 IsVisible="{Binding ShowHeader}">
            
            <Grid ColumnDefinitions="Auto,*,Auto">
              
              <!-- Ícone do título -->
              <material:MaterialIcon Grid.Column="0"
                                    Classes="modal-icon"
                                    Kind="{Binding TitleIconKind}"
                                    Foreground="{DynamicResource PrimaryBrush}"
                                    IsVisible="{Binding ShowTitleIcon}" />
              
              <!-- Título e subtítulo -->
              <StackPanel Grid.Column="1" VerticalAlignment="Center">
                
                <TextBlock Classes="modal-title"
                          Text="{Binding Title}"
                          IsVisible="{Binding !#Title.Text, Converter={x:Static StringConverters.IsNotNullOrEmpty}}" />
                
                <TextBlock Classes="modal-subtitle"
                          Text="{Binding Subtitle}"
                          IsVisible="{Binding !#Subtitle.Text, Converter={x:Static StringConverters.IsNotNullOrEmpty}}" />
                
              </StackPanel>
              
              <!-- Botão fechar -->
              <Button Grid.Column="2"
                     Classes="modal-close"
                     Command="{Binding CloseCommand}"
                     IsVisible="{Binding ShowCloseButton}"
                     ToolTip.Tip="Fechar">
                <material:MaterialIcon Kind="Close" Width="20" Height="20" />
              </Button>
              
            </Grid>
            
          </Border>
          
          <!-- Conteúdo -->
          <ScrollViewer Grid.Row="1"
                       Classes="modal-content">
            
            <StackPanel Spacing="16">
              
              <!-- Barra de progresso -->
              <ProgressBar Classes="modal-progress"
                          Value="{Binding ProgressValue}"
                          Maximum="{Binding ProgressMaximum}"
                          IsVisible="{Binding ShowProgress}"
                          IsIndeterminate="{Binding IsProgressIndeterminate}" />
              
              <!-- Mensagem de status -->
              <Border Background="{DynamicResource SurfaceVariantBrush}"
                     CornerRadius="8"
                     Padding="16,12"
                     IsVisible="{Binding HasStatusMessage}">
                
                <Grid ColumnDefinitions="Auto,*">
                  
                  <material:MaterialIcon Grid.Column="0"
                                        Kind="{Binding StatusIconKind}"
                                        Width="20" Height="20"
                                        Foreground="{Binding StatusIconColor}"
                                        Margin="0,0,12,0" />
                  
                  <TextBlock Grid.Column="1"
                            Text="{Binding StatusMessage}"
                            FontSize="14"
                            Foreground="{DynamicResource OnSurfaceBrush}"
                            TextWrapping="Wrap"
                            VerticalAlignment="Center" />
                  
                </Grid>
                
              </Border>
              
              <!-- Conteúdo principal -->
              <TextBlock Text="{Binding Content}"
                        FontSize="14"
                        Foreground="{DynamicResource OnSurfaceBrush}"
                        TextWrapping="Wrap"
                        LineHeight="20"
                        IsVisible="{Binding !#Content.Text, Converter={x:Static StringConverters.IsNotNullOrEmpty}}" />
              
              <!-- Conteúdo customizado -->
              <ContentPresenter Content="{Binding CustomContent}"
                               IsVisible="{Binding HasCustomContent}" />
              
              <!-- Lista de itens -->
              <ItemsControl ItemsSource="{Binding Items}"
                           IsVisible="{Binding HasItems}">
                <ItemsControl.ItemTemplate>
                  <DataTemplate>
                    <Border Background="{DynamicResource SurfaceBrush}"
                           CornerRadius="8"
                           Padding="16,12"
                           Margin="0,4"
                           BorderThickness="1"
                           BorderBrush="{DynamicResource OutlineVariantBrush}">
                      
                      <Grid ColumnDefinitions="Auto,*,Auto">
                        
                        <material:MaterialIcon Grid.Column="0"
                                              Kind="{Binding IconKind}"
                                              Width="20" Height="20"
                                              Foreground="{Binding IconColor}"
                                              Margin="0,0,12,0"
                                              IsVisible="{Binding HasIcon}" />
                        
                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                          
                          <TextBlock Text="{Binding Title}"
                                    FontSize="14"
                                    FontWeight="Medium"
                                    Foreground="{DynamicResource OnSurfaceBrush}" />
                          
                          <TextBlock Text="{Binding Description}"
                                    FontSize="12"
                                    Foreground="{DynamicResource OnSurfaceVariantBrush}"
                                    IsVisible="{Binding HasDescription}"
                                    Margin="0,2,0,0" />
                          
                        </StackPanel>
                        
                        <StackPanel Grid.Column="2"
                                   Orientation="Horizontal"
                                   Spacing="8"
                                   VerticalAlignment="Center">
                          
                          <TextBlock Text="{Binding Value}"
                                    FontSize="13"
                                    FontWeight="Medium"
                                    Foreground="{DynamicResource OnSurfaceVariantBrush}"
                                    IsVisible="{Binding HasValue}" />
                          
                          <Button Classes="modal-action tertiary"
                                 Content="{Binding ActionText}"
                                 Command="{Binding ActionCommand}"
                                 IsVisible="{Binding HasAction}"
                                 Padding="8,4"
                                 FontSize="12" />
                          
                        </StackPanel>
                        
                      </Grid>
                      
                    </Border>
                  </DataTemplate>
                </ItemsControl.ItemTemplate>
              </ItemsControl>
              
              <!-- Formulário -->
              <ContentPresenter Content="{Binding FormContent}"
                               IsVisible="{Binding HasForm}" />
              
            </StackPanel>
            
          </ScrollViewer>
          
          <!-- Footer com ações -->
          <Border Grid.Row="2"
                 Classes="modal-footer"
                 IsVisible="{Binding ShowFooter}">
            
            <Grid ColumnDefinitions="*,Auto">
              
              <!-- Informações adicionais -->
              <StackPanel Grid.Column="0"
                         VerticalAlignment="Center"
                         IsVisible="{Binding HasFooterInfo}">
                
                <TextBlock Text="{Binding FooterInfo}"
                          FontSize="12"
                          Foreground="{DynamicResource OnSurfaceVariantBrush}" />
                
              </StackPanel>
              
              <!-- Botões de ação -->
              <StackPanel Grid.Column="1"
                         Orientation="Horizontal"
                         Spacing="12"
                         HorizontalAlignment="Right">
                
                <Button Classes="modal-action tertiary"
                       Content="{Binding TertiaryActionText}"
                       Command="{Binding TertiaryActionCommand}"
                       IsVisible="{Binding HasTertiaryAction}" />
                
                <Button Classes="modal-action secondary"
                       Content="{Binding SecondaryActionText}"
                       Command="{Binding SecondaryActionCommand}"
                       IsVisible="{Binding HasSecondaryAction}" />
                
                <Button Classes="modal-action primary"
                       Content="{Binding PrimaryActionText}"
                       Command="{Binding PrimaryActionCommand}"
                       IsVisible="{Binding HasPrimaryAction}"
                       IsEnabled="{Binding IsPrimaryActionEnabled}" />
                
              </StackPanel>
              
            </Grid>
            
          </Border>
          
        </Grid>
        
      </Border>
      
    </Grid>
    
  </Border>
  
</UserControl>