Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1

Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}"
EndProject

Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC943}"
EndProject

Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "docs", "docs", "{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC944}"
EndProject

Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "build", "build", "{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC945}"
EndProject

Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AutoInstaller.Core", "src\AutoInstaller.Core\AutoInstaller.Core.csproj", "{A1B2C3D4-E5F6-7890-1234-567890ABCDEF}"
EndProject

Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AutoInstaller.Application", "src\AutoInstaller.Application\AutoInstaller.Application.csproj", "{B2C3D4E5-F6G7-8901-2345-67890ABCDEF1}"
EndProject

Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AutoInstaller.Infrastructure", "src\AutoInstaller.Infrastructure\AutoInstaller.Infrastructure.csproj", "{C3D4E5F6-G7H8-9012-3456-7890ABCDEF12}"
EndProject

Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AutoInstaller.UI", "src\AutoInstaller.UI\AutoInstaller.UI.csproj", "{D4E5F6G7-H8I9-0123-4567-890ABCDEF123}"
EndProject

Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AutoInstaller.Tests.Unit", "tests\AutoInstaller.Tests.Unit\AutoInstaller.Tests.Unit.csproj", "{E5F6G7H8-I9J0-1234-5678-90ABCDEF1234}"
EndProject

Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{F6G7H8I9-J0K1-2345-6789-0ABCDEF12345}"
	ProjectSection(SolutionItems) = preProject
		README.md = README.md
		.gitignore = .gitignore
		Directory.Build.props = Directory.Build.props
		Directory.Packages.props = Directory.Packages.props
		global.json = global.json
	EndProjectSection
EndProject

Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A1B2C3D4-E5F6-7890-1234-567890ABCDEF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-1234-567890ABCDEF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-1234-567890ABCDEF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-1234-567890ABCDEF}.Release|Any CPU.Build.0 = Release|Any CPU
		{B2C3D4E5-F6G7-8901-2345-67890ABCDEF1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B2C3D4E5-F6G7-8901-2345-67890ABCDEF1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B2C3D4E5-F6G7-8901-2345-67890ABCDEF1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B2C3D4E5-F6G7-8901-2345-67890ABCDEF1}.Release|Any CPU.Build.0 = Release|Any CPU
		{C3D4E5F6-G7H8-9012-3456-7890ABCDEF12}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C3D4E5F6-G7H8-9012-3456-7890ABCDEF12}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C3D4E5F6-G7H8-9012-3456-7890ABCDEF12}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C3D4E5F6-G7H8-9012-3456-7890ABCDEF12}.Release|Any CPU.Build.0 = Release|Any CPU
		{D4E5F6G7-H8I9-0123-4567-890ABCDEF123}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D4E5F6G7-H8I9-0123-4567-890ABCDEF123}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D4E5F6G7-H8I9-0123-4567-890ABCDEF123}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D4E5F6G7-H8I9-0123-4567-890ABCDEF123}.Release|Any CPU.Build.0 = Release|Any CPU
		{E5F6G7H8-I9J0-1234-5678-90ABCDEF1234}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E5F6G7H8-I9J0-1234-5678-90ABCDEF1234}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E5F6G7H8-I9J0-1234-5678-90ABCDEF1234}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E5F6G7H8-I9J0-1234-5678-90ABCDEF1234}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{A1B2C3D4-E5F6-7890-1234-567890ABCDEF} = {8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}
		{B2C3D4E5-F6G7-8901-2345-67890ABCDEF1} = {8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}
		{C3D4E5F6-G7H8-9012-3456-7890ABCDEF12} = {8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}
		{D4E5F6G7-H8I9-0123-4567-890ABCDEF123} = {8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}
		{E5F6G7H8-I9J0-1234-5678-90ABCDEF1234} = {8BC9CEB8-8B4A-11D0-8D11-00A0C91BC943}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {12345678-1234-1234-1234-123456789012}
	EndGlobalSection
EndGlobal
