using AutoInstaller.Core.Entities;
using AutoInstaller.Core.Exceptions;
using AutoInstaller.Core.ValueObjects;

namespace AutoInstaller.Tests.Unit.Core.Entities;

/// <summary>
/// Testes para Container Entity
/// </summary>
public class ContainerTests
{
    private readonly IFixture _fixture;

    public ContainerTests()
    {
        _fixture = new Fixture();
        _fixture.Customize<ContainerName>(c => c.FromFactory(() => ContainerName.Create("test-container")));
        _fixture.Customize<ImageTag>(c => c.FromFactory(() => ImageTag.Create("latest")));
    }

    [Fact]
    public void Constructor_WithValidParameters_ShouldCreateContainer()
    {
        // Arrange
        var name = ContainerName.Create("test-container");
        var imageName = "nginx";
        var imageTag = ImageTag.Create("latest");
        var runtime = ContainerRuntime.Docker;
        var createdBy = "test-user";

        // Act
        var container = new Container(name, imageName, imageTag, runtime, createdBy);

        // Assert
        container.Should().NotBeNull();
        container.Id.Should().NotBeEmpty();
        container.Name.Should().Be(name);
        container.ImageName.Should().Be(imageName);
        container.ImageTag.Should().Be(imageTag);
        container.Runtime.Should().Be(runtime);
        container.Status.Should().Be(ContainerStatus.Created);
        container.CreatedBy.Should().Be(createdBy);
        container.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        container.IsDeleted.Should().BeFalse();
        container.AutoRestart.Should().BeFalse();
        container.RestartPolicy.Should().Be("no");
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public void Constructor_WithInvalidImageName_ShouldThrowArgumentException(string? invalidImageName)
    {
        // Arrange
        var name = ContainerName.Create("test-container");
        var imageTag = ImageTag.Create("latest");
        var runtime = ContainerRuntime.Docker;
        var createdBy = "test-user";

        // Act & Assert
        var act = () => new Container(name, invalidImageName!, imageTag, runtime, createdBy);
        act.Should().Throw<ArgumentException>()
           .WithMessage("Nome da imagem não pode ser vazio*");
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public void Constructor_WithInvalidCreatedBy_ShouldThrowArgumentException(string? invalidCreatedBy)
    {
        // Arrange
        var name = ContainerName.Create("test-container");
        var imageName = "nginx";
        var imageTag = ImageTag.Create("latest");
        var runtime = ContainerRuntime.Docker;

        // Act & Assert
        var act = () => new Container(name, imageName, imageTag, runtime, invalidCreatedBy!);
        act.Should().Throw<ArgumentException>()
           .WithMessage("Usuário criador não pode ser vazio*");
    }

    [Fact]
    public void UpdateStatus_WithValidStatus_ShouldUpdateStatus()
    {
        // Arrange
        var container = CreateValidContainer();
        var newStatus = ContainerStatus.Running;
        var updatedBy = "test-user";

        // Act
        container.UpdateStatus(newStatus, updatedBy);

        // Assert
        container.Status.Should().Be(newStatus);
        container.UpdatedBy.Should().Be(updatedBy);
        container.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public void UpdateStatus_WithInvalidUpdatedBy_ShouldThrowArgumentException(string? invalidUpdatedBy)
    {
        // Arrange
        var container = CreateValidContainer();
        var newStatus = ContainerStatus.Running;

        // Act & Assert
        var act = () => container.UpdateStatus(newStatus, invalidUpdatedBy!);
        act.Should().Throw<ArgumentException>()
           .WithMessage("Usuário atualizador não pode ser vazio*");
    }

    [Fact]
    public void SetCommand_WithValidParameters_ShouldSetCommand()
    {
        // Arrange
        var container = CreateValidContainer();
        var command = "/bin/bash";
        var arguments = new[] { "-c", "echo hello" };
        var updatedBy = "test-user";

        // Act
        container.SetCommand(command, arguments, updatedBy);

        // Assert
        container.Command.Should().Be(command);
        container.Arguments.Should().BeEquivalentTo(arguments);
        container.UpdatedBy.Should().Be(updatedBy);
        container.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public void SetCommand_WithInvalidCommand_ShouldThrowArgumentException(string? invalidCommand)
    {
        // Arrange
        var container = CreateValidContainer();
        var updatedBy = "test-user";

        // Act & Assert
        var act = () => container.SetCommand(invalidCommand!, null, updatedBy);
        act.Should().Throw<ArgumentException>()
           .WithMessage("Comando não pode ser vazio*");
    }

    [Fact]
    public void SetExecutionSettings_WithValidParameters_ShouldSetSettings()
    {
        // Arrange
        var container = CreateValidContainer();
        var workingDirectory = "/app";
        var user = "appuser";
        var updatedBy = "test-user";

        // Act
        container.SetExecutionSettings(workingDirectory, user, updatedBy);

        // Assert
        container.WorkingDirectory.Should().Be(workingDirectory);
        container.User.Should().Be(user);
        container.UpdatedBy.Should().Be(updatedBy);
        container.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void SetRestartPolicy_WithValidParameters_ShouldSetPolicy()
    {
        // Arrange
        var container = CreateValidContainer();
        var autoRestart = true;
        var restartPolicy = "always";
        var updatedBy = "test-user";

        // Act
        container.SetRestartPolicy(autoRestart, restartPolicy, updatedBy);

        // Assert
        container.AutoRestart.Should().Be(autoRestart);
        container.RestartPolicy.Should().Be(restartPolicy);
        container.UpdatedBy.Should().Be(updatedBy);
        container.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public void SetRestartPolicy_WithInvalidPolicy_ShouldThrowArgumentException(string? invalidPolicy)
    {
        // Arrange
        var container = CreateValidContainer();
        var updatedBy = "test-user";

        // Act & Assert
        var act = () => container.SetRestartPolicy(true, invalidPolicy!, updatedBy);
        act.Should().Throw<ArgumentException>()
           .WithMessage("Política de reinicialização não pode ser vazia*");
    }

    [Fact]
    public void SetResourceLimits_WithValidParameters_ShouldSetLimits()
    {
        // Arrange
        var container = CreateValidContainer();
        var memoryLimit = 1024L * 1024 * 1024; // 1GB
        var cpuLimit = 2.0;
        var updatedBy = "test-user";

        // Act
        container.SetResourceLimits(memoryLimit, cpuLimit, updatedBy);

        // Assert
        container.MemoryLimit.Should().Be(memoryLimit);
        container.CpuLimit.Should().Be(cpuLimit);
        container.UpdatedBy.Should().Be(updatedBy);
        container.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Theory]
    [InlineData(-1)]
    [InlineData(0)]
    public void SetResourceLimits_WithInvalidMemoryLimit_ShouldThrowArgumentException(long invalidMemoryLimit)
    {
        // Arrange
        var container = CreateValidContainer();
        var updatedBy = "test-user";

        // Act & Assert
        var act = () => container.SetResourceLimits(invalidMemoryLimit, null, updatedBy);
        act.Should().Throw<ArgumentException>()
           .WithMessage("Limite de memória deve ser positivo*");
    }

    [Theory]
    [InlineData(-1.0)]
    [InlineData(0.0)]
    public void SetResourceLimits_WithInvalidCpuLimit_ShouldThrowArgumentException(double invalidCpuLimit)
    {
        // Arrange
        var container = CreateValidContainer();
        var updatedBy = "test-user";

        // Act & Assert
        var act = () => container.SetResourceLimits(null, invalidCpuLimit, updatedBy);
        act.Should().Throw<ArgumentException>()
           .WithMessage("Limite de CPU deve ser positivo*");
    }

    [Fact]
    public void AddExposedPort_WithValidPort_ShouldAddPort()
    {
        // Arrange
        var container = CreateValidContainer();
        var port = Port.Create(80, PortProtocol.TCP);
        var updatedBy = "test-user";

        // Act
        container.AddExposedPort(port, updatedBy);

        // Assert
        container.ExposedPorts.Should().Contain(port);
        container.UpdatedBy.Should().Be(updatedBy);
        container.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void AddExposedPort_WithDuplicatePort_ShouldThrowBusinessRuleException()
    {
        // Arrange
        var container = CreateValidContainer();
        var port = Port.Create(80, PortProtocol.TCP);
        var updatedBy = "test-user";
        container.AddExposedPort(port, updatedBy);

        // Act & Assert
        var act = () => container.AddExposedPort(port, updatedBy);
        act.Should().Throw<BusinessRuleException>()
           .WithMessage("Porta 80/TCP já está exposta");
    }

    [Fact]
    public void RemoveExposedPort_WithExistingPort_ShouldRemovePort()
    {
        // Arrange
        var container = CreateValidContainer();
        var port = Port.Create(80, PortProtocol.TCP);
        var updatedBy = "test-user";
        container.AddExposedPort(port, updatedBy);

        // Act
        container.RemoveExposedPort(port, updatedBy);

        // Assert
        container.ExposedPorts.Should().NotContain(port);
        container.UpdatedBy.Should().Be(updatedBy);
    }

    [Fact]
    public void AddEnvironmentVariable_WithValidKeyValue_ShouldAddVariable()
    {
        // Arrange
        var container = CreateValidContainer();
        var key = "NODE_ENV";
        var value = "production";
        var updatedBy = "test-user";

        // Act
        container.AddEnvironmentVariable(key, value, updatedBy);

        // Assert
        container.EnvironmentVariables.Should().ContainKey(key);
        container.EnvironmentVariables[key].Should().Be(value);
        container.UpdatedBy.Should().Be(updatedBy);
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public void AddEnvironmentVariable_WithInvalidKey_ShouldThrowArgumentException(string? invalidKey)
    {
        // Arrange
        var container = CreateValidContainer();
        var value = "production";
        var updatedBy = "test-user";

        // Act & Assert
        var act = () => container.AddEnvironmentVariable(invalidKey!, value, updatedBy);
        act.Should().Throw<ArgumentException>()
           .WithMessage("Chave da variável de ambiente não pode ser vazia*");
    }

    [Fact]
    public void AddLabel_WithValidKeyValue_ShouldAddLabel()
    {
        // Arrange
        var container = CreateValidContainer();
        var key = "app.version";
        var value = "1.0.0";
        var updatedBy = "test-user";

        // Act
        container.AddLabel(key, value, updatedBy);

        // Assert
        container.Labels.Should().ContainKey(key);
        container.Labels[key].Should().Be(value);
        container.UpdatedBy.Should().Be(updatedBy);
    }

    [Fact]
    public void AddVolume_WithValidVolume_ShouldAddVolume()
    {
        // Arrange
        var container = CreateValidContainer();
        var volume = "/host/path:/container/path";
        var updatedBy = "test-user";

        // Act
        container.AddVolume(volume, updatedBy);

        // Assert
        container.Volumes.Should().Contain(volume);
        container.UpdatedBy.Should().Be(updatedBy);
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public void AddVolume_WithInvalidVolume_ShouldThrowArgumentException(string? invalidVolume)
    {
        // Arrange
        var container = CreateValidContainer();
        var updatedBy = "test-user";

        // Act & Assert
        var act = () => container.AddVolume(invalidVolume!, updatedBy);
        act.Should().Throw<ArgumentException>()
           .WithMessage("Definição de volume não pode ser vazia*");
    }

    [Theory]
    [InlineData(ContainerStatus.Created, true)]
    [InlineData(ContainerStatus.Exited, true)]
    [InlineData(ContainerStatus.Running, false)]
    [InlineData(ContainerStatus.Paused, false)]
    [InlineData(ContainerStatus.Dead, false)]
    public void CanStart_WithDifferentStatuses_ShouldReturnCorrectResult(ContainerStatus status, bool expectedCanStart)
    {
        // Arrange
        var container = CreateValidContainer();
        container.UpdateStatus(status, "test-user");

        // Act
        var result = container.CanStart();

        // Assert
        result.Should().Be(expectedCanStart);
    }

    [Theory]
    [InlineData(ContainerStatus.Running, true)]
    [InlineData(ContainerStatus.Paused, true)]
    [InlineData(ContainerStatus.Created, false)]
    [InlineData(ContainerStatus.Exited, false)]
    [InlineData(ContainerStatus.Dead, false)]
    public void CanStop_WithDifferentStatuses_ShouldReturnCorrectResult(ContainerStatus status, bool expectedCanStop)
    {
        // Arrange
        var container = CreateValidContainer();
        container.UpdateStatus(status, "test-user");

        // Act
        var result = container.CanStop();

        // Assert
        result.Should().Be(expectedCanStop);
    }

    [Theory]
    [InlineData(ContainerStatus.Running, true)]
    [InlineData(ContainerStatus.Created, false)]
    [InlineData(ContainerStatus.Paused, false)]
    [InlineData(ContainerStatus.Exited, false)]
    public void CanPause_WithDifferentStatuses_ShouldReturnCorrectResult(ContainerStatus status, bool expectedCanPause)
    {
        // Arrange
        var container = CreateValidContainer();
        container.UpdateStatus(status, "test-user");

        // Act
        var result = container.CanPause();

        // Assert
        result.Should().Be(expectedCanPause);
    }

    [Theory]
    [InlineData(ContainerStatus.Created, true)]
    [InlineData(ContainerStatus.Exited, true)]
    [InlineData(ContainerStatus.Dead, true)]
    [InlineData(ContainerStatus.Running, false)]
    [InlineData(ContainerStatus.Paused, false)]
    public void CanRemove_WithDifferentStatuses_ShouldReturnCorrectResult(ContainerStatus status, bool expectedCanRemove)
    {
        // Arrange
        var container = CreateValidContainer();
        container.UpdateStatus(status, "test-user");

        // Act
        var result = container.CanRemove();

        // Assert
        result.Should().Be(expectedCanRemove);
    }

    [Fact]
    public void MarkAsDeleted_WithValidUser_ShouldMarkAsDeleted()
    {
        // Arrange
        var container = CreateValidContainer();
        var deletedBy = "test-user";

        // Act
        container.MarkAsDeleted(deletedBy);

        // Assert
        container.IsDeleted.Should().BeTrue();
        container.UpdatedBy.Should().Be(deletedBy);
        container.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void Restore_WithValidUser_ShouldRestoreContainer()
    {
        // Arrange
        var container = CreateValidContainer();
        var deletedBy = "test-user";
        var restoredBy = "admin-user";
        container.MarkAsDeleted(deletedBy);

        // Act
        container.Restore(restoredBy);

        // Assert
        container.IsDeleted.Should().BeFalse();
        container.UpdatedBy.Should().Be(restoredBy);
        container.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    private Container CreateValidContainer()
    {
        var name = ContainerName.Create("test-container");
        var imageName = "nginx";
        var imageTag = ImageTag.Create("latest");
        var runtime = ContainerRuntime.Docker;
        var createdBy = "test-user";

        return new Container(name, imageName, imageTag, runtime, createdBy);
    }
}
