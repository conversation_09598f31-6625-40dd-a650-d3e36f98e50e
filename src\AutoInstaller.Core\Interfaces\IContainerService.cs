using AutoInstaller.Core.Entities;
using AutoInstaller.Core.ValueObjects;

namespace AutoInstaller.Core.Interfaces;

/// <summary>
/// Interface para serviços de containerização
/// </summary>
public interface IContainerService
{
    /// <summary>
    /// Runtime suportado pelo serviço
    /// </summary>
    ContainerRuntime SupportedRuntime { get; }

    /// <summary>
    /// Verifica se o runtime está disponível
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se disponível</returns>
    Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtém informações do runtime
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Informações do runtime</returns>
    Task<RuntimeInfo> GetRuntimeInfoAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Lista todos os containers
    /// </summary>
    /// <param name="includeAll">Incluir containers parados</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de containers</returns>
    Task<IReadOnlyList<ContainerInfo>> ListContainersAsync(bool includeAll = true, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtém informações de um container específico
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Informações do container</returns>
    Task<ContainerInfo?> GetContainerAsync(string containerId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Cria um novo container
    /// </summary>
    /// <param name="request">Requisição de criação</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>ID do container criado</returns>
    Task<string> CreateContainerAsync(CreateContainerRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Inicia um container
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se iniciado com sucesso</returns>
    Task<bool> StartContainerAsync(string containerId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Para um container
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="timeoutSeconds">Timeout em segundos</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se parado com sucesso</returns>
    Task<bool> StopContainerAsync(string containerId, int timeoutSeconds = 10, CancellationToken cancellationToken = default);

    /// <summary>
    /// Reinicia um container
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="timeoutSeconds">Timeout em segundos</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se reiniciado com sucesso</returns>
    Task<bool> RestartContainerAsync(string containerId, int timeoutSeconds = 10, CancellationToken cancellationToken = default);

    /// <summary>
    /// Pausa um container
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se pausado com sucesso</returns>
    Task<bool> PauseContainerAsync(string containerId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Despausa um container
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se despausado com sucesso</returns>
    Task<bool> UnpauseContainerAsync(string containerId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Remove um container
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="force">Forçar remoção</param>
    /// <param name="removeVolumes">Remover volumes associados</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se removido com sucesso</returns>
    Task<bool> RemoveContainerAsync(string containerId, bool force = false, bool removeVolumes = false, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtém logs de um container
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="tail">Número de linhas do final</param>
    /// <param name="since">Data de início</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Logs do container</returns>
    Task<string> GetContainerLogsAsync(string containerId, int? tail = null, DateTime? since = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtém estatísticas de um container
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Estatísticas do container</returns>
    Task<ContainerStats?> GetContainerStatsAsync(string containerId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Lista imagens disponíveis
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de imagens</returns>
    Task<IReadOnlyList<ImageInfo>> ListImagesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Puxa uma imagem do registry
    /// </summary>
    /// <param name="imageName">Nome da imagem</param>
    /// <param name="tag">Tag da imagem</param>
    /// <param name="progress">Callback de progresso</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se puxada com sucesso</returns>
    Task<bool> PullImageAsync(string imageName, string tag = "latest", IProgress<PullProgress>? progress = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Remove uma imagem
    /// </summary>
    /// <param name="imageId">ID da imagem</param>
    /// <param name="force">Forçar remoção</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se removida com sucesso</returns>
    Task<bool> RemoveImageAsync(string imageId, bool force = false, CancellationToken cancellationToken = default);
}

/// <summary>
/// Informações do runtime
/// </summary>
public class RuntimeInfo
{
    /// <summary>
    /// Nome do runtime
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Versão do runtime
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// Versão da API
    /// </summary>
    public string ApiVersion { get; set; } = string.Empty;

    /// <summary>
    /// Sistema operacional
    /// </summary>
    public string OperatingSystem { get; set; } = string.Empty;

    /// <summary>
    /// Arquitetura
    /// </summary>
    public string Architecture { get; set; } = string.Empty;

    /// <summary>
    /// Número de CPUs
    /// </summary>
    public int CpuCount { get; set; }

    /// <summary>
    /// Memória total em bytes
    /// </summary>
    public long TotalMemory { get; set; }

    /// <summary>
    /// Indica se está executando
    /// </summary>
    public bool IsRunning { get; set; }
}

/// <summary>
/// Informações de um container
/// </summary>
public class ContainerInfo
{
    /// <summary>
    /// ID do container
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// Nome do container
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Imagem do container
    /// </summary>
    public string Image { get; set; } = string.Empty;

    /// <summary>
    /// Status do container
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// Estado do container
    /// </summary>
    public string State { get; set; } = string.Empty;

    /// <summary>
    /// Data de criação
    /// </summary>
    public DateTime Created { get; set; }

    /// <summary>
    /// Data de início
    /// </summary>
    public DateTime? StartedAt { get; set; }

    /// <summary>
    /// Data de parada
    /// </summary>
    public DateTime? FinishedAt { get; set; }

    /// <summary>
    /// Portas expostas
    /// </summary>
    public Dictionary<string, string> Ports { get; set; } = new();

    /// <summary>
    /// Labels do container
    /// </summary>
    public Dictionary<string, string> Labels { get; set; } = new();

    /// <summary>
    /// Mounts/Volumes
    /// </summary>
    public List<string> Mounts { get; set; } = new();
}

/// <summary>
/// Requisição para criação de container
/// </summary>
public class CreateContainerRequest
{
    /// <summary>
    /// Nome do container
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Imagem do container
    /// </summary>
    public string Image { get; set; } = string.Empty;

    /// <summary>
    /// Tag da imagem
    /// </summary>
    public string Tag { get; set; } = "latest";

    /// <summary>
    /// Comando a ser executado
    /// </summary>
    public string? Command { get; set; }

    /// <summary>
    /// Argumentos do comando
    /// </summary>
    public string[]? Arguments { get; set; }

    /// <summary>
    /// Variáveis de ambiente
    /// </summary>
    public Dictionary<string, string> Environment { get; set; } = new();

    /// <summary>
    /// Portas a serem expostas
    /// </summary>
    public Dictionary<string, string> Ports { get; set; } = new();

    /// <summary>
    /// Volumes a serem montados
    /// </summary>
    public Dictionary<string, string> Volumes { get; set; } = new();

    /// <summary>
    /// Labels do container
    /// </summary>
    public Dictionary<string, string> Labels { get; set; } = new();

    /// <summary>
    /// Diretório de trabalho
    /// </summary>
    public string? WorkingDirectory { get; set; }

    /// <summary>
    /// Usuário de execução
    /// </summary>
    public string? User { get; set; }

    /// <summary>
    /// Política de reinicialização
    /// </summary>
    public string RestartPolicy { get; set; } = "no";

    /// <summary>
    /// Limite de memória em bytes
    /// </summary>
    public long? MemoryLimit { get; set; }

    /// <summary>
    /// Limite de CPU
    /// </summary>
    public double? CpuLimit { get; set; }
}

/// <summary>
/// Estatísticas de um container
/// </summary>
public class ContainerStats
{
    /// <summary>
    /// ID do container
    /// </summary>
    public string ContainerId { get; set; } = string.Empty;

    /// <summary>
    /// Uso de CPU em percentual
    /// </summary>
    public double CpuUsagePercent { get; set; }

    /// <summary>
    /// Uso de memória em bytes
    /// </summary>
    public long MemoryUsage { get; set; }

    /// <summary>
    /// Limite de memória em bytes
    /// </summary>
    public long MemoryLimit { get; set; }

    /// <summary>
    /// Uso de memória em percentual
    /// </summary>
    public double MemoryUsagePercent { get; set; }

    /// <summary>
    /// Bytes recebidos pela rede
    /// </summary>
    public long NetworkRxBytes { get; set; }

    /// <summary>
    /// Bytes transmitidos pela rede
    /// </summary>
    public long NetworkTxBytes { get; set; }

    /// <summary>
    /// Bytes lidos do disco
    /// </summary>
    public long BlockReadBytes { get; set; }

    /// <summary>
    /// Bytes escritos no disco
    /// </summary>
    public long BlockWriteBytes { get; set; }

    /// <summary>
    /// Timestamp da coleta
    /// </summary>
    public DateTime Timestamp { get; set; }
}

/// <summary>
/// Informações de uma imagem
/// </summary>
public class ImageInfo
{
    /// <summary>
    /// ID da imagem
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// Tags da imagem
    /// </summary>
    public List<string> RepoTags { get; set; } = new();

    /// <summary>
    /// Tamanho da imagem em bytes
    /// </summary>
    public long Size { get; set; }

    /// <summary>
    /// Data de criação
    /// </summary>
    public DateTime Created { get; set; }

    /// <summary>
    /// Labels da imagem
    /// </summary>
    public Dictionary<string, string> Labels { get; set; } = new();
}

/// <summary>
/// Progresso de pull de imagem
/// </summary>
public class PullProgress
{
    /// <summary>
    /// Status atual
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// ID da camada
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// Progresso atual
    /// </summary>
    public long Current { get; set; }

    /// <summary>
    /// Total de bytes
    /// </summary>
    public long Total { get; set; }

    /// <summary>
    /// Percentual de progresso
    /// </summary>
    public double ProgressPercent => Total > 0 ? (double)Current / Total * 100 : 0;
}
