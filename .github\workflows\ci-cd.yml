name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

env:
  DOTNET_VERSION: '9.0.x'
  DOTNET_SKIP_FIRST_TIME_EXPERIENCE: true
  DOTNET_CLI_TELEMETRY_OPTOUT: true
  NUGET_PACKAGES: ${{ github.workspace }}/.nuget/packages

jobs:
  # Job de Build e Testes
  build-and-test:
    name: Build and Test
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}

    - name: Cache NuGet packages
      uses: actions/cache@v4
      with:
        path: ${{ env.NUGET_PACKAGES }}
        key: ${{ runner.os }}-nuget-${{ hashFiles('**/*.csproj', '**/Directory.Packages.props') }}
        restore-keys: |
          ${{ runner.os }}-nuget-

    - name: Restore dependencies
      run: dotnet restore

    - name: Build solution
      run: dotnet build --no-restore --configuration Release

    - name: Run unit tests
      run: |
        dotnet test --no-build --configuration Release \
          --collect:"XPlat Code Coverage" \
          --results-directory ./artifacts/coverage \
          --logger trx \
          --verbosity normal

    - name: Generate coverage report
      uses: danielpalme/ReportGenerator-GitHub-Action@5.3.8
      with:
        reports: './artifacts/coverage/**/coverage.cobertura.xml'
        targetdir: './artifacts/coverage-report'
        reporttypes: 'Html;Cobertura;MarkdownSummaryGithub'

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v4
      with:
        files: ./artifacts/coverage-report/Cobertura.xml
        fail_ci_if_error: false

    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: test-results
        path: |
          ./artifacts/coverage
          ./artifacts/coverage-report

  # Job de Build Multiplataforma
  build-multiplatform:
    name: Build for ${{ matrix.os }}
    needs: build-and-test
    runs-on: ${{ matrix.runner }}
    
    strategy:
      matrix:
        include:
          - os: windows
            runner: windows-latest
            rid: win-x64
            artifact: AutoInstaller-Windows-x64
            extension: .exe
          - os: linux
            runner: ubuntu-latest
            rid: linux-x64
            artifact: AutoInstaller-Linux-x64
            extension: ''
          - os: macos
            runner: macos-latest
            rid: osx-x64
            artifact: AutoInstaller-macOS-x64
            extension: ''

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}

    - name: Cache NuGet packages
      uses: actions/cache@v4
      with:
        path: ${{ env.NUGET_PACKAGES }}
        key: ${{ runner.os }}-nuget-${{ hashFiles('**/*.csproj', '**/Directory.Packages.props') }}
        restore-keys: |
          ${{ runner.os }}-nuget-

    - name: Restore dependencies
      run: dotnet restore

    - name: Publish application
      run: |
        dotnet publish src/AutoInstaller.UI/AutoInstaller.UI.csproj \
          --configuration Release \
          --runtime ${{ matrix.rid }} \
          --self-contained true \
          --output ./artifacts/publish/${{ matrix.rid }} \
          -p:PublishSingleFile=true \
          -p:PublishTrimmed=true \
          -p:TrimMode=link \
          -p:EnableCompressionInSingleFile=true \
          -p:IncludeNativeLibrariesForSelfExtract=true

    - name: Create distribution package (Windows)
      if: matrix.os == 'windows'
      run: |
        $version = if ($env:GITHUB_REF -match 'refs/tags/v(.*)') { $matches[1] } else { "dev" }
        $packageName = "${{ matrix.artifact }}-$version"
        
        # Criar estrutura de diretórios
        New-Item -ItemType Directory -Path "./artifacts/dist/$packageName" -Force
        
        # Copiar executável
        Copy-Item "./artifacts/publish/${{ matrix.rid }}/AutoInstaller.UI${{ matrix.extension }}" "./artifacts/dist/$packageName/"
        
        # Copiar arquivos adicionais
        Copy-Item "README.md" "./artifacts/dist/$packageName/" -ErrorAction SilentlyContinue
        Copy-Item "LICENSE" "./artifacts/dist/$packageName/" -ErrorAction SilentlyContinue
        
        # Criar ZIP
        Compress-Archive -Path "./artifacts/dist/$packageName/*" -DestinationPath "./artifacts/dist/$packageName.zip"
      shell: pwsh

    - name: Create distribution package (Linux/macOS)
      if: matrix.os != 'windows'
      run: |
        version=$(echo $GITHUB_REF | sed -n 's/refs\/tags\/v\(.*\)/\1/p')
        if [ -z "$version" ]; then version="dev"; fi
        packageName="${{ matrix.artifact }}-$version"
        
        # Criar estrutura de diretórios
        mkdir -p "./artifacts/dist/$packageName"
        
        # Copiar executável
        cp "./artifacts/publish/${{ matrix.rid }}/AutoInstaller.UI${{ matrix.extension }}" "./artifacts/dist/$packageName/"
        chmod +x "./artifacts/dist/$packageName/AutoInstaller.UI${{ matrix.extension }}"
        
        # Copiar arquivos adicionais
        cp README.md "./artifacts/dist/$packageName/" 2>/dev/null || true
        cp LICENSE "./artifacts/dist/$packageName/" 2>/dev/null || true
        
        # Criar TAR.GZ
        cd "./artifacts/dist"
        tar -czf "$packageName.tar.gz" "$packageName"

    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: ${{ matrix.artifact }}
        path: |
          ./artifacts/dist/*.zip
          ./artifacts/dist/*.tar.gz
        retention-days: 30

  # Job de Release (apenas para tags)
  release:
    name: Create Release
    needs: build-multiplatform
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/v')
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download all artifacts
      uses: actions/download-artifact@v4
      with:
        path: ./artifacts

    - name: Create Release
      uses: softprops/action-gh-release@v2
      with:
        files: |
          ./artifacts/**/*.zip
          ./artifacts/**/*.tar.gz
        generate_release_notes: true
        draft: false
        prerelease: ${{ contains(github.ref, '-') }}
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  # Job de Análise de Código
  code-analysis:
    name: Code Analysis
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}

    - name: Cache NuGet packages
      uses: actions/cache@v4
      with:
        path: ${{ env.NUGET_PACKAGES }}
        key: ${{ runner.os }}-nuget-${{ hashFiles('**/*.csproj', '**/Directory.Packages.props') }}
        restore-keys: |
          ${{ runner.os }}-nuget-

    - name: Restore dependencies
      run: dotnet restore

    - name: Run code analysis
      run: |
        dotnet build --no-restore --configuration Release \
          --verbosity normal \
          -p:TreatWarningsAsErrors=true \
          -p:WarningsAsErrors="" \
          -p:RunAnalyzersDuringBuild=true

  # Job de Segurança
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v3
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'
