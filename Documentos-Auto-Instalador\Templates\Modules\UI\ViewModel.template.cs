using ReactiveUI;
using System.Reactive;
using System.Reactive.Disposables;
using System.Reactive.Linq;
using System.Collections.ObjectModel;
using MediatR;
using Microsoft.Extensions.Logging;
using AutoInstaller.UI.ViewModels.Base;
using AutoInstaller.Application.DTOs;
using AutoInstaller.Application.Modules.{{ModuleName}}.Commands;
using AutoInstaller.Application.Modules.{{ModuleName}}.Queries;
using AutoInstaller.Core.Modules.{{ModuleName}}.Entities;
using DynamicData;
using DynamicData.Binding;

namespace AutoInstaller.UI.ViewModels.{{ModuleName}};

/// <summary>
/// ViewModel para gerenciamento de {{EntityName}}
/// </summary>
public class {{EntityName}}ViewModel : ViewModelBase
{
    private readonly SourceList<{{EntityName}}Dto> _{{entityName}}Source = new();
    private readonly ReadOnlyObservableCollection<{{EntityName}}Dto> _{{entityName}}s;
    
    // Propriedades de estado
    private {{EntityName}}Dto? _selected{{EntityName}};
    private string _searchText = string.Empty;
    private {{EntityName}}Status? _statusFilter;
    private bool _isLoading;
    private bool _isRefreshing;
    private string? _errorMessage;
    private int _totalCount;
    
    // Propriedades de formulário
    private string _name = string.Empty;
    private string _description = string.Empty;
    private bool _isEditing;
    private bool _isCreating;
    
    public {{EntityName}}ViewModel(IMediator mediator, ILogger<{{EntityName}}ViewModel> logger) 
        : base(mediator, logger)
    {
        // Configurar coleção observável com filtros
        var filter = this.WhenAnyValue(
                x => x.SearchText,
                x => x.StatusFilter,
                (search, status) => new Func<{{EntityName}}Dto, bool>(item =>
                    (string.IsNullOrWhiteSpace(search) || 
                     item.Name.Contains(search, StringComparison.OrdinalIgnoreCase) ||
                     item.Description.Contains(search, StringComparison.OrdinalIgnoreCase)) &&
                    (!status.HasValue || item.Status == status.Value)))
            .DistinctUntilChanged();
        
        _{{entityName}}Source
            .Connect()
            .Filter(filter)
            .Sort(SortExpressionComparer<{{EntityName}}Dto>.Descending(x => x.CreatedAt))
            .ObserveOn(RxApp.MainThreadScheduler)
            .Bind(out _{{entityName}}s)
            .Subscribe()
            .DisposeWith(Disposables);
        
        // Inicializar comandos
        LoadCommand = ReactiveCommand.CreateFromTask(LoadAsync);
        RefreshCommand = ReactiveCommand.CreateFromTask(RefreshAsync);
        CreateCommand = ReactiveCommand.CreateFromTask(CreateAsync, CanCreate);
        UpdateCommand = ReactiveCommand.CreateFromTask(UpdateAsync, CanUpdate);
        DeleteCommand = ReactiveCommand.CreateFromTask<{{EntityName}}Dto>(DeleteAsync, CanDelete);
        ActivateCommand = ReactiveCommand.CreateFromTask<{{EntityName}}Dto>(ActivateAsync, CanActivate);
        DeactivateCommand = ReactiveCommand.CreateFromTask<{{EntityName}}Dto>(DeactivateAsync, CanDeactivate);
        
        StartCreateCommand = ReactiveCommand.Create(StartCreate);
        StartEditCommand = ReactiveCommand.Create<{{EntityName}}Dto>(StartEdit, CanStartEdit);
        CancelEditCommand = ReactiveCommand.Create(CancelEdit);
        
        SearchCommand = ReactiveCommand.Create<string>(Search);
        ClearSearchCommand = ReactiveCommand.Create(ClearSearch);
        
        // Configurar observáveis para comandos
        var isExecutingAny = Observable.CombineLatest(
            LoadCommand.IsExecuting,
            CreateCommand.IsExecuting,
            UpdateCommand.IsExecuting,
            DeleteCommand.IsExecuting,
            (load, create, update, delete) => load || create || update || delete);
        
        isExecutingAny
            .Subscribe(executing => IsLoading = executing)
            .DisposeWith(Disposables);
        
        RefreshCommand.IsExecuting
            .Subscribe(refreshing => IsRefreshing = refreshing)
            .DisposeWith(Disposables);
        
        // Configurar tratamento de erros
        var commandExceptions = Observable.Merge(
            LoadCommand.ThrownExceptions,
            CreateCommand.ThrownExceptions,
            UpdateCommand.ThrownExceptions,
            DeleteCommand.ThrownExceptions,
            RefreshCommand.ThrownExceptions);
        
        commandExceptions
            .Subscribe(HandleError)
            .DisposeWith(Disposables);
        
        // Auto-refresh a cada 30 segundos
        Observable.Timer(TimeSpan.Zero, TimeSpan.FromSeconds(30))
            .Where(_ => !IsLoading && !IsRefreshing)
            .InvokeCommand(RefreshCommand)
            .DisposeWith(Disposables);
    }
    
    #region Propriedades
    
    public ReadOnlyObservableCollection<{{EntityName}}Dto> {{EntityName}}s => _{{entityName}}s;
    
    public {{EntityName}}Dto? Selected{{EntityName}}
    {
        get => _selected{{EntityName}};
        set => this.RaiseAndSetIfChanged(ref _selected{{EntityName}}, value);
    }
    
    public string SearchText
    {
        get => _searchText;
        set => this.RaiseAndSetIfChanged(ref _searchText, value);
    }
    
    public {{EntityName}}Status? StatusFilter
    {
        get => _statusFilter;
        set => this.RaiseAndSetIfChanged(ref _statusFilter, value);
    }
    
    public bool IsLoading
    {
        get => _isLoading;
        set => this.RaiseAndSetIfChanged(ref _isLoading, value);
    }
    
    public bool IsRefreshing
    {
        get => _isRefreshing;
        set => this.RaiseAndSetIfChanged(ref _isRefreshing, value);
    }
    
    public string? ErrorMessage
    {
        get => _errorMessage;
        set => this.RaiseAndSetIfChanged(ref _errorMessage, value);
    }
    
    public int TotalCount
    {
        get => _totalCount;
        set => this.RaiseAndSetIfChanged(ref _totalCount, value);
    }
    
    public string Name
    {
        get => _name;
        set => this.RaiseAndSetIfChanged(ref _name, value);
    }
    
    public string Description
    {
        get => _description;
        set => this.RaiseAndSetIfChanged(ref _description, value);
    }
    
    public bool IsEditing
    {
        get => _isEditing;
        set => this.RaiseAndSetIfChanged(ref _isEditing, value);
    }
    
    public bool IsCreating
    {
        get => _isCreating;
        set => this.RaiseAndSetIfChanged(ref _isCreating, value);
    }
    
    public bool IsFormVisible => IsEditing || IsCreating;
    
    #endregion
    
    #region Comandos
    
    public ReactiveCommand<Unit, Unit> LoadCommand { get; }
    public ReactiveCommand<Unit, Unit> RefreshCommand { get; }
    public ReactiveCommand<Unit, Unit> CreateCommand { get; }
    public ReactiveCommand<Unit, Unit> UpdateCommand { get; }
    public ReactiveCommand<{{EntityName}}Dto, Unit> DeleteCommand { get; }
    public ReactiveCommand<{{EntityName}}Dto, Unit> ActivateCommand { get; }
    public ReactiveCommand<{{EntityName}}Dto, Unit> DeactivateCommand { get; }
    
    public ReactiveCommand<Unit, Unit> StartCreateCommand { get; }
    public ReactiveCommand<{{EntityName}}Dto, Unit> StartEditCommand { get; }
    public ReactiveCommand<Unit, Unit> CancelEditCommand { get; }
    
    public ReactiveCommand<string, Unit> SearchCommand { get; }
    public ReactiveCommand<Unit, Unit> ClearSearchCommand { get; }
    
    #endregion
    
    #region Observáveis para validação de comandos
    
    private IObservable<bool> CanCreate =>
        Observable.CombineLatest(
            this.WhenAnyValue(x => x.Name),
            this.WhenAnyValue(x => x.IsCreating),
            this.WhenAnyValue(x => x.IsLoading),
            (name, creating, loading) => 
                !string.IsNullOrWhiteSpace(name) && creating && !loading);
    
    private IObservable<bool> CanUpdate =>
        Observable.CombineLatest(
            this.WhenAnyValue(x => x.Name),
            this.WhenAnyValue(x => x.IsEditing),
            this.WhenAnyValue(x => x.Selected{{EntityName}}),
            this.WhenAnyValue(x => x.IsLoading),
            (name, editing, selected, loading) => 
                !string.IsNullOrWhiteSpace(name) && editing && selected != null && !loading);
    
    private IObservable<bool> CanDelete =>
        this.WhenAnyValue(x => x.IsLoading, loading => !loading);
    
    private IObservable<bool> CanActivate =>
        this.WhenAnyValue(x => x.IsLoading, loading => !loading);
    
    private IObservable<bool> CanDeactivate =>
        this.WhenAnyValue(x => x.IsLoading, loading => !loading);
    
    private IObservable<bool> CanStartEdit =>
        this.WhenAnyValue(
            x => x.IsLoading,
            x => x.IsEditing,
            x => x.IsCreating,
            (loading, editing, creating) => !loading && !editing && !creating);
    
    #endregion
    
    #region Métodos de comando
    
    protected override void OnActivated()
    {
        base.OnActivated();
        
        if (LoadCommand.CanExecute.FirstAsync().Wait())
        {
            LoadCommand.Execute().Subscribe();
        }
    }
    
    private async Task LoadAsync()
    {
        try
        {
            Logger.LogDebug("Carregando {{EntityName}}s");
            
            var query = new Get{{EntityName}}ListQuery();
            var result = await Mediator.Send(query);
            
            if (result.IsSuccess)
            {
                _{{entityName}}Source.Clear();
                _{{entityName}}Source.AddRange(result.Value);
                TotalCount = result.Value.Count();
                ErrorMessage = null;
                
                Logger.LogDebug("{{EntityName}}s carregados: {Count}", TotalCount);
            }
            else
            {
                ErrorMessage = result.Error;
                Logger.LogWarning("Erro ao carregar {{EntityName}}s: {Error}", result.Error);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao carregar {{EntityName}}s");
            ErrorMessage = "Erro ao carregar dados";
        }
    }
    
    private async Task RefreshAsync()
    {
        await LoadAsync();
    }
    
    private async Task CreateAsync()
    {
        try
        {
            Logger.LogDebug("Criando {{EntityName}}: {Name}", Name);
            
            var command = new Create{{EntityName}}Command
            {
                Name = Name,
                Description = Description,
                CreatedBy = "Current User" // TODO: Obter usuário atual
            };
            
            var result = await Mediator.Send(command);
            
            if (result.IsSuccess)
            {
                _{{entityName}}Source.Add(result.Value);
                TotalCount++;
                CancelEdit();
                ErrorMessage = null;
                
                Logger.LogInformation("{{EntityName}} criado: {Id}", result.Value.Id);
            }
            else
            {
                ErrorMessage = result.Error;
                Logger.LogWarning("Erro ao criar {{EntityName}}: {Error}", result.Error);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao criar {{EntityName}}");
            ErrorMessage = "Erro ao criar {{EntityName}}";
        }
    }
    
    private async Task UpdateAsync()
    {
        if (Selected{{EntityName}} == null) return;
        
        try
        {
            Logger.LogDebug("Atualizando {{EntityName}}: {Id}", Selected{{EntityName}}.Id);
            
            var command = new Update{{EntityName}}Command
            {
                Id = Selected{{EntityName}}.Id,
                Name = Name,
                Description = Description,
                UpdatedBy = "Current User" // TODO: Obter usuário atual
            };
            
            var result = await Mediator.Send(command);
            
            if (result.IsSuccess)
            {
                var index = _{{entityName}}Source.Items.ToList().FindIndex(x => x.Id == result.Value.Id);
                if (index >= 0)
                {
                    _{{entityName}}Source.ReplaceAt(index, result.Value);
                }
                
                Selected{{EntityName}} = result.Value;
                CancelEdit();
                ErrorMessage = null;
                
                Logger.LogInformation("{{EntityName}} atualizado: {Id}", result.Value.Id);
            }
            else
            {
                ErrorMessage = result.Error;
                Logger.LogWarning("Erro ao atualizar {{EntityName}}: {Error}", result.Error);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao atualizar {{EntityName}}");
            ErrorMessage = "Erro ao atualizar {{EntityName}}";
        }
    }
    
    private async Task DeleteAsync({{EntityName}}Dto {{entityName}})
    {
        try
        {
            Logger.LogDebug("Excluindo {{EntityName}}: {Id}", {{entityName}}.Id);
            
            var command = new Delete{{EntityName}}Command
            {
                Id = {{entityName}}.Id,
                DeletedBy = "Current User", // TODO: Obter usuário atual
                SoftDelete = true
            };
            
            var result = await Mediator.Send(command);
            
            if (result.IsSuccess)
            {
                _{{entityName}}Source.Remove({{entityName}});
                TotalCount--;
                
                if (Selected{{EntityName}}?.Id == {{entityName}}.Id)
                {
                    Selected{{EntityName}} = null;
                    CancelEdit();
                }
                
                ErrorMessage = null;
                Logger.LogInformation("{{EntityName}} excluído: {Id}", {{entityName}}.Id);
            }
            else
            {
                ErrorMessage = result.Error;
                Logger.LogWarning("Erro ao excluir {{EntityName}}: {Error}", result.Error);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao excluir {{EntityName}}");
            ErrorMessage = "Erro ao excluir {{EntityName}}";
        }
    }
    
    private async Task ActivateAsync({{EntityName}}Dto {{entityName}})
    {
        try
        {
            Logger.LogDebug("Ativando {{EntityName}}: {Id}", {{entityName}}.Id);
            
            var command = new Activate{{EntityName}}Command
            {
                Id = {{entityName}}.Id,
                ActivatedBy = "Current User" // TODO: Obter usuário atual
            };
            
            var result = await Mediator.Send(command);
            
            if (result.IsSuccess)
            {
                var index = _{{entityName}}Source.Items.ToList().FindIndex(x => x.Id == result.Value.Id);
                if (index >= 0)
                {
                    _{{entityName}}Source.ReplaceAt(index, result.Value);
                }
                
                ErrorMessage = null;
                Logger.LogInformation("{{EntityName}} ativado: {Id}", result.Value.Id);
            }
            else
            {
                ErrorMessage = result.Error;
                Logger.LogWarning("Erro ao ativar {{EntityName}}: {Error}", result.Error);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao ativar {{EntityName}}");
            ErrorMessage = "Erro ao ativar {{EntityName}}";
        }
    }
    
    private async Task DeactivateAsync({{EntityName}}Dto {{entityName}})
    {
        try
        {
            Logger.LogDebug("Desativando {{EntityName}}: {Id}", {{entityName}}.Id);
            
            var command = new Deactivate{{EntityName}}Command
            {
                Id = {{entityName}}.Id,
                DeactivatedBy = "Current User" // TODO: Obter usuário atual
            };
            
            var result = await Mediator.Send(command);
            
            if (result.IsSuccess)
            {
                var index = _{{entityName}}Source.Items.ToList().FindIndex(x => x.Id == result.Value.Id);
                if (index >= 0)
                {
                    _{{entityName}}Source.ReplaceAt(index, result.Value);
                }
                
                ErrorMessage = null;
                Logger.LogInformation("{{EntityName}} desativado: {Id}", result.Value.Id);
            }
            else
            {
                ErrorMessage = result.Error;
                Logger.LogWarning("Erro ao desativar {{EntityName}}: {Error}", result.Error);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao desativar {{EntityName}}");
            ErrorMessage = "Erro ao desativar {{EntityName}}";
        }
    }
    
    private void StartCreate()
    {
        ClearForm();
        IsCreating = true;
        IsEditing = false;
        Selected{{EntityName}} = null;
        
        Logger.LogDebug("Iniciando criação de {{EntityName}}");
    }
    
    private void StartEdit({{EntityName}}Dto {{entityName}})
    {
        Selected{{EntityName}} = {{entityName}};
        Name = {{entityName}}.Name;
        Description = {{entityName}}.Description;
        IsEditing = true;
        IsCreating = false;
        
        Logger.LogDebug("Iniciando edição de {{EntityName}}: {Id}", {{entityName}}.Id);
    }
    
    private void CancelEdit()
    {
        ClearForm();
        IsEditing = false;
        IsCreating = false;
        Selected{{EntityName}} = null;
        
        Logger.LogDebug("Cancelando edição/criação de {{EntityName}}");
    }
    
    private void Search(string searchTerm)
    {
        SearchText = searchTerm ?? string.Empty;
        Logger.LogDebug("Pesquisando {{EntityName}}s: {SearchTerm}", SearchText);
    }
    
    private void ClearSearch()
    {
        SearchText = string.Empty;
        StatusFilter = null;
        Logger.LogDebug("Limpando filtros de pesquisa");
    }
    
    private void ClearForm()
    {
        Name = string.Empty;
        Description = string.Empty;
        ErrorMessage = null;
    }
    
    private void HandleError(Exception exception)
    {
        Logger.LogError(exception, "Erro no {{EntityName}}ViewModel: {ErrorMessage}", exception.Message);
        ErrorMessage = exception.Message;
    }
    
    #endregion
    
    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            _{{entityName}}Source?.Dispose();
        }
        
        base.Dispose(disposing);
    }
}