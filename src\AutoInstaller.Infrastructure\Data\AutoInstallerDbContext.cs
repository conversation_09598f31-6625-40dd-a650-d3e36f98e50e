using AutoInstaller.Core.Common;
using AutoInstaller.Core.Entities;
using AutoInstaller.Core.Interfaces;
using AutoInstaller.Infrastructure.Data.Configurations;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Reflection;

namespace AutoInstaller.Infrastructure.Data;

/// <summary>
/// Contexto principal do banco de dados
/// </summary>
public class AutoInstallerDbContext : DbContext
{
    private readonly ILogger<AutoInstallerDbContext> _logger;

    /// <summary>
    /// Containers
    /// </summary>
    public DbSet<Container> Containers { get; set; } = null!;

    /// <summary>
    /// Construtor para migrations
    /// </summary>
    public AutoInstallerDbContext()
    {
        _logger = null!;
    }

    /// <summary>
    /// Construtor principal
    /// </summary>
    /// <param name="options">Opções do contexto</param>
    /// <param name="logger">Logger</param>
    public AutoInstallerDbContext(DbContextOptions<AutoInstallerDbContext> options, ILogger<AutoInstallerDbContext> logger)
        : base(options)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Configuração do modelo
    /// </summary>
    /// <param name="modelBuilder">Model builder</param>
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Aplicar todas as configurações do assembly atual
        modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());

        // Configurações globais
        ConfigureGlobalFilters(modelBuilder);
        ConfigureConventions(modelBuilder);
    }

    /// <summary>
    /// Configuração de filtros globais
    /// </summary>
    /// <param name="modelBuilder">Model builder</param>
    private static void ConfigureGlobalFilters(ModelBuilder modelBuilder)
    {
        // Filtro global para soft delete
        modelBuilder.Entity<Container>().HasQueryFilter(e => !e.IsDeleted);
    }

    /// <summary>
    /// Configuração de convenções
    /// </summary>
    /// <param name="modelBuilder">Model builder</param>
    private static void ConfigureConventions(ModelBuilder modelBuilder)
    {
        // Configurar precisão decimal padrão
        foreach (var property in modelBuilder.Model.GetEntityTypes()
            .SelectMany(t => t.GetProperties())
            .Where(p => p.ClrType == typeof(decimal) || p.ClrType == typeof(decimal?)))
        {
            property.SetColumnType("decimal(18,2)");
        }

        // Configurar tamanho padrão para strings
        foreach (var property in modelBuilder.Model.GetEntityTypes()
            .SelectMany(t => t.GetProperties())
            .Where(p => p.ClrType == typeof(string)))
        {
            if (property.GetMaxLength() == null)
            {
                property.SetMaxLength(255);
            }
        }
    }

    /// <summary>
    /// Configuração da conexão (para migrations)
    /// </summary>
    /// <param name="optionsBuilder">Options builder</param>
    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        if (!optionsBuilder.IsConfigured)
        {
            // Configuração padrão para migrations
            optionsBuilder.UseSqlite("Data Source=autoinstaller.db");
            optionsBuilder.EnableSensitiveDataLogging(false);
            optionsBuilder.EnableDetailedErrors(false);
        }
    }

    /// <summary>
    /// Salva as alterações e processa eventos de domínio
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Número de entidades afetadas</returns>
    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            // Atualizar timestamps de auditoria
            UpdateAuditFields();

            // Salvar alterações
            var result = await base.SaveChangesAsync(cancellationToken);

            // Processar eventos de domínio após salvar
            await ProcessDomainEventsAsync(cancellationToken);

            return result;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Erro ao salvar alterações no banco de dados");
            throw;
        }
    }

    /// <summary>
    /// Atualiza campos de auditoria
    /// </summary>
    private void UpdateAuditFields()
    {
        var entries = ChangeTracker.Entries()
            .Where(e => e.Entity is BaseEntity && (e.State == EntityState.Added || e.State == EntityState.Modified));

        foreach (var entry in entries)
        {
            var entity = (BaseEntity)entry.Entity;

            if (entry.State == EntityState.Added)
            {
                // Para entidades novas, CreatedAt e CreatedBy já devem estar definidos
                if (entity.CreatedAt == default)
                {
                    entry.Property(nameof(BaseEntity.CreatedAt)).CurrentValue = DateTime.UtcNow;
                }
            }
            else if (entry.State == EntityState.Modified)
            {
                // Para entidades modificadas, atualizar UpdatedAt
                entry.Property(nameof(BaseEntity.UpdatedAt)).CurrentValue = DateTime.UtcNow;
                
                // Não permitir alteração de CreatedAt e CreatedBy
                entry.Property(nameof(BaseEntity.CreatedAt)).IsModified = false;
                entry.Property(nameof(BaseEntity.CreatedBy)).IsModified = false;
            }
        }
    }

    /// <summary>
    /// Processa eventos de domínio
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    private async Task ProcessDomainEventsAsync(CancellationToken cancellationToken)
    {
        var domainEntities = ChangeTracker.Entries<BaseEntity>()
            .Where(x => x.Entity.DomainEvents.Any())
            .Select(x => x.Entity)
            .ToList();

        var domainEvents = domainEntities
            .SelectMany(x => x.DomainEvents)
            .ToList();

        // Limpar eventos das entidades
        domainEntities.ForEach(entity => entity.ClearDomainEvents());

        // Aqui você pode publicar os eventos usando MediatR ou outro mecanismo
        // Por enquanto, apenas logamos
        foreach (var domainEvent in domainEvents)
        {
            _logger?.LogDebug("Evento de domínio processado: {EventType} - {EventId}", 
                domainEvent.EventType, domainEvent.EventId);
        }

        await Task.CompletedTask;
    }

    /// <summary>
    /// Obtém entidades incluindo as excluídas (soft delete)
    /// </summary>
    /// <typeparam name="T">Tipo da entidade</typeparam>
    /// <returns>DbSet sem filtro de soft delete</returns>
    public IQueryable<T> GetAllIncludingDeleted<T>() where T : BaseEntity
    {
        return Set<T>().IgnoreQueryFilters();
    }

    /// <summary>
    /// Obtém apenas entidades excluídas (soft delete)
    /// </summary>
    /// <typeparam name="T">Tipo da entidade</typeparam>
    /// <returns>DbSet apenas com entidades excluídas</returns>
    public IQueryable<T> GetDeleted<T>() where T : BaseEntity
    {
        return Set<T>().IgnoreQueryFilters().Where(e => e.IsDeleted);
    }

    /// <summary>
    /// Restaura uma entidade excluída
    /// </summary>
    /// <typeparam name="T">Tipo da entidade</typeparam>
    /// <param name="id">ID da entidade</param>
    /// <param name="restoredBy">Usuário que restaurou</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se restaurada com sucesso</returns>
    public async Task<bool> RestoreEntityAsync<T>(Guid id, string restoredBy, CancellationToken cancellationToken = default) 
        where T : BaseEntity
    {
        var entity = await GetDeleted<T>().FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
        
        if (entity == null)
            return false;

        entity.Restore(restoredBy);
        await SaveChangesAsync(cancellationToken);
        
        return true;
    }

    /// <summary>
    /// Remove permanentemente uma entidade
    /// </summary>
    /// <typeparam name="T">Tipo da entidade</typeparam>
    /// <param name="id">ID da entidade</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se removida com sucesso</returns>
    public async Task<bool> HardDeleteEntityAsync<T>(Guid id, CancellationToken cancellationToken = default) 
        where T : BaseEntity
    {
        var entity = await GetAllIncludingDeleted<T>().FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
        
        if (entity == null)
            return false;

        Set<T>().Remove(entity);
        await SaveChangesAsync(cancellationToken);
        
        return true;
    }

    /// <summary>
    /// Executa uma operação em uma transação
    /// </summary>
    /// <typeparam name="T">Tipo do resultado</typeparam>
    /// <param name="operation">Operação a ser executada</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Resultado da operação</returns>
    public async Task<T> ExecuteInTransactionAsync<T>(Func<Task<T>> operation, CancellationToken cancellationToken = default)
    {
        using var transaction = await Database.BeginTransactionAsync(cancellationToken);
        
        try
        {
            var result = await operation();
            await transaction.CommitAsync(cancellationToken);
            return result;
        }
        catch
        {
            await transaction.RollbackAsync(cancellationToken);
            throw;
        }
    }

    /// <summary>
    /// Executa uma operação em uma transação (sem retorno)
    /// </summary>
    /// <param name="operation">Operação a ser executada</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    public async Task ExecuteInTransactionAsync(Func<Task> operation, CancellationToken cancellationToken = default)
    {
        using var transaction = await Database.BeginTransactionAsync(cancellationToken);
        
        try
        {
            await operation();
            await transaction.CommitAsync(cancellationToken);
        }
        catch
        {
            await transaction.RollbackAsync(cancellationToken);
            throw;
        }
    }
}
