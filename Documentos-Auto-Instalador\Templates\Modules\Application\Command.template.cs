using MediatR;
using AutoInstaller.Application.Common;
using AutoInstaller.Application.DTOs;
using System.ComponentModel.DataAnnotations;

namespace AutoInstaller.Application.Modules.{{ModuleName}}.Commands;

/// <summary>
/// Command para criar um novo {{EntityName}}
/// </summary>
public record Create{{EntityName}}Command : IRequest<Result<{{EntityName}}Dto>>
{
    [Required(ErrorMessage = "Nome é obrigatório")]
    [StringLength(100, MinimumLength = 2, ErrorMessage = "Nome deve ter entre 2 e 100 caracteres")]
    public string Name { get; init; } = string.Empty;
    
    [StringLength(500, ErrorMessage = "Descrição não pode exceder 500 caracteres")]
    public string? Description { get; init; }
    
    [Required(ErrorMessage = "Usuário criador é obrigatório")]
    public string CreatedBy { get; init; } = string.Empty;
    
    // Propriedades adicionais específicas do {{EntityName}}
    // TODO: Adicionar propriedades específicas conforme necessário
}

/// <summary>
/// Command para atualizar um {{EntityName}} existente
/// </summary>
public record Update{{EntityName}}Command : IRequest<Result<{{EntityName}}Dto>>
{
    [Required(ErrorMessage = "ID é obrigatório")]
    public Guid Id { get; init; }
    
    [Required(ErrorMessage = "Nome é obrigatório")]
    [StringLength(100, MinimumLength = 2, ErrorMessage = "Nome deve ter entre 2 e 100 caracteres")]
    public string Name { get; init; } = string.Empty;
    
    [StringLength(500, ErrorMessage = "Descrição não pode exceder 500 caracteres")]
    public string? Description { get; init; }
    
    [Required(ErrorMessage = "Usuário que atualizou é obrigatório")]
    public string UpdatedBy { get; init; } = string.Empty;
    
    // Propriedades adicionais específicas do {{EntityName}}
    // TODO: Adicionar propriedades específicas conforme necessário
}

/// <summary>
/// Command para ativar um {{EntityName}}
/// </summary>
public record Activate{{EntityName}}Command : IRequest<Result<{{EntityName}}Dto>>
{
    [Required(ErrorMessage = "ID é obrigatório")]
    public Guid Id { get; init; }
    
    [Required(ErrorMessage = "Usuário que ativou é obrigatório")]
    public string ActivatedBy { get; init; } = string.Empty;
}

/// <summary>
/// Command para desativar um {{EntityName}}
/// </summary>
public record Deactivate{{EntityName}}Command : IRequest<Result<{{EntityName}}Dto>>
{
    [Required(ErrorMessage = "ID é obrigatório")]
    public Guid Id { get; init; }
    
    [Required(ErrorMessage = "Usuário que desativou é obrigatório")]
    public string DeactivatedBy { get; init; } = string.Empty;
}

/// <summary>
/// Command para excluir um {{EntityName}}
/// </summary>
public record Delete{{EntityName}}Command : IRequest<Result<bool>>
{
    [Required(ErrorMessage = "ID é obrigatório")]
    public Guid Id { get; init; }
    
    [Required(ErrorMessage = "Usuário que excluiu é obrigatório")]
    public string DeletedBy { get; init; } = string.Empty;
    
    public bool SoftDelete { get; init; } = true;
}

/// <summary>
/// Command para operações em lote de {{EntityName}}
/// </summary>
public record Bulk{{EntityName}}Command : IRequest<Result<BulkOperationResult>>
{
    [Required(ErrorMessage = "IDs são obrigatórios")]
    [MinLength(1, ErrorMessage = "Pelo menos um ID deve ser fornecido")]
    public IEnumerable<Guid> Ids { get; init; } = Enumerable.Empty<Guid>();
    
    [Required(ErrorMessage = "Operação é obrigatória")]
    public BulkOperation Operation { get; init; }
    
    [Required(ErrorMessage = "Usuário é obrigatório")]
    public string PerformedBy { get; init; } = string.Empty;
    
    public object? OperationData { get; init; }
}

/// <summary>
/// Enum para tipos de operações em lote
/// </summary>
public enum BulkOperation
{
    Activate,
    Deactivate,
    Delete,
    UpdateStatus,
    Archive
}

/// <summary>
/// Resultado de operação em lote
/// </summary>
public class BulkOperationResult
{
    public int TotalRequested { get; set; }
    public int SuccessCount { get; set; }
    public int FailureCount { get; set; }
    public IEnumerable<BulkOperationError> Errors { get; set; } = Enumerable.Empty<BulkOperationError>();
    public TimeSpan Duration { get; set; }
    public DateTime CompletedAt { get; set; }
    
    public bool IsSuccess => FailureCount == 0;
    public double SuccessRate => TotalRequested > 0 ? (double)SuccessCount / TotalRequested * 100 : 0;
}

/// <summary>
/// Erro em operação em lote
/// </summary>
public class BulkOperationError
{
    public Guid Id { get; set; }
    public string ErrorMessage { get; set; } = string.Empty;
    public string? ErrorCode { get; set; }
    public Exception? Exception { get; set; }
}