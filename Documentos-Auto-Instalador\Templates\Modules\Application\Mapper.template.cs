using AutoMapper;
using {{RootNamespace}}.Core.Modules.{{ModuleName}}.Entities;
using {{RootNamespace}}.Application.Modules.{{ModuleName}}.Commands;
using {{RootNamespace}}.Application.Modules.{{ModuleName}}.DTOs;
using {{RootNamespace}}.Application.Modules.{{ModuleName}}.Queries;

namespace {{RootNamespace}}.Application.Modules.{{ModuleName}}.Mappers;

/// <summary>
/// Profile de mapeamento para {{EntityName}}
/// </summary>
public class {{EntityName}}MappingProfile : Profile
{
    public {{EntityName}}MappingProfile()
    {
        ConfigureEntityMappings();
        ConfigureCommandMappings();
        ConfigureQueryMappings();
    }

    /// <summary>
    /// Configura mapeamentos da entidade
    /// </summary>
    private void ConfigureEntityMappings()
    {
        // Entity -> DTO
        CreateMap<{{EntityName}}, {{EntityName}}Dto>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
            .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.Description))
            .ForMember(dest => dest.IsActive, opt => opt.MapFrom(src => src.IsActive))
            .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => src.CreatedAt))
            .ForMember(dest => dest.CreatedBy, opt => opt.MapFrom(src => src.CreatedBy))
            .ForMember(dest => dest.ModifiedAt, opt => opt.MapFrom(src => src.ModifiedAt))
            .ForMember(dest => dest.ModifiedBy, opt => opt.MapFrom(src => src.ModifiedBy));

        // DTO -> Entity (para criação)
        CreateMap<{{EntityName}}Dto, {{EntityName}}>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
            .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.Description))
            .ForMember(dest => dest.IsActive, opt => opt.MapFrom(src => src.IsActive))
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.ModifiedAt, opt => opt.Ignore())
            .ForMember(dest => dest.ModifiedBy, opt => opt.Ignore());

        // Entity -> Summary DTO (para listas)
        CreateMap<{{EntityName}}, {{EntityName}}SummaryDto>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
            .ForMember(dest => dest.IsActive, opt => opt.MapFrom(src => src.IsActive))
            .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => src.CreatedAt));
    }

    /// <summary>
    /// Configura mapeamentos de comandos
    /// </summary>
    private void ConfigureCommandMappings()
    {
        // Create Command -> Entity
        CreateMap<Create{{EntityName}}Command, {{EntityName}}>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
            .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.Description))
            .ForMember(dest => dest.IsActive, opt => opt.MapFrom(src => true))
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedBy, opt => opt.MapFrom(src => src.CreatedBy))
            .ForMember(dest => dest.ModifiedAt, opt => opt.Ignore())
            .ForMember(dest => dest.ModifiedBy, opt => opt.Ignore());

        // Update Command -> Entity (para atualização)
        CreateMap<Update{{EntityName}}Command, {{EntityName}}>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
            .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.Description))
            .ForMember(dest => dest.IsActive, opt => opt.MapFrom(src => src.IsActive))
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.ModifiedAt, opt => opt.Ignore())
            .ForMember(dest => dest.ModifiedBy, opt => opt.MapFrom(src => src.ModifiedBy));

        // Create Command -> DTO
        CreateMap<Create{{EntityName}}Command, {{EntityName}}Dto>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
            .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.Description))
            .ForMember(dest => dest.IsActive, opt => opt.MapFrom(src => true))
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedBy, opt => opt.MapFrom(src => src.CreatedBy))
            .ForMember(dest => dest.ModifiedAt, opt => opt.Ignore())
            .ForMember(dest => dest.ModifiedBy, opt => opt.Ignore());
    }

    /// <summary>
    /// Configura mapeamentos de queries
    /// </summary>
    private void ConfigureQueryMappings()
    {
        // Query parameters para filtros
        CreateMap<Get{{PluralEntityName}}Query, {{EntityName}}FilterDto>()
            .ForMember(dest => dest.SearchTerm, opt => opt.MapFrom(src => src.SearchTerm))
            .ForMember(dest => dest.PageNumber, opt => opt.MapFrom(src => src.PageNumber))
            .ForMember(dest => dest.PageSize, opt => opt.MapFrom(src => src.PageSize))
            .ForMember(dest => dest.SortBy, opt => opt.MapFrom(src => src.SortBy))
            .ForMember(dest => dest.SortDescending, opt => opt.MapFrom(src => src.SortDescending));

        // Criteria Query para filtros específicos
        CreateMap<Get{{PluralEntityName}}ByCriteriaQuery, {{EntityName}}CriteriaDto>()
            .ForMember(dest => dest.Criteria, opt => opt.MapFrom(src => src.Criteria))
            .ForMember(dest => dest.Value, opt => opt.MapFrom(src => src.Value))
            .ForMember(dest => dest.PageNumber, opt => opt.MapFrom(src => src.PageNumber))
            .ForMember(dest => dest.PageSize, opt => opt.MapFrom(src => src.PageSize));
    }
}

/// <summary>
/// Extensões para mapeamento manual quando necessário
/// </summary>
public static class {{EntityName}}MappingExtensions
{
    /// <summary>
    /// Mapeia Entity para DTO com configurações customizadas
    /// </summary>
    /// <param name="entity">Entidade a ser mapeada</param>
    /// <param name="includeDetails">Se deve incluir detalhes completos</param>
    /// <returns>DTO mapeado</returns>
    public static {{EntityName}}Dto ToDto(this {{EntityName}} entity, bool includeDetails = true)
    {
        if (entity == null) return null;

        var dto = new {{EntityName}}Dto
        {
            Id = entity.Id,
            Name = entity.Name,
            Description = includeDetails ? entity.Description : null,
            IsActive = entity.IsActive,
            CreatedAt = entity.CreatedAt,
            CreatedBy = entity.CreatedBy,
            ModifiedAt = entity.ModifiedAt,
            ModifiedBy = entity.ModifiedBy
        };

        // Adicionar mapeamentos customizados aqui se necessário
        
        return dto;
    }

    /// <summary>
    /// Mapeia lista de Entities para lista de DTOs
    /// </summary>
    /// <param name="entities">Lista de entidades</param>
    /// <param name="includeDetails">Se deve incluir detalhes completos</param>
    /// <returns>Lista de DTOs</returns>
    public static IEnumerable<{{EntityName}}Dto> ToDtoList(this IEnumerable<{{EntityName}}> entities, bool includeDetails = false)
    {
        return entities?.Select(e => e.ToDto(includeDetails)) ?? Enumerable.Empty<{{EntityName}}Dto>();
    }

    /// <summary>
    /// Mapeia Entity para Summary DTO
    /// </summary>
    /// <param name="entity">Entidade a ser mapeada</param>
    /// <returns>Summary DTO</returns>
    public static {{EntityName}}SummaryDto ToSummaryDto(this {{EntityName}} entity)
    {
        if (entity == null) return null;

        return new {{EntityName}}SummaryDto
        {
            Id = entity.Id,
            Name = entity.Name,
            IsActive = entity.IsActive,
            CreatedAt = entity.CreatedAt
        };
    }

    /// <summary>
    /// Atualiza entidade existente com dados do comando de atualização
    /// </summary>
    /// <param name="entity">Entidade a ser atualizada</param>
    /// <param name="command">Comando com novos dados</param>
    /// <returns>Entidade atualizada</returns>
    public static {{EntityName}} UpdateFromCommand(this {{EntityName}} entity, Update{{EntityName}}Command command)
    {
        if (entity == null || command == null) return entity;

        entity.Name = command.Name;
        entity.Description = command.Description;
        entity.IsActive = command.IsActive;
        entity.ModifiedBy = command.ModifiedBy;
        entity.ModifiedAt = DateTime.UtcNow;

        return entity;
    }
}